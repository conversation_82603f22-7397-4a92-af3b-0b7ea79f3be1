---
inclusion: fileMatch  
fileMatchPattern: '*feedback*|*tampermonkey*|*lambda*'
---

# Feedback System Development Guide

## System Architecture

### Components Overview
- **Lambda Function**: Generates S3 presigned URLs for secure file uploads
- **API Gateway**: HTTP API endpoint for Lambda function access
- **S3 Storage**: Organized feedback data storage with timestamp hierarchy
- **Tampermonkey Script**: Frontend UI injection and user interaction handling

### Security Model
- **CORS Restriction**: Only `https://issues.cn-northwest-1.amazonaws.cn` allowed
- **Presigned URLs**: 5-minute expiration for secure uploads
- **IAM Permissions**: Minimal S3 PutObject permissions only
- **Input Validation**: Sanitize and validate all user inputs

## Lambda Function Implementation

### Environment Configuration
```python
S3_BUCKET_NAME = os.environ['S3_BUCKET_NAME']
ALLOWED_ORIGIN = os.environ.get('ALLOWED_ORIGIN', '*')
```

### File Naming Convention
```
feedback/YYYY/MM/DD/HH-MM-SS-{uuid}.json
```

### Response Format
```json
{
    "uploadURL": "https://s3-presigned-url...",
    "objectKey": "feedback/2024/07/15/14-30-45-abc123def456.json"
}
```

### Error Handling Requirements
- Log all errors to CloudWatch with detailed context
- Return standardized error responses with CORS headers
- Handle both success and failure cases consistently
- Provide meaningful error messages for debugging

### CORS Implementation
```python
'headers': {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': ALLOWED_ORIGIN
}
```

## Tampermonkey Script Development

### Framework Integration
基于 `.kiro/specs/tampermonkey-framework/` 的通用框架，反馈系统脚本现在使用统一的页面内容检测和模块化架构。

### Script Configuration
```javascript
// @match        https://issues.cn-northwest-1.amazonaws.cn/issues/*
// @grant        GM_xmlhttpRequest
// @connect      qek0wlrzwb.execute-api.cn-north-1.amazonaws.com.cn
// @connect      lianhe-mass-email-feedback-test.s3.cn-north-1.amazonaws.com.cn
// @require      https://cdn.example.com/tampermonkey-framework.min.js
```

### Framework-Based Implementation
```javascript
// 初始化框架
const tampermonkey = new TampermonkeyFramework({
    contentDetection: {
        targets: ['CN Mass Email'],
        strategies: ['textContent', 'innerText', 'visibleText'],
        maxAttempts: 10,
        checkInterval: 1000,
        initialDelay: 2000,
        timeout: 30000,
        dynamicDetection: true
    },
    debug: {
        enabled: true,
        logLevel: 'info'
    },
    coordination: {
        namespace: 'awsMassEmailFeedback',
        shareDetectionResults: true
    }
});

// 注册反馈模块
tampermonkey.registerModule('feedback', {
    activate: function(detectionResult) {
        console.log('[Feedback Module] 检测到 CN Mass Email 内容，初始化反馈系统');
        this.createMainFeedbackButton();
        return Promise.resolve();
    },
    
    createMainFeedbackButton: function() {
        // 创建反馈按钮实现
        // ...
    },
    
    showFeedbackModal: function() {
        // 显示反馈模态框实现
        // ...
    },
    
    submitFeedback: function(data) {
        // 提交反馈数据实现
        // ...
    }
});

// 启动框架
tampermonkey.start().then(result => {
    if (result) {
        console.log('[Feedback Script] 反馈系统已激活');
    } else {
        console.log('[Feedback Script] 未检测到相关内容，反馈系统未激活');
    }
});
```

### Page Content Detection Strategy (Framework-Based)
- **Framework Integration**: 使用统一的内容检测引擎
- **Multiple Strategies**: 支持 textContent、innerText、visibleText、selector、regex 等多种检测策略
- **Dynamic Content**: 内置 MutationObserver 支持动态内容检测
- **Performance Optimization**: 自动防抖、节流和资源管理
- **Error Handling**: 完善的错误处理和恢复机制
- **Multi-script Coordination**: 支持多脚本协调和检测结果共享

### Legacy Content Detection (For Reference)
```javascript
// 传统检测方法（已被框架替代）
function checkPageContainsMassEmail() {
    const pageText = document.body.innerText || document.body.textContent || '';
    return pageText.includes('CN Mass Email');
}

function waitForPageContentAndInitialize() {
    let attempts = 0;
    const maxAttempts = 10;
    const checkInterval = 1000;
    
    const checkContent = () => {
        attempts++;
        
        if (checkPageContainsMassEmail()) {
            console.log('[Feedback Script] 检测到 CN Mass Email 内容，初始化反馈按钮');
            createMainFeedbackButton();
            return;
        }
        
        if (attempts < maxAttempts) {
            setTimeout(checkContent, checkInterval);
        } else {
            console.log('[Feedback Script] 未检测到 CN Mass Email 内容，脚本不会激活');
        }
    };
    
    checkContent();
}
```

### UI Injection Strategy
- **Timing**: 2-second delay after page load, then content detection
- **Button Placement**: Fixed position bottom-right
- **Modal System**: Custom overlay with form elements
- **Styling**: Inline CSS for compatibility
- **Conditional Activation**: Only show UI when "CN Mass Email" text is detected

### Feedback Data Structure
```javascript
{
    mass_email_page_url: window.location.href,
    satisfaction: "satisfied" | "unsatisfied", 
    comments: "user input text",
    submitted_at_iso: "2024-07-15T14:30:45.123Z",
    user_agent: navigator.userAgent
}
```

### Debug Logging System
- Real-time debug log display in modal
- Console logging for developer tools
- Timestamp-prefixed log entries
- Auto-scroll to latest entries
- Network request/response logging

### Error Handling Flow
1. **API Call Failures**: Display network error messages
2. **JSON Parsing Issues**: Handle double-encoded responses
3. **Upload Failures**: Retry logic and user feedback
4. **CORS Issues**: Clear error messaging
5. **Timeout Handling**: Graceful degradation

## API Integration Patterns

### Request Flow
1. User clicks feedback button
2. Modal displays with form
3. User submits feedback
4. Script calls Lambda API for presigned URL
5. Script uploads JSON directly to S3
6. Success/failure feedback to user

### Response Handling
```javascript
// Handle potential double-encoded JSON from API Gateway
if (typeof responseData === 'string') {
    responseData = JSON.parse(responseData);
}
```

### Cross-Domain Request Configuration
```javascript
GM_xmlhttpRequest({
    method: 'GET',
    url: CONFIG.API_ENDPOINT_GENERATE_URL,
    onload: handleResponse,
    onerror: handleError
});
```

## Deployment and Testing

### Lambda Deployment Commands
```bash
cd lambda/feedback_test
zip -r feedback_lambda.zip .
aws lambda update-function-code \
  --function-name mass-email-feedback \
  --zip-file fileb://feedback_lambda.zip \
  --region cn-northwest-1
```

### Local Testing
```bash
cd lambda/feedback_test
python -c "
import json
from lianhe_mass_email_feedback_test import lambda_handler
with open('lambda_test_event/feedback_test_lambda_test_event.json', 'r') as f:
    event = json.load(f)
result = lambda_handler(event, None)
print(json.dumps(result, indent=2, ensure_ascii=False))
"
```

### Monitoring Commands
```bash
# View CloudWatch logs
aws logs describe-log-groups \
  --log-group-name-prefix "/aws/lambda/mass-email-feedback" \
  --region cn-northwest-1

# Tail logs in real-time  
aws logs tail /aws/lambda/mass-email-feedback \
  --follow --region cn-northwest-1
```

## Quality Assurance

### Testing Checklist
- [ ] CORS headers present in all responses
- [ ] Presigned URL generation and expiration
- [ ] S3 file upload with correct naming
- [ ] Error handling for all failure scenarios
- [ ] UI injection timing and compatibility
- [ ] Debug logging functionality
- [ ] Cross-browser compatibility
- [ ] Network timeout handling

### Security Validation
- [ ] CORS origin restriction enforced
- [ ] IAM permissions minimal and correct
- [ ] Input sanitization implemented
- [ ] No sensitive data in logs
- [ ] Presigned URL expiration working
- [ ] HTTPS-only communication

### Performance Considerations
- [ ] Lambda cold start optimization
- [ ] S3 upload performance
- [ ] UI injection minimal impact
- [ ] Memory usage optimization
- [ ] Error recovery efficiency

## Common Issues and Solutions

### Double-Encoded JSON Responses
API Gateway may double-encode JSON responses. Handle with:
```javascript
if (typeof responseData === 'string') {
    responseData = JSON.parse(responseData);
}
```

### CORS Preflight Issues
Ensure Lambda returns CORS headers for both success and error responses.

### Tampermonkey Permission Issues
Verify `@connect` directives include all required domains.

### S3 Upload Failures
Check presigned URL format, expiration, and CORS configuration on bucket.

### Page Content Detection Issues
Common problems with content detection and solutions:

#### Content Not Detected on Dynamic Pages
```javascript
// Problem: AJAX-loaded content not detected
// Solution: Implement MutationObserver with retry mechanism
const observer = new MutationObserver((mutations) => {
    if (checkPageContainsMassEmail()) {
        createMainFeedbackButton();
        observer.disconnect();
    }
});
observer.observe(document.body, { childList: true, subtree: true });
```

#### Performance Impact from Content Detection
```javascript
// Problem: Frequent DOM scanning affects performance
// Solution: Use debounced detection with performance limits
let debounceTimer;
function debouncedContentCheck() {
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(() => {
        if (performance.now() - startTime < 30000) { // 30s limit
            checkPageContainsMassEmail();
        }
    }, 500);
}
```

#### False Positives in Content Detection
```javascript
// Problem: Script activates on wrong pages
// Solution: More specific text matching and context validation
function validateMassEmailContext() {
    const pageText = document.body.textContent;
    return pageText.includes('CN Mass Email') && 
           (pageText.includes('translation') || pageText.includes('处理'));
}
```

### Debug Information Access
Always provide detailed logging for troubleshooting user issues, including content detection status.