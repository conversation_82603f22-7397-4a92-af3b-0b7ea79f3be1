# AWS中国区服务名称同步系统实施计划

## 实施任务清单

- [x] 1. 设置项目基础结构

  - 创建Lambda函数项目目录结构
  - 设置Python虚拟环境和依赖管理
  - 配置开发环境和测试框架
  - _需求: 1.1, 6.1_

- [x] 2. 实现网页爬虫模块

  - [x] 2.1 创建网页爬虫基础框架

    - 实现HTTP请求和错误处理
    - 添加请求重试和超时机制
    - 实现User-Agent轮换功能
    - _需求: 1.1, 1.3, 5.2_

  - [x] 2.2 实现AWS中国区服务页面解析器


    - 编写HTML解析逻辑提取服务名称
    - 实现服务全称和简称的提取
    - 添加服务类别和区域可用性解析
    - _需求: 1.1, 2.1, 2.2_

  - [x] 2.3 实现网页爬虫异常处理和日志记录


    - 添加详细的错误处理和日志记录
    - 实现网络错误和解析错误的处理策略
    - _需求: 1.3, 5.1, 5.2_

- [x] 3. 实现PDF解析模块


  - [x] 3.1 创建S3 PDF文件获取功能

    - 更新PDFParser类使用ConfigManager获取PDF S3路径配置
    - 实现基于Secret Manager配置的S3客户端和文件下载功能
    - 添加S3路径解析逻辑（从s3://bucket/key格式提取bucket和key）
    - 添加文件完整性验证和临时文件管理
    - _需求: 1.2, 6.2_

  - [x] 3.2 实现PDF内容解析功能


    - 编写PDF文本和表格提取逻辑
    - 实现服务名称和元数据提取
    - 添加结构化数据转换功能
    - _需求: 1.2, 2.1, 2.2_


  - [x] 3.3 实现PDF解析异常处理和日志记录


    - 添加PDF解析错误处理
    - 实现格式异常和内容缺失的处理策略
    - _需求: 1.4, 5.1, 5.2_

- [x] 4. 实现数据处理和标准化模块


  - [x] 4.1 创建服务数据模型和验证器


    - 定义包含authoritative_full_name、base_name和internal_name字段的ServiceInfo数据类
    - 实现authoritative_full_name作为业务主键的数据验证逻辑
    - 添加base_name推导逻辑（移除括号内缩写）
    - 实现字段职责分离：authoritative_full_name负责数据同步、base_name负责翻译逻辑状态跟踪
    - _需求: 2.1, 2.2, 2.5, 4.3, 4.12_

  - [x] 4.2 实现数据合并和去重功能


    - 编写多数据源结果合并逻辑
    - 实现服务名称去重算法
    - 添加数据一致性检查
    - _需求: 2.2, 2.3, 2.4_

  - [x] 4.3 实现数据变更检测和历史记录


    - 编写服务名称变更检测逻辑
    - 实现变更历史记录功能
    - 添加变更统计和报告生成
    - _需求: 2.4, 4.2, 4.3_

- [x] 5. 实现RDS数据库存储模块

  - [x] 5.1 创建配置管理和Secret Manager集成功能

    - 实现ConfigManager类，集成AWS Secrets Manager获取敏感配置
    - 配置Lambda环境变量SECRETS_MANAGER_ARN指向arn:aws-cn:secretsmanager:cn-northwest-1:793374724522:secret:dev/mass_email_translator-xZP5S7
    - 实现从Secret Manager获取数据库连接信息（host、port、username、password、database）
    - 实现从Secret Manager获取PDF S3路径配置（snpdf字段）
    - 添加配置缓存机制和错误处理
    - _需求: 4.1, 6.2, 6.4_

  - [x] 5.2 创建数据库连接和管理功能

    - 更新RDSClient类使用ConfigManager获取数据库连接信息
    - 实现基于Secret Manager配置的数据库连接池管理
    - 添加连接错误处理和重试机制
    - 实现安全的连接字符串生成逻辑
    - _需求: 4.1, 6.2, 6.4_

  - [x] 5.3 实现数据库表创建和迁移功能
    - 编写v2版本表结构创建SQL脚本，包含authoritative_full_name作为唯一业务键
    - 实现从旧表结构到新表结构的数据迁移逻辑，确保字段职责分离
    - 添加base_name字段的数据推导和填充逻辑
    - 实现版本控制和回滚功能，支持安全的表结构升级
    - _需求: 4.1, 4.2, 4.9, 4.12_

  - [x] 5.4 实现服务数据CRUD操作
    - 编写基于authoritative_full_name作为业务主键的UPSERT操作（ON CONFLICT处理）
    - 实现服务查询功能，支持按base_name（翻译逻辑）和authoritative_full_name（同步逻辑）查询
    - 添加批量操作和事务管理，确保数据一致性
    - 实现字段职责分离的数据处理：full_name_en提供首次替换值、short_name_en提供后续替换值
    - _需求: 4.1, 4.2, 4.3, 4.12_

  - [x] 5.5 实现同步历史记录功能
    - 编写同步执行记录存储功能
    - 实现统计信息收集和存储
    - 添加历史数据查询功能
    - _需求: 4.2, 4.3, 5.4_

- [x] 6. 实现Lambda处理程序和调度功能

  - [x] 6.1 创建Lambda处理程序入口

    - 实现主处理流程和组件协调
    - 添加环境变量配置管理
    - 实现初始化和清理逻辑
    - _需求: 3.1, 3.2_

  - [x] 6.2 实现错误处理和重试机制

    - 编写全局错误处理逻辑
    - 实现分级重试策略
    - 添加超时和资源限制处理
    - _需求: 3.3, 5.1, 5.2_

  - [x] 6.3 实现增强日志输出功能

    - 编写结构化日志记录功能，包含关键执行指标
    - 实现统一的日志格式和级别管理
    - 添加执行统计信息的日志输出（处理数量、成功率、执行时间等）
    - 实现关键错误和异常的详细日志记录
    - _需求: 3.4, 5.1, 5.3_

  - [x] 6.4 实现执行状态和指标日志输出

    - 编写执行开始、进度和完成状态的日志记录
    - 实现数据同步统计信息的结构化日志输出
    - 添加性能指标和资源使用情况的日志记录
    - 实现便于CloudWatch监控的日志格式和关键字
    - _需求: 3.5, 5.4_

- [x] 7. 实现与Mass Email系统的集成接口
  - [x] 7.1 创建服务名称查询API
    - 实现服务名称查询接口
    - 添加过滤和分页功能
    - 实现响应缓存和优化
    - _需求: 7.1, 7.3_

  - [x] 7.2 实现数据更新通知机制
    - 编写数据更新事件的结构化日志记录功能
    - 实现数据变更统计信息的日志输出（新增、更新、删除的服务数量）
    - 添加数据同步完成状态的详细日志记录，便于监控系统识别
    - _需求: 7.2, 7.4_



- [x] 8. 配置验证和AWS集成
  - [x] 8.1 验证Secret Manager配置和连接
    - 实现ConfigManager类，集成AWS Secrets Manager获取敏感配置
    - 配置Lambda环境变量SECRETS_MANAGER_ARN指向指定的Secret ARN
    - 实现配置缓存机制和错误处理，支持配置验证和健康检查
    - 提供配置验证和连接测试的业务逻辑实现
    - _需求: 14.1, 14.2, 14.6_

  - [x] 8.2 创建Lambda函数环境变量配置
    - 更新RDSClient和PDFParser类使用ConfigManager获取配置信息
    - 实现基于Secret Manager配置的数据库连接池管理和S3客户端
    - 添加IAM权限配置文档和安全配置指导
    - 实现向后兼容性，支持传统配置方式
    - _需求: 14.1, 14.4, 14.7_

  - [x] 8.3 AWS服务集成验证
    - 创建完整的AWS服务集成验证逻辑，验证ConfigManager集成功能
    - 实现云上环境的配置验证和服务连通性检查
    - 添加CloudWatch日志输出，便于在AWS控制台中监控验证结果
    - 提供详细的AWS服务集成指导文档和故障排查指南
    - _需求: 1.1, 1.2, 4.1, 5.1_

- [x] 9. 基础设施即代码部署指导（手动操作）

  - [x] 9.1 RDS数据库资源部署指导


    - **操作说明**: 手动配置RDS实例和相关网络资源
    - **参考步骤**: 通过AWS控制台或CLI创建数据库实例
    - **配置要点**: 设置子网组、安全组、参数组和备份策略
    - **注意事项**: 确保数据库安全配置和网络访问控制
    - _需求: 4.1, 10.1, 10.4_

  - [x] 9.2 Lambda和事件触发器部署指导



    - **操作说明**: 手动部署Lambda函数和配置EventBridge规则
    - **参考步骤**: 打包Lambda代码并通过控制台或CLI部署
    - **配置要点**: 设置IAM角色、环境变量和触发器规则
    - **注意事项**: 确保Lambda权限配置和资源限制设置
    - _需求: 6.1, 6.2, 10.1_

  - [x] 9.3 监控和告警资源部署指导


    - **操作说明**: 手动配置CloudWatch告警和SNS通知
    - **参考步骤**: 通过控制台创建告警规则和通知主题
    - **配置要点**: 设置告警阈值、通知订阅和日志过滤器
    - **注意事项**: 确保告警覆盖关键指标和通知及时性
    - _需求: 7.1, 7.5, 7.7_

- [x] 10. Lambda函数部署和云上测试准备
  - [x] 10.1 创建云上测试事件模板和验证指导
    - 创建EventBridge测试事件模板，用于在AWS控制台中手动触发Lambda函数
    - 编写CloudWatch日志分析指导，帮助验证Lambda函数执行结果
    - 提供AWS控制台中的功能验证步骤和检查清单
    - 创建性能监控配置指导，跟踪执行时间和资源使用
    - 编写故障排查指导文档，包含常见问题和解决方案
    - _需求: 15.4, 15.5, 15.6, 15.7_

  - [ ] 10.2 创建Lambda函数打包和部署指导



    - 编写自动化打包脚本，包含依赖管理和文件压缩
    - 提供部署指导脚本，包含详细的AWS CLI部署命令
    - 支持不同环境（dev、test、prod）的配置差异
    - 添加版本管理和回滚功能
    - 创建环境变量配置模板和验证
    - 实现部署前检查和部署后验证功能
    - _需求: 15.1, 15.2, 15.3_



- [x] 11. 正则表达式模式生成和管理


  - [x] 11.1 实现自动正则表达式模式生成



    - 基于同步的服务名称自动生成regex_patterns表记录
    - 实现优先级计算和复合模式识别逻辑
    - 添加模式验证和冲突检测功能
    - 创建模式更新和版本管理机制
    - _需求: 8.2, 8.3, 8.4_

  - [x] 11.2 实现正则表达式模式优化和维护




    - 创建模式性能分析和优化建议
    - 实现模式使用统计和效果评估
    - 添加模式清理和归档功能
    - 创建模式导入导出和备份机制
    - 实现模式测试和验证工具
    - _需求: 8.2, 8.5, 8.6_
