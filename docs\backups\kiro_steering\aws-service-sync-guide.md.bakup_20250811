---
inclusion: fileMatch  
fileMatchPattern: '*aws*service*sync*|*service*name*|*同步*'
---

# AWS服务名称同步模块开发指南 - v2.0 优化版

## 📋 文档导航

### 快速定位指南
- **🏗️ 数据库架构和优化**: 请参考 **[数据库开发指导文档](database-development-guide.md)**
- **📊 8种核心模式类型详解**: 请参考 **[数据库开发指导文档 - regex_patterns表](database-development-guide.md#regex_patterns-8种模式类型)**
- **⚡ 高性能索引和查询优化**: 请参考 **[数据库开发指导文档 - 性能优化要点](database-development-guide.md#性能优化要点)**
- **🔧 组件化架构支持**: 请参考 **[数据库开发指导文档 - 组件化架构支持](database-development-guide.md#组件化架构支持)**

---

## 核心原则

**🎯 功能精简原则**: AWS服务名称同步模块的所有子功能开发都应该**只实现功能本身**，不要过度开发增加其他功能，如：
- ❌ 不要添加复杂的监控和性能统计功能
- ❌ 不要添加详细的报告生成和分析功能  
- ❌ 不要添加复杂的测试套件和验证工具
- ❌ 不要添加过度的日志记录和指标收集
- ✅ 只实现核心业务功能和基本错误处理
- ✅ 使用简单的日志记录（基本的成功/失败状态）
- ✅ 保持代码简洁和可维护性

## 模块概述

AWS服务名称同步模块是AWS中国区批量邮件自动化翻译系统的重要组成部分，专门负责定期从官方网页和PDF文档中抓取、解析和同步AWS中国区服务名称数据。

**模块定位**: 作为批量邮件翻译系统的数据源管理模块，为翻译流水线中的阶段1（文本预处理标准化）提供服务名称识别和占位符替换的基础数据。

**核心功能模块**:
1. **网页爬虫模块** - 从AWS中国区官网抓取服务名称
2. **PDF解析模块** - 解析S3存储的PDF文档
3. **数据处理模块** - 合并和标准化数据
4. **数据库存储模块** - 存储到PostgreSQL数据库
5. **正则表达式模式生成** - 为服务名称生成匹配模式

## 核心架构

### 模块结构
```
aws_service_sync/
├── handler.py              # Lambda入口函数
├── config.py              # 配置管理
├── scrapers/
│   ├── web_scraper.py     # 网页抓取器
│   └── pdf_parser.py      # PDF解析器
├── processors/
│   ├── data_processor.py  # 数据处理器
│   ├── regex_pattern_generator.py    # 正则模式生成器
│   └── service_pattern_sync.py       # 服务模式同步处理器
├── storage/
│   ├── rds_client.py      # RDS数据库客户端
│   └── s3_client.py       # S3操作客户端
└── utils/
    ├── logger.py          # 基础日志工具
    └── retry.py           # 重试机制
```

## 数据获取模块

### Web Scraper模块
- **目标URL**: https://www.amazonaws.cn/en/about-aws/regional-product-services/
- **提取内容**: "提供的服务"字段的值作为权威服务全称
- **基本验证**: 检查数据完整性
- **简单错误处理**: 网络访问失败时记录错误

### PDF Parser模块
- **数据源**: S3存储的AWS官方PDF文档
- **解析内容**: 三列数据（AWS offering, Long name, Short name）
- **数据清理**: 标准化服务名称格式
- **基本验证**: 检查解析结果

## 数据处理模块

### 数据合并和标准化
- **匹配逻辑**: 网页数据与PDF数据的智能匹配
- **字段生成**: 
  - `base_name`: 从`authoritative_full_name`移除括号内容
  - `service_code`: 从`internal_name`生成小写代码
- **去重处理**: 基于`authoritative_full_name`去重

## 数据库集成 - v2.0 优化版

> 📋 **完整数据库架构**: 表结构、索引优化和性能策略请参考 
> **[数据库开发指导文档 - 核心数据表章节](database-development-guide.md#核心数据表)**

### 模块特定的数据库操作

AWS服务名称同步模块基于系统统一的v2数据库架构，实现以下特定操作：

### UPSERT操作

```python
def upsert_service(service_data):
    """
    使用authoritative_full_name作为业务主键进行UPSERT操作
    
    Args:
        service_data (dict): 服务数据字典，必须包含以下字段：
            - authoritative_full_name: 权威服务全称（业务主键）
            - base_name: 规范化基础名称（用于翻译逻辑）
            - internal_name: PDF内部名称（可选）
            - short_name_en: 英文简称（可选，默认使用authoritative_full_name）
            - service_code: 服务代码（可选）
            - source: 数据来源（可选，默认'web_scrape'）
    
    Returns:
        int: 服务记录的ID
        
    Raises:
        ValueError: 当必需字段缺失时
        psycopg2.IntegrityError: 当数据完整性约束违反时
    """
    
    # 数据验证
    required_fields = ['authoritative_full_name', 'base_name']
    for field in required_fields:
        if not service_data.get(field):
            raise ValueError(f"Required field '{field}' is missing or empty")
    
    # 数据预处理和标准化
    processed_data = {
        'authoritative_full_name': service_data['authoritative_full_name'].strip(),
        'base_name': service_data['base_name'].strip(),
        'internal_name': service_data.get('internal_name', '').strip() or None,
        'short_name_en': service_data.get('short_name_en', service_data['authoritative_full_name']).strip(),
        'service_code': service_data.get('service_code', '').lower().strip() or None,
        'source': service_data.get('source', 'web_scrape')
    }
    
    sql = """
    INSERT INTO service_names 
        (authoritative_full_name, base_name, internal_name, full_name_en, 
         short_name_en, service_code, source, last_synced_at)
    VALUES 
        (%(authoritative_full_name)s, %(base_name)s, %(internal_name)s, 
         %(authoritative_full_name)s, %(short_name_en)s, %(service_code)s, 
         %(source)s, NOW())
    ON CONFLICT (authoritative_full_name) 
    DO UPDATE SET
        base_name = EXCLUDED.base_name,
        internal_name = EXCLUDED.internal_name,
        full_name_en = EXCLUDED.authoritative_full_name,  -- 保持与业务主键一致
        short_name_en = EXCLUDED.short_name_en,
        service_code = EXCLUDED.service_code,
        source = EXCLUDED.source,
        last_synced_at = NOW(),
        updated_at = NOW()
    RETURNING id, authoritative_full_name;
    """
    
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute(sql, processed_data)
                result = cur.fetchone()
                conn.commit()
                
                logger.info(f"Successfully upserted service: {result[1]} (ID: {result[0]})")
                return result[0]  # 返回服务ID
                
    except psycopg2.IntegrityError as e:
        logger.error(f"Integrity error upserting service '{processed_data['authoritative_full_name']}': {e}")
        raise
    except psycopg2.Error as e:
        logger.error(f"Database error upserting service '{processed_data['authoritative_full_name']}': {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error upserting service '{processed_data['authoritative_full_name']}': {e}")
        raise

def batch_upsert_services(services_data_list):
    """
    批量UPSERT服务数据，提高性能
    
    Args:
        services_data_list (list): 服务数据列表
        
    Returns:
        tuple: (成功处理数量, 失败列表)
    """
    success_count = 0
    failed_services = []
    
    for service_data in services_data_list:
        try:
            upsert_service(service_data)
            success_count += 1
        except Exception as e:
            failed_services.append({
                'service': service_data.get('authoritative_full_name', 'Unknown'),
                'error': str(e)
            })
            logger.warning(f"Failed to upsert service: {e}")
    
    logger.info(f"Batch upsert completed: {success_count} successful, {len(failed_services)} failed")
    return success_count, failed_services
```

#### 字段职责说明

**核心字段职责分离**：
- **`authoritative_full_name`**: 业务主键，来自AWS官网的权威服务全称
- **`base_name`**: 翻译逻辑用，规范化基础名称（移除括号内容）
- **`full_name_en`**: 首次提及替换值，与 `authoritative_full_name` 保持一致
- **`short_name_en`**: 后续提及替换值，来自PDF或默认使用全称
- **`internal_name`**: PDF原始数据保留，用于追溯
- **`service_code`**: 系统内部标识，用于模式分组管理

#### 优化特性

1. **数据验证**: 确保必需字段完整性
2. **数据标准化**: 自动清理和格式化输入数据
3. **错误处理**: 分类处理不同类型的数据库错误
4. **事务管理**: 确保操作的原子性
5. **返回值**: 提供操作结果反馈
6. **批量处理**: 支持高效的批量操作
7. **日志记录**: 详细的操作日志用于调试


### 正则表达式模式生成

> **⚠️ 重要**: 模式生成策略已完全重构。请参考 **[数据库开发指导文档 - 8种核心模式类型](database-development-guide.md#regex_patterns-8种模式类型)** 获取完整的实现指导。

> 🔧 **完整实现代码**: 详细的模式生成代码实现请参考 **[数据库开发指导文档 - 模式生成实现代码](database-development-guide.md#模式生成实现代码-v20-完整实现)**

#### 模块实现要点

AWS服务名称同步模块负责自动生成8种核心正则表达式模式类型：

```python
# 使用新的模式生成系统
from processors.regex_pattern_generator import generate_comprehensive_service_patterns

def sync_patterns_for_services(services_data):
    """同步服务的正则表达式模式"""
    all_patterns = []
    
    for service_data in services_data:
        # 生成完整的模式集合（8种核心类型 + 特殊变体）
        service_patterns = generate_comprehensive_service_patterns(service_data)
        all_patterns.extend(service_patterns)
    
    # 批量插入到数据库
    validated_count, conflicts = batch_insert_patterns_optimized(all_patterns)
    
    logger.info(f"Generated {len(all_patterns)} patterns, validated {validated_count}")
    if conflicts:
        logger.warning(f"Detected {len(conflicts)} pattern conflicts")
    
    return len(all_patterns)
```

> 🔗 **详细的模式类型说明、边界保护机制和特殊服务处理**: 请参考 
> **[数据库开发指导文档 - regex_patterns表详解](database-development-guide.md#regex_patterns-8种模式类型)**

### 与翻译系统的数据接口

AWS服务名称同步模块为翻译系统提供优化的数据接口，支持高性能匹配：

```python
def provide_patterns_for_translation_system():
    """为翻译系统提供优化的模式数据"""
    query = """
    SELECT pattern_name, regex_string, metadata, priority, service_code,
           related_service_id
    FROM regex_patterns 
    WHERE is_active = TRUE AND validation_status = 'valid'
    ORDER BY priority DESC, id ASC;
    """
    # 此查询利用系统优化的部分索引提供高性能支持
    return execute_query(query)
```

> 🔗 **数据库性能优化详情**: 索引策略、JSONB字段设计等请参考 
> **[数据库开发指导文档 - 性能优化要点](database-development-guide.md#性能优化要点)**

## 配置管理

### AWS Secrets Manager集成

```python
class ConfigManager:
    def __init__(self):
        self.secrets_client = boto3.client('secretsmanager', region_name='cn-northwest-1')
        self.secret_arn = os.environ.get('SECRETS_MANAGER_ARN')
        self._config_cache = None
    
    def get_config(self):
        """从Secrets Manager获取配置信息"""
        if self._config_cache is None:
            try:
                response = self.secrets_client.get_secret_value(SecretId=self.secret_arn)
                self._config_cache = json.loads(response['SecretString'])
            except Exception as e:
                logger.error(f"Failed to get config: {e}")
                raise
        return self._config_cache
```

### 配置结构
```json
{
  "host": "数据库主机",
  "port": "5432",
  "username": "数据库用户名",
  "password": "数据库密码",
  "database": "数据库名",
  "snpdf": "s3://bucket/path/to/pdf"
}
```

## 基本错误处理

### 重试机制

```python
@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=2, max=10),
    retry=retry_if_exception_type((ConnectionError, TimeoutError))
)
def fetch_data_with_retry(url):
    """带基本重试机制的数据获取"""
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        return response
    except requests.RequestException as e:
        logger.error(f"Failed to fetch data: {e}")
        raise
```

### 基本日志记录

```python
import logging

logger = logging.getLogger(__name__)

def log_sync_result(success, processed_count, error_message=None):
    """记录同步结果的基本信息"""
    if success:
        logger.info(f"同步成功，处理了 {processed_count} 个服务")
    else:
        logger.error(f"同步失败: {error_message}")
```

## 开发指导原则

### 1. 保持简洁
- 每个模块只实现其核心功能
- 避免添加复杂的统计和分析功能
- 使用简单直接的实现方式

### 2. 基本错误处理
- 实现必要的重试机制
- 记录关键错误信息
- 不要过度设计错误处理逻辑

### 3. 最小化依赖
- 只使用必要的第三方库
- 避免引入复杂的框架
- 保持代码的可维护性

### 4. 数据验证
- 实现基本的数据完整性检查
- 验证必要字段的存在
- 不要过度验证非关键数据

## Lambda函数入口 - v2.0 优化版

```python
def lambda_handler(event, context):
    """Lambda函数入口 - v2.0优化版，集成组件化架构"""
    try:
        logger.info("开始执行AWS服务同步 - v2.0")
        
        # 1. 初始化配置管理
        config_manager = ConfigManager()
        config = config_manager.get_config()
        
        # 2. 数据抓取模块
        web_scraper = WebScraper()
        pdf_parser = PDFParser(config)
        
        web_services = web_scraper.scrape_services()
        pdf_services = pdf_parser.parse_pdf()
        
        logger.info(f"数据抓取完成: 网页 {len(web_services)} 个服务, PDF {len(pdf_services)} 个服务")
        
        # 3. 数据处理和合并
        data_processor = DataProcessor()
        merged_services = data_processor.process_services(web_services, pdf_services)
        
        # 4. 数据库存储 - 使用v2架构
        rds_client = RDSClient(config)
        stored_count = rds_client.store_services(merged_services)
        
        # 5. 正则表达式模式生成 - v2.0完整实现
        from processors.regex_pattern_generator import (
            batch_generate_all_patterns, 
            batch_insert_patterns_with_metadata
        )
        
        # 生成8种核心模式类型 + 特殊变体
        all_patterns = batch_generate_all_patterns(merged_services)
        
        # 批量插入，支持metadata JSONB字段
        validated_count, conflicts = batch_insert_patterns_with_metadata(all_patterns)
        
        # 6. 性能统计和结果汇总
        result_summary = {
            'services_processed': stored_count,
            'patterns_generated': len(all_patterns),
            'patterns_validated': validated_count,
            'conflicts_detected': len(conflicts),
            'database_version': 'v2.0',
            'features_enabled': [
                'ENUM_types', 'JSONB_metadata', 'partial_indexes', 
                '8_pattern_types', 'boundary_protection', 'component_architecture'
            ]
        }
        
        logger.info(f"同步完成 - {result_summary}")
        
        if conflicts:
            logger.warning(f"检测到 {len(conflicts)} 个模式冲突，已自动处理")
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                'success': True,
                'version': '2.0',
                'summary': result_summary
            })
        }
        
    except Exception as e:
        logger.error(f"同步失败: {e}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'success': False,
                'error': str(e),
                'version': '2.0'
            })
        }

# 简单的模式数据提供接口
def provide_patterns_to_translation_system():
    """为翻译系统提供模式数据的简单接口"""
    try:
        patterns = get_active_patterns_from_database()
        logger.info(f"为翻译系统提供了 {len(patterns)} 个活跃模式")
        return patterns
    except Exception as e:
        logger.error(f"提供模式数据失败: {e}")
        return []

def get_active_patterns_from_database():
    """从数据库获取活跃的模式数据"""
    query = """
    SELECT pattern_name, regex_string, metadata, priority, service_code
    FROM regex_patterns 
    WHERE is_active = TRUE AND validation_status = 'valid'
    ORDER BY priority DESC, id ASC;
    """
    
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            cur.execute(query)
            return cur.fetchall()
```

## 📖 相关文档导航

### 系统级文档
- **[数据库开发指导文档](database-development-guide.md)** - 完整的数据库架构、优化策略和组件化支持
- **[批量邮件系统上下文](mass-email-system-context.md)** - 整个翻译系统的架构和上下文

### 快速查找索引
| 需要查找的内容 | 参考文档章节 |
|---------------|-------------|
| 完整数据库表结构 | [数据库开发指导文档 - 核心数据表](database-development-guide.md#核心数据表) |
| 8种正则模式类型详解 | [数据库开发指导文档 - regex_patterns表](database-development-guide.md#regex_patterns-8种模式类型) |
| 高性能索引策略 | [数据库开发指导文档 - 性能优化要点](database-development-guide.md#性能优化要点) |
| 组件化架构支持 | [数据库开发指导文档 - 组件化架构支持](database-development-guide.md#组件化架构支持) |
| 边界保护机制 | [数据库开发指导文档 - 边界保护机制](database-development-guide.md#边界保护机制) |
| JSONB字段设计 | [数据库开发指导文档 - JSONB高性能索引](database-development-guide.md#jsonb-高性能索引) |

## 总结

AWS服务同步模块应该专注于核心功能实现：
1. **数据抓取** - 从网页和PDF获取服务名称
2. **数据处理** - 合并、标准化和去重
3. **数据存储** - 存储到数据库
4. **模式生成** - 生成正则表达式匹配模式

避免过度开发监控、统计、测试等附加功能，保持系统的简洁性和可维护性。

> 💡 **开发提示**: 本模块的所有数据库相关的详细设计和优化策略都已统一到 **[数据库开发指导文档](database-development-guide.md)** 中，请优先参考该文档获取权威信息。