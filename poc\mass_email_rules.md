## I. 英文原文修订规则

### A. 基础替换
1.  `AWS` → `Amazon`
2.  `AWS Support` → `Amazon Web Services`
3.  `all the AWS regions` 或者 `all the commercial regions` -> `Amazon Web Services (AWS) China Region includes the Amazon Web Services China (Beijing) Region, operated by Sinnet and Amazon Web Services China (Ningxia) Region, operated by NWCD`

### B. 时间和日期处理
1.  需要将`UTC`时区的时间转换成`UTC+8`的时间，并重新标注时区为`UTC+8`。
    将美国时区(PDT, PST等)时间转换为北京时间(UTC+8)
    示例: `12:01 AM PDT` → `15:01 (UTC+8)`
    保持日期格式不变，确保年月日顺序一致


## II. 中文翻译规则

### A. 公司和服务名称
1.  `Amazon` → `亚马逊云科技`
2.  `Amazon account` → `亚马逊云科技账户`
3.  `Amazon console` → `亚马逊云科技控制台`
4.  `Amazon Web Services` → `亚马逊云科技技术支持`（首次出现）/ `我们`（后续出现）
5.  `Amazon Support` / `Amazon Support center` → `亚马逊云科技中国支持团队`
6.  `Amazon Document` → `亚马逊云科技文档`
7.  `Amazon CLI` → `Amazon Command Line Interface (Amazon CLI)`(首次出现)/ `Amazon CLI`(后续出现)
8.  `Affected resources` → `受影响的资源`
9.  `all the AWS regions` 或者 `all the commercial regions` -> `亚马逊云科技中国区域`

## III. 产品名称规范

### A. 命名规则
1.  产品首次出现：使用全称
    a.  示例：`Amazon Elastic Compute Cloud (Amazon EC2)`
2.  后续出现：使用简称
    a.  示例：`Amazon EC2`

## IV. CLI参数规范

### A. 参数使用
对包含`--region`参数的CLI命令,要检索全文是否提及北京区域或者宁夏区域，单独提及某个区域时--region 参数直接使用对应区域标识,多个区域提及时-- region 参数使用北京区域标识:
宁夏区域相关内容使用: `--region cn-northwest-1`
北京区域相关内容使用: `--region cn-north-1`


## VI. 其他合规性检查

### A. 链接处理
1. 将`https://aws.amazon.com/support`替换为`https://console.amazonaws.cn/support/`
2. 将链接中的所有大写字母转为小写
3. 将链接中的`aws.amazon.com`域名替换为`amazonaws.cn`或`www.amazonaws.cn`
4. 对于非`amazonaws.cn`的域名的网页链接只做标识出的处理。