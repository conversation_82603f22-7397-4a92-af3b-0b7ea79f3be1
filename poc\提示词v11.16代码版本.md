**核心变化(与提示词优化.md对比)**：

1.  **明确区分处理阶段**：更清晰地指明哪些规则由JavaScript预处理执行，哪些是LLM的核心任务，哪些由JavaScript后处理完成。
2.  **LLM任务聚焦**：强调LLM的主要任务是处理带有 `__XXX_YYY__` 占位符的文本，即翻译占位符周围的自然语言，并严格保留占位符。
3.  **元数据和Wording行处理澄清**：说明这些行的关键部分（如键名）会被JS转换为占位符，LLM需保留这些占位符。
4.  **【】标记处理**：根据代码中 `llmTranslationPrompt` 的实际规则（规则9）调整，强调翻译括号内的内容并使用合适的中文标点，而不是简单粗暴地全局移除 `【】`。
5.  **整体流程对齐**：使文档描述的系统行为与 `chat-aitao_massemail_code.html` 中的JavaScript实现和实际LLM提示词更为一致。

---

## 提示词优化与系统行为说明 (v11.16 代码对齐版)

### 核心目标

本系统严格遵循一个多阶段流程，旨在生成符合亚马逊云科技官方风格和术语规范的简体中文翻译。最终输出为经过完整处理流程的简体中文文本。

**系统处理流程概览**：

1.  **阶段 0: JavaScript 文本行类型分析与分发**:
    *   JavaScript 代码首先分析输入文本的每一行，判断其类型（元数据行、特殊内容行 `Wording :`、普通内容行）。
    *   不同类型的行将按各自策略进入后续处理。

2.  **阶段 1: JavaScript 英文术语标准化与占位符生成**:
    *   对于需要处理的英文内容（元数据行的值部分、`Wording :` 行的实际内容、普通内容行的全部内容），JavaScript 会执行一系列标准化操作（详见【阶段 1】规则）。
    *   标准化后的关键术语、服务名称、链接、代码块、ARN、CLI命令、特定错误码、时间戳等会被替换为 `__XXX_YYY__` 格式的占位符。
    *   **LLM接收的是经过此阶段处理后，包含大量占位符的文本。**

3.  **阶段 2: LLM 翻译 (核心任务)**:
    *   LLM 的核心任务是**翻译阶段 1 输出文本中所有未被占位符保护的英文自然语言部分**。
    *   **LLM 必须严格、原封不动地保留所有 `__XXX_YYY__` 占位符及其完整形式。**
    *   LLM 需遵循特定的翻译风格和指令（详见代码中 `llmTranslationPrompt` 的内容，其关键点会在【阶段 2】中体现）。

4.  **阶段 3: JavaScript 占位符恢复与中文最终格式化**:
    *   JavaScript 接收 LLM 翻译后的文本（仍包含占位符）。
    *   它会将所有 `__XXX_YYY__` 占位符恢复为其原始的、标准化的英文值或特殊格式化后的中文值（如时间）。
    *   执行最终的中文品牌术语替换和格式调整（详见【阶段 3】规则）。

### 最高优先级要求 (贯穿系统)

*   **行结构完整性**: 输出必须**完全保留**原文的行类型和格式。JavaScript在预处理和后处理阶段会尽力维持这一点，LLM也应通过保留换行和段落结构来配合。
*   **禁止添加原文不存在的元数据行**: 如果原文不包含任何元数据行（如`Service:`, `Region:`等），则最终输出也**绝对不允许**包含这些行。

### 输入文本行类型与JS预处理策略 (阶段 0 & 1)

输入文本被JavaScript初步划分为以下类型，并按相应策略进行预处理和占位符化：

1.  **元数据行 (Metadata Lines)**:
    *   **JS识别**: 以特定关键字开头的行，如 `Service:`, `Region:`, `Failure mode X:`, `TypeCode:`, `Event Type Code:`。
    *   **JS处理 (阶段 1)**:
        *   **键名 (Key)**: 如 "Service:"、"Region:" 会被JS转换为 `__METAKEY_SERVICE_KEY_X__`、`__METAKEY_REGION_KEY_X__` 等占位符。
        *   **值 (Value)**: 冒号后的值部分：
            *   对于特定键 (如 `Service`, `Region`, `TypeCode`, `Event Type Code`, `Failure mode`)，其值如果包含可标准化的AWS服务名或术语，JS也会尝试标准化并可能替换为相应占位符。但对于这些特定元数据键的值，JS的标准化可能较为宽松或直接保护。
            *   **LLM看到的是键名的占位符和可能已被部分处理/占位符化的值。**
    *   **LLM任务 (阶段 2)**: 保留 `__METAKEY_...__` 占位符。如果其后的值部分仍有未被JS占位符化的英文自然语言，则翻译之。

2.  **特殊内容行 (`Wording :` / `First Post Wording :`)**:
    *   **JS识别**: 以 `Wording :`、`wording :`、`First Post Wording :` 等结尾的行。
    *   **JS处理 (阶段 1)**:
        *   **键名部分**: 如 "Wording: " 或 "First Post Wording: " 会被JS转换为相应的 `__METAKEY_WORDING_KEY_X__` 或 `__METAKEY_FIRST_POST_WORDING_KEY_X__` 占位符。
        *   **内容部分**: 冒号后的实际英文内容会由JS执行【阶段 1: JavaScript 英文术语标准化】规则（**使用行内作用域进行服务名首次/后续追踪**），然后其标准化结果中的特定术语也会被替换为占位符。
    *   **LLM任务 (阶段 2)**: 保留键名占位符，翻译内容部分中所有未被JS进一步占位符化的英文自然语言。

3.  **普通内容行 (General Content Lines)**:
    *   **JS识别**: 所有不属于上述两类的行，且包含需要翻译或标准化的英文文本。
    *   **JS处理 (阶段 1)**:
        *   整行文本内容会由JS执行【阶段 1: JavaScript 英文术语标准化】规则（**使用全局作用域进行服务名首次/后续追踪**）。
        *   标准化后的术语、服务名等会被替换为占位符。
    *   **LLM任务 (阶段 2)**: 翻译所有未被JS占位符化的英文自然语言部分。

### 无元数据输入处理

*   如果输入文本完全不包含任何元数据行和Wording行，则JavaScript会将所有行都视为**普通内容行**进行阶段1的处理。LLM随后翻译处理后的文本。最终输出将保持与输入相同的段落结构，不添加任何元数据行。

---

### 【阶段 1: JavaScript 英文术语标准化与占位符生成】

**以下标准化主要由JavaScript预处理模块在文本送给LLM之前执行。LLM接收的是这些规则处理后，包含大量 `__XXX_YYY__` 占位符的文本。**

*   **核心原则**: 逐行（根据类型确定处理范围）按严格顺序应用以下规则，并将识别出的特定术语、代码、链接等替换为相应的 `__XXX_YYY__` 占位符。

*   **规则 0: `AWS_XXX_YYY` 特殊代码保护 (JS最高优先级)**
    *   JS会首先查找并保护所有 `AWS_` 开头的特定大写错误码/类型码 (如 `AWS_DYNAMODB_ACCESS_CONTROL_API_ISSUE`)，将其替换为 `__KEYCODE_X__` 占位符。

*   **规则 1: CLI 命令保护与区域参数标准化 (JS)**
    *   以 `aws ...` 开头的命令行会被JS识别。
    *   命令中的 `--region <region_code>` 参数，如果 `<region_code>` 不是 `{{region}}` 且不是中国区代码，JS会根据上下文（ZHY/宁夏 -> `cn-northwest-1`，BJS/北京或默认 -> `cn-north-1`）将其替换为对应的中国区代码。
    *   整个处理后的CLI命令会被替换为 `__CLICMD_X__` 占位符。

*   **规则 2: ARN 与 JSON 策略块保护 (JS)**
    *   JS会识别标准ARN格式字符串和 `{"Version": ...}` JSON块，并将它们分别替换为 `__ARNSTR_X__` 和 `__JSONBLOCK_X__` 占位符。
    *   ARN中的 `arn:aws:...` 会被JS尝试修正为 `arn:aws-cn:...`。

*   **规则 3: URL 保护与初步本地化 (JS)**
    *   JS会识别URL。
    *   对特定AWS域名进行初步转换（如 `docs.aws.amazon.com` -> `docs.amazonaws.cn`, `console.aws.amazon.com` -> `console.amazonaws.com.cn`）。
    *   对 `console.amazonaws.cn` 的链接，JS会尝试标准化区域子域名或`?region=`参数为中国区（优先上下文，默认 `cn-north-1`）。
    *   处理后的URL会被替换为 `__URL_X__` 占位符。

*   **规则 4: 服务名称规范化与占位符化 (JS - 核心)**
    *   **权威数据源**: JavaScript 代码中的 `servicePatterns` 数组定义了服务的标准全称、标准简称、基础名称等。
    *   **首次/后续规则**:
        *   Wording/First Post Wording行内容：**行内作用域**追踪服务首次/后续出现。
        *   普通内容行：**全局作用域**追踪服务首次/后续出现。
    *   **JS处理**:
        *   根据作用域和首次/后续状态，将文本中出现的各种服务名称变体（如 `EC2`, `AWS RDS`, `Amazon Elastic Compute Cloud (EC2) instance`）替换为 **标准全称** (首次) 或 **标准简称** (后续)。
        *   如果服务名称是复合词的一部分 (如 `EC2-specific`)，也会按规则处理服务名部分。
        *   特定模式如 `RDS for PostgreSQL`，核心服务 `RDS` 会按规则处理，然后 ` for PostgreSQL` 追加。
        *   **标准化后的服务名称**（如 `Amazon Elastic Compute Cloud (EC2)`, `Amazon RDS for PostgreSQL`）会被JS替换为 `__SRVCNM_X__` 占位符。
        *   常见的非服务但重要的AWS术语 (如 `Amazon Linux 2023`, `AL2023`, `Service Quotas`, `Open ID Connect (OIDC)`) 也会被JS替换为 `__PRDNM_X__` 或 `__TECHTERM_X__` 占位符。
        *   常见缩写 (CLI, API, SDK, IAM, SCPs, AMI等，非服务名本身时) 会被替换为 `__ACRNYM_X__` 占位符。

*   **规则 5: AWS 基础通用术语标准化 (JS)**
    *   独立 `AWS` (非服务名、非CLI开头) → `Amazon` (JS内部临时标记，最终可能由JS后处理为“亚马逊云科技”或被服务名吸收)。
    *   `AWS Account` → `Amazon Web Services Account` (可能被 `__PRDNM_X__` 保护)。
    *   `AWS Support`/`AWS Support Team`/`AWS Support Center` → `Amazon Web Services Support` (可能被 `__PRDNM_X__` 保护)。
    *   `AWS Management Console`/`AWS console` → `Amazon Web Services Console` (可能被 `__PRDNM_X__` 保护)。
    *   这些标准化后的术语，如果未被更具体的服务名或产品名占位符覆盖，其自身也可能被JS替换为通用占位符。

*   **规则 6: 时间和日期处理 (JS)**
    *   JS会识别多种时间日期格式 (包括PDT, PST, UTC, EST, EDT, GMT等)。
    *   **精确计算**: 将识别到的时间**精确计算**并统一转换为 **UTC+8** 时区。
        *   PDT (UTC-7) → 原始时间 + 15 小时
        *   PST (UTC-8) → 原始时间 + 16 小时
        *   UTC/GMT (UTC+0) → 原始时间 + 8 小时
        *   EDT (UTC-4) → 原始时间 + 12 小时
        *   EST (UTC-5) → 原始时间 + 13 小时
    *   **中间格式**: 转换后的时间被JS格式化为 `YYYY-MM-DD HH:MM UTC+8`。
    *   此中间格式的时间字符串会被JS替换为 `__TIMESTAMP_X__` 占位符。

*   **规则 7: 其他受保护元素 (JS)**
    *   `{{...}}` Mustache模板占位符: 替换为 `__MTPLHDR_X__`。
    *   `[...]` 文档引用标记 (如 `[1]`, `[some-doc-link]`): 替换为 `__DOCREF_X__`。
    *   `'some:PolicyAction*'` IAM策略字符串: 替换为 `__POLICY_X__`。

---

### 【阶段 2: LLM 翻译任务】

**LLM接收的是经过阶段1 JS预处理和占位符化后的文本。LLM的核心指令来源于代码中的 `llmTranslationPrompt` 变量。以下是关键的LLM行为要求，与 `llmTranslationPrompt` 对齐：**

*   **1. 绝对核心：占位符处理 (LLM最高指令)**
    *   文本中包含特殊占位符，格式如 `__TERM_PROTECTED_XXX__`, `__SRVCNM_XXX__`, `__ACRNYM_XXX__`, `__TIMESTAMP_XXX__`, `__URL_XXX__`, `__MTPLHDR_XXX__`, `__DOCREF_XXX__`, `__POLICY_XXX__`, `__PRDNM_XXX__`, `__TECHTERM_XXX__`, `__JSONBLOCK_XXX__`, `__ARNSTR_XXX__`, `__CLICMD_XXX__`, `__KEYCODE_XXX__`, `__METAKEY_XXX__`。
    *   **LLM必须原封不动地保留所有这些占位符及其完整形式。绝对禁止以任何形式翻译、修改、删除或添加任何占位符。**
    *   特别是 `__JSONBLOCK_XXX__`, `__ARNSTR_XXX__`, `__CLICMD_XXX__`, `__KEYCODE_XXX__` 和 `__METAKEY_XXX__` 代表的代码/ARN/CLI块、特殊代码或元数据键名，它们的内容（包括所有字符如引号、星号、斜杠、参数、双破折号、冒号等）**必须被精确地、逐字逐句地保留在占位符内部**（JS已将其内容存入占位符映射，LLM只需保留占位符本身）。
    *   确保占位符与其周围的文本（无论是英文原文还是中文译文）之间保持适当的空格（如果原文中有空格）。

*   **2. 翻译核心：翻译所有非占位符的英文 (LLM最高指令)**
    *   **LLM务必、必须、一定要翻译所有占位符之外的、且确实是英文自然语言表述的文本。**
    *   任何非占位符的英文自然语言叙述部分，无论它出现在哪里（即使在多个占位符之间），无论多短，都必须无一例外地翻译成简体中文。不允许遗漏任何应翻译的英文片段。
    *   **针对代码块/ARN块之间或附近的描述性文本**：如果一段由英文自然语言构成的描述性句子或段落（其内部可能也包含其他类型的占位符，如 `__TIMESTAMP_XXX__` 或 `__SRVCNM_XXX__`）出现在主要占位符（如 `__JSONBLOCK_XXX__`）之间，或者紧接其前后，这段英文描述性文本本身**绝对必须被翻译**。
    *   **针对自身包含占位符的描述性句子**：如果一个英文自然语言句子中既包含需要翻译的英文文本，也包含一个或多个其他类型的占位符（例如 `To continue with the same behavior after __TIMESTAMP_XXX__, we recommend...`），则**必须翻译**句子中所有非占位符的英文部分。

*   **3. 翻译风格与格式**
    *   翻译应流畅、准确，符合亚马逊云科技官方专业技术文档风格。
    *   使用简体中文和中文标点符号。
    *   保持原始文本的段落和行结构。确保输出的行数和换行符位置与输入文本中的占位符和被翻译文本段落结构一致。

*   **4. 特定品牌术语处理 (LLM)**
    *   **"Amazon"的翻译**: 只有当 "Amazon" 作为独立词汇出现，并且不是任何服务名称占位符 (`__SRVCNM_XXX__`) 或产品名称占位符 (`__PRDNM_XXX__`) 的一部分时，才应将其翻译为 "亚马逊云科技"。如果上下文中 "Amazon" 后面紧跟一个服务名称占位符 (如 "Amazon `__SRVCNM_XXX__`")，则此 "Amazon" 通常不应翻译，因为它已被JS作为服务名的一部分处理并包含在占位符中了。

*   **5. 特殊句子重写 (LLM - 对应 `llmTranslationPrompt` 规则8)**
    *   如果遇到类似 "If you have any questions or concerns, the Amazon Web Services Support is available on Product X `__DOCREF_0__` and via Support Center `__DOCREF_1__`." 这样的句子（其中`Amazon Web Services Support`可能已被JS替换为`__PRDNM_X__`），请将其翻译并重写为更简洁的："如果您有任何问题或者疑虑，请联系 `__PRDNM_X__` `__DOCREF_1__`。" (保留Support Center的引用，移除其他产品引用)。

*   **6. 方括号 `[...]` 内容处理 (LLM - 对应 `llmTranslationPrompt` 规则9)**
    *   如果方括号内不是一个被 `__DOCREF_XXX__` 占位符保护的引用标记，而是普通英文文本，则**必须翻译方括号内的文本**。
    *   翻译结果两侧应使用合适的中文标点：
        *   如果原文方括号表示强调或注释，可使用中文全角圆括号 `（...）` 或融入句子。
        *   **避免使用中文全角方括号 `【...】`**，除非有极特殊的排版要求且确信JS后处理不会移除。优先使用圆括号。

*   **7. 通用技术术语翻译 (LLM)**
    *   诸如 "Fast Launch" 之类的通用技术词汇，如果未被JS占位符保护，应翻译成中文 (例如 "Fast Launch" -> "快速启动")。其他例子见原提示词。

*   **8. LLM最终输出要求**
    *   **仅包含翻译后的中文文本和未经修改的占位符。** 不包含任何解释、注释或英文原文。

---

### 【阶段 3: JavaScript 占位符恢复与中文最终格式化】

**LLM返回包含占位符的翻译文本后，JavaScript执行以下收尾工作：**

*   **1. 占位符恢复 (JS)**
    *   JS会将所有 `__XXX_YYY__` 占位符替换回它们在阶段1被保护时对应的原始值（如标准化的服务名、ARN、URL、CLI命令、代码块等）或特定格式化后的值。
    *   **时间戳占位符 (`__TIMESTAMP_X__`) 特殊处理**:
        *   JS会取回其对应的 `YYYY-MM-DD HH:MM UTC+8` 值。
        *   将其格式化为 **`YYYY年M月D日 上午/下午 h:mm 北京时间`**。
        *   如果原始只有日期（阶段1的 `YYYY-MM-DD` 对应 `__TIMESTAMP_X__`），则JS格式化为 `YYYY年M月D日`，**不添加任何时间**。

*   **2. 最终中文品牌术语替换 (JS)**
    *   `Amazon Web Services Support` (从占位符恢复后) → `亚马逊云科技中国支持团队`。
    *   `Amazon Web Services Console` → `亚马逊云科技控制台`。
    *   `Amazon Web Services Account` → `亚马逊云科技账户`。
    *   `Amazon Web Services` (独立且非特定产品/服务名) → `亚马逊云科技`。
    *   独立 `Amazon` (非服务名/产品名部分，根据JS内置的排除列表逻辑判断) → `亚马逊云科技`。

*   **3. 格式微调 (JS)**
    *   JS可能会进行一些轻微的空格调整或确保输出格式的最终一致性。
    *   **关于 `【】`**：虽然已建议LLM避免使用，但如果最终输出中仍意外出现 `【】` 包围已恢复的术语，理想情况下JS应有最终清理步骤。当前代码 (`restorePlaceholdersAndFinalizeChinese`) 未显式包含此全局清理，但 `checkTranslationQuality_Optimized` 会检查一些特定场景的错误括号使用。

### 系统实现增强机制 (JS代码能力)

(此部分描述了JavaScript代码已具备的能力，供理解系统背景，非直接LLM指令)

*   **服务名称状态的精确追踪与验证 (JS)**: JS内部通过 `servicePatterns` 和状态管理（如 `globalServiceMentionState`）来处理和验证服务名称的首次/后续使用规则。
*   **通用格式修复 (JS)**: JS能够自动识别和修复一些常见的服务名称格式错误（如 "Amazon Simple Storage Service (Amazon S3)" → "Amazon Simple Storage Service (S3)"）。
*   **调试与监控 (JS)**: 代码包含详细的 `logDebug` 调用，用于追踪处理流程和状态。
*   **错误处理 (JS)**: 代码中有 `handleError` 函数处理异常。

---