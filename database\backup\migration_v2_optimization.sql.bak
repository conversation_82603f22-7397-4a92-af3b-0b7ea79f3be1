-- ====================================================================
-- CN Mass Email Translator 数据库优化迁移脚本
-- 基于 Amazon_service_name_matching_solutio.md 的优化建议
-- 
-- 版本: v1.1 -> v2.0
-- 创建日期: 2025-01-08
-- 用途: 将现有数据库升级到优化版本，保证数据完整性
-- ====================================================================

-- 开始事务以确保原子性操作
BEGIN;

-- 设置客户端编码
SET client_encoding = 'UTF8';

-- ====================================================================
-- 1. 备份现有数据
-- ====================================================================

-- 创建临时备份表
CREATE TEMP TABLE regex_patterns_backup AS 
SELECT * FROM regex_patterns;

RAISE NOTICE '已创建 regex_patterns 数据备份';

-- ====================================================================
-- 2. 添加新的ENUM类型值（如果需要）
-- ====================================================================

-- 检查并添加新的ENUM值
DO $$
BEGIN
    -- 检查 regex_pattern_type ENUM 类型是否存在
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'regex_pattern_type') THEN
        -- 为 regex_pattern_type 添加新值（如果不存在）
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumlabel = 'CONTEXT_PROTECTED' 
            AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'regex_pattern_type')
        ) THEN
            ALTER TYPE regex_pattern_type ADD VALUE 'CONTEXT_PROTECTED';
            RAISE NOTICE '已添加新的ENUM值: CONTEXT_PROTECTED';
        ELSE
            RAISE NOTICE 'ENUM值 CONTEXT_PROTECTED 已存在，跳过添加';
        END IF;
    ELSE
        RAISE NOTICE '警告: regex_pattern_type ENUM类型不存在，可能需要先创建基础架构';
    END IF;
END $$;

-- ====================================================================
-- 3. 表结构优化
-- ====================================================================

-- 3.1 修改 regex_patterns 表结构
RAISE NOTICE '开始优化 regex_patterns 表结构...';

-- 添加新的 metadata JSONB 字段
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'regex_patterns' AND column_name = 'metadata'
    ) THEN
        ALTER TABLE regex_patterns ADD COLUMN metadata JSONB;
        RAISE NOTICE '已添加 metadata JSONB 字段';
    END IF;
END $$;

-- 将现有的 pattern_type 从 VARCHAR 改为 ENUM（如果当前是VARCHAR）
DO $$
BEGIN
    -- 检查当前 pattern_type 字段类型
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'regex_patterns' 
        AND column_name = 'pattern_type' 
        AND data_type = 'character varying'
    ) THEN
        -- 先更新所有无效值为有效的ENUM值
        UPDATE regex_patterns 
        SET pattern_type = 'GENERAL' 
        WHERE pattern_type NOT IN ('SERVICE_NAME', 'TIMEZONE', 'CLI_COMMAND', 'URL', 'GENERAL');
        
        -- 修改字段类型为ENUM
        ALTER TABLE regex_patterns 
        ALTER COLUMN pattern_type TYPE regex_pattern_type 
        USING pattern_type::regex_pattern_type;
        
        RAISE NOTICE '已将 pattern_type 字段改为 ENUM 类型';
    END IF;
END $$;

-- 添加正则表达式长度约束
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'chk_regex_string_length'
    ) THEN
        ALTER TABLE regex_patterns 
        ADD CONSTRAINT chk_regex_string_length 
        CHECK (length(regex_string) < 10000);
        RAISE NOTICE '已添加 regex_string 长度约束';
    END IF;
END $$;

-- ====================================================================
-- 4. 数据迁移和转换
-- ====================================================================

RAISE NOTICE '开始数据迁移和转换...';

-- 4.1 将现有的 notes 字段中的结构化信息迁移到 metadata 字段
-- 注意：只有在 metadata 字段存在时才执行迁移
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'regex_patterns' AND column_name = 'metadata'
    ) THEN
        -- 将现有的 notes 字段中的结构化信息迁移到 metadata 字段
        UPDATE regex_patterns 
        SET metadata = jsonb_build_object(
            'isCompoundWithSuffix', 
            CASE 
                WHEN notes LIKE '%isCompoundWithSuffix: true%' THEN true 
                ELSE false 
            END,
            'suffixGroup',
            CASE 
                WHEN notes ~ 'suffixGroup: (\d+)' THEN 
                    (regexp_match(notes, 'suffixGroup: (\d+)'))[1]::int
                ELSE null
            END,
            'patternCategory',
            CASE 
                WHEN pattern_name LIKE '%_FULL_%' THEN 'full_name'
                WHEN pattern_name LIKE '%_SHORT_%' THEN 'short_name'
                WHEN pattern_name LIKE '%_ACRONYM_%' THEN 'acronym'
                ELSE 'general'
            END,
            'hasBoundaryProtection',
            CASE 
                WHEN notes LIKE '%boundary protection%' THEN true
                ELSE false
            END,
            'migrated_from_notes', true
        )
        WHERE metadata IS NULL AND notes IS NOT NULL;

        RAISE NOTICE '已完成 notes 到 metadata 的数据迁移';

        -- 为没有 metadata 的记录设置默认值
        UPDATE regex_patterns 
        SET metadata = jsonb_build_object(
            'isCompoundWithSuffix', false,
            'patternCategory', 'general',
            'hasBoundaryProtection', false
        )
        WHERE metadata IS NULL;

        RAISE NOTICE '已为所有记录设置 metadata 默认值';
    ELSE
        RAISE NOTICE '跳过 metadata 迁移：字段不存在';
    END IF;
END $$;

-- ====================================================================
-- 5. 索引优化
-- ====================================================================

RAISE NOTICE '开始创建优化索引...';

-- 5.1 创建部分索引 - 只索引活跃的服务
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_service_names_active_code') THEN
        CREATE INDEX idx_service_names_active_code 
        ON service_names(service_code) WHERE is_active = TRUE;
        RAISE NOTICE '已创建部分索引: idx_service_names_active_code';
    END IF;
END $$;

-- 5.2 创建关键性能提升索引 - 只索引活跃且有效的模式
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_regex_patterns_active_valid') THEN
        CREATE INDEX idx_regex_patterns_active_valid 
        ON regex_patterns(priority DESC, id ASC) 
        WHERE is_active = TRUE AND validation_status = 'valid';
        RAISE NOTICE '已创建部分索引: idx_regex_patterns_active_valid';
    END IF;
END $$;

-- 5.3 为新的 metadata JSONB 字段创建 GIN 索引
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_regex_patterns_metadata') THEN
        CREATE INDEX idx_regex_patterns_metadata 
        ON regex_patterns USING GIN (metadata);
        RAISE NOTICE '已创建 GIN 索引: idx_regex_patterns_metadata';
    END IF;
END $$;

-- ====================================================================
-- 6. translation_jobs 表分区优化
-- ====================================================================

RAISE NOTICE '开始 translation_jobs 表分区优化...';

-- 注意：将现有表转换为分区表需要重建表，这里提供一个安全的方法
DO $$
DECLARE
    table_exists boolean;
    has_data boolean;
BEGIN
    -- 检查表是否存在且有数据
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'translation_jobs'
    ) INTO table_exists;
    
    IF table_exists THEN
        SELECT EXISTS (SELECT 1 FROM translation_jobs LIMIT 1) INTO has_data;
        
        IF has_data THEN
            RAISE NOTICE '检测到 translation_jobs 表中有数据，跳过分区转换';
            RAISE NOTICE '建议：在维护窗口期间手动执行分区转换';
        ELSE
            RAISE NOTICE 'translation_jobs 表为空，可以安全地转换为分区表';
            -- 这里可以添加分区转换逻辑，但为了安全起见，我们只记录建议
        END IF;
    END IF;
END $$;

-- ====================================================================
-- 7. 数据验证
-- ====================================================================

RAISE NOTICE '开始数据验证...';

-- 7.1 验证 metadata 字段迁移
DO $$
DECLARE
    total_patterns integer;
    patterns_with_metadata integer;
BEGIN
    SELECT COUNT(*) INTO total_patterns FROM regex_patterns;
    SELECT COUNT(*) INTO patterns_with_metadata FROM regex_patterns WHERE metadata IS NOT NULL;
    
    IF total_patterns = patterns_with_metadata THEN
        RAISE NOTICE '✓ metadata 字段迁移验证通过: %/%', patterns_with_metadata, total_patterns;
    ELSE
        RAISE EXCEPTION '✗ metadata 字段迁移验证失败: %/% 记录有 metadata', patterns_with_metadata, total_patterns;
    END IF;
END $$;

-- 7.2 验证 ENUM 类型转换
DO $$
DECLARE
    enum_count integer;
BEGIN
    SELECT COUNT(*) INTO enum_count 
    FROM regex_patterns 
    WHERE pattern_type::text IN ('SERVICE_NAME', 'TIMEZONE', 'CLI_COMMAND', 'URL', 'GENERAL', 'CONTEXT_PROTECTED');
    
    IF enum_count = (SELECT COUNT(*) FROM regex_patterns) THEN
        RAISE NOTICE '✓ ENUM 类型转换验证通过';
    ELSE
        RAISE EXCEPTION '✗ ENUM 类型转换验证失败';
    END IF;
END $$;

-- 7.3 验证索引创建
DO $$
DECLARE
    expected_indexes text[] := ARRAY[
        'idx_service_names_active_code',
        'idx_regex_patterns_active_valid', 
        'idx_regex_patterns_metadata'
    ];
    idx text;
    missing_indexes text[] := '{}';
BEGIN
    FOREACH idx IN ARRAY expected_indexes
    LOOP
        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = idx) THEN
            missing_indexes := array_append(missing_indexes, idx);
        END IF;
    END LOOP;
    
    IF array_length(missing_indexes, 1) IS NULL THEN
        RAISE NOTICE '✓ 所有优化索引创建验证通过';
    ELSE
        RAISE EXCEPTION '✗ 缺少索引: %', array_to_string(missing_indexes, ', ');
    END IF;
END $$;

-- ====================================================================
-- 8. 性能统计信息更新
-- ====================================================================

RAISE NOTICE '更新表统计信息...';

-- 更新所有相关表的统计信息
ANALYZE service_names;
ANALYZE regex_patterns;
ANALYZE brand_term_mappings;
ANALYZE url_mappings;
ANALYZE translation_jobs;
ANALYZE feedback_submissions;

-- ====================================================================
-- 9. 清理和完成
-- ====================================================================

-- 删除临时备份表（在事务成功后）
-- DROP TABLE IF EXISTS regex_patterns_backup;

-- 提交事务
COMMIT;

-- ====================================================================
-- 迁移完成报告
-- ====================================================================

DO $$
DECLARE
    service_count integer;
    pattern_count integer;
    active_pattern_count integer;
BEGIN
    SELECT COUNT(*) INTO service_count FROM service_names WHERE is_active = TRUE;
    SELECT COUNT(*) INTO pattern_count FROM regex_patterns;
    SELECT COUNT(*) INTO active_pattern_count FROM regex_patterns WHERE is_active = TRUE AND validation_status = 'valid';
    
    RAISE NOTICE '=================================================================';
    RAISE NOTICE 'CN Mass Email Translator 数据库优化迁移完成！';
    RAISE NOTICE '迁移时间: %', NOW();
    RAISE NOTICE '=================================================================';
    RAISE NOTICE '数据统计:';
    RAISE NOTICE '- 活跃服务数量: %', service_count;
    RAISE NOTICE '- 正则模式总数: %', pattern_count;
    RAISE NOTICE '- 活跃有效模式数: %', active_pattern_count;
    RAISE NOTICE '=================================================================';
    RAISE NOTICE '主要优化项目:';
    RAISE NOTICE '✓ regex_patterns.pattern_type 改为 ENUM 类型';
    RAISE NOTICE '✓ 新增 regex_patterns.metadata JSONB 字段';
    RAISE NOTICE '✓ 添加 regex_string 长度约束 (< 10000 字符)';
    RAISE NOTICE '✓ 创建高性能部分索引';
    RAISE NOTICE '✓ 数据完整性验证通过';
    RAISE NOTICE '=================================================================';
    RAISE NOTICE '后续建议:';
    RAISE NOTICE '1. 在维护窗口期间考虑将 translation_jobs 转换为分区表';
    RAISE NOTICE '2. 监控新索引的性能表现';
    RAISE NOTICE '3. 定期更新表统计信息以保持查询优化';
    RAISE NOTICE '=================================================================';
END $$;