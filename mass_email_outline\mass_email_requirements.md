# Mass Email 工作要求总结

## 背景说明
### 当前处理流程
Mass Email目前通过一个专门的网页界面进行展示和管理。处理流程如下：
1. 通过网页界面接收新的Mass Email发布更新
2. 人工审核人员根据规范和要求对内容进行检查
3. 根据检查结果，更新修订后的中英文内容
4. 将修订后的内容提交回网页平台
5. 整个过程需要确保符合所有规范要求，保证翻译质量和内容准确性

### 目的
本文档旨在提供一个完整的Mass Email处理规范指南，帮助审核人员在处理过程中能够全面且准确地执行各项要求。

## 1. 翻译流程和审核流程
- CS → PS翻译 → PS内部Review的三步流程
- CS设置"Pending on PS translation"状态启动翻译
- PP收到提醒后安排对应工程师翻译
- PS工程师完成翻译后，设置为"Pending on PS internal review"
- MoD完成review后，设置为"Pending on CS internal review"

## 2. 固定翻译规则
### 英文原文修订规则
- PS在翻译前需要对英文原文进行检查和修订
- 首先将英文原文中的"AWS"替换为"Amazon"
- 修订后再进行后续的翻译工作
- 当指代 AWS Support 时，请使用“Amazon Web Services”或“我们”。

### 中Amazon文翻译规则
- Amazon → 亚马逊云科技
- Amazon account → 亚马逊云科技账户
- Amazon console → 亚马逊云科技控制台
- Amazon Support / Amazon Support center → 亚马逊云科技中国支持团队
- Amazon Document → 亚马逊云科技文档
- Amazon CLI → 首次使用Amazon Command Line Interface (Amazon CLI)，后续使用Amazon CLI
- Support console / Support center 链接必须使用：https://console.amazonaws.cn/support/
- "Affected resources" → "受影响的资源"
- 标准结束语："如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队"

## 3. 产品名称规范
- 产品首次出现使用全称，后续可用简称
- 例如：Amazon Relational Database Service (RDS) → Amazon RDS
- 以在线版regional product services table为准
- 避免使用未在中国区域上线的产品名称

## 4. 区域表述规范
### 中国区域表述
- 使用"亚马逊云科技中国区域"
- 具体说明"北京区域"(BJS)或"宁夏区域"(ZHY)
- 区分光环新网和西云数据运营的区域
### 避免使用的表述
- "all the AWS regions"
- "all the commercial regions"

## 5. 时间和格式要求
- UTC时间必须转换为北京时间（UTC+8）
- CLI示例中的region参数需使用正确的区域标识
- 如果是单个region的通知，--region参数直接写通知的region

## 6. 文档和链接要求
- 确保所有链接在中国区可访问
- 检查是否有对应的中国区文档链接
- 避免引用YouTube等中国无法访问的资源
- 验证所有技术文档的可访问性

## 7. 合规性要求
- 确保产品在中国区域可用
- Global China发送需要法律部确认
- 保持区域隔离的表述准确性

## 8. 语言质量要求
### 准确性
- 避免歧义
- 确保通顺可理解

### 专业性
- 正确使用产品全称和简称
- 保持专业术语的一致性
- 确保技术细节准确

### 语言流畅性
- 避免机械翻译
- 确保句子通顺
- 保持专业用语统一

## 9. SIM工作流程规范
- PP在SIM中记录关键字格式："BJS pp check in, login@ will help on it ."
- 需要E2M确认时：更新内容并修改Next Step为"Comment by requester"
- 不需要确认时：更新翻译内容并设置Next Step为"Pending on PS internal review"
