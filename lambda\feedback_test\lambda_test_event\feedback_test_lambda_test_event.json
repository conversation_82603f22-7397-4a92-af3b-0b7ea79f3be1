{"version": "2.0", "routeKey": "$default", "rawPath": "/generate-upload-url", "rawQueryString": "", "headers": {"accept": "*/*", "accept-encoding": "gzip, deflate, br", "accept-language": "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7", "host": "your-api-id.execute-api.cn-north-1.amazonaws.com.cn", "origin": "https://issues.cn-northwest-1.amazonaws.cn", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "x-amzn-trace-id": "Root=1-6684d193-abcdef1234567890"}, "requestContext": {"accountId": "************", "apiId": "your-api-id", "domainName": "your-api-id.execute-api.cn-north-1.amazonaws.com.cn", "domainPrefix": "your-api-id", "http": {"method": "GET", "path": "/generate-upload-url", "protocol": "HTTP/1.1", "sourceIp": "*********", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "requestId": "REQUEST_ID_12345", "routeKey": "$default", "stage": "$default", "time": "03/Jul/2024:08:42:27 +0000", "timeEpoch": *************}, "isBase64Encoded": false}