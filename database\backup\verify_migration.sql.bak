-- ====================================================================
-- CN Mass Email Translator 数据库迁移验证脚本
-- 用途: 验证数据库优化迁移是否成功完成
-- ====================================================================

-- 设置输出格式
\pset border 2
\pset format wrapped

SELECT '=== 数据库优化迁移验证报告 ===' as title;
SELECT NOW() as verification_time;

-- ====================================================================
-- 1. 表结构验证
-- ====================================================================

SELECT '1. 表结构验证' as section;

-- 验证 regex_patterns 表的新字段
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'regex_patterns' 
    AND column_name IN ('pattern_type', 'metadata')
ORDER BY column_name;

-- 验证约束
SELECT 
    constraint_name,
    constraint_type
FROM information_schema.table_constraints 
WHERE table_name = 'regex_patterns' 
    AND constraint_name = 'chk_regex_string_length';

-- ====================================================================
-- 2. ENUM 类型验证
-- ====================================================================

SELECT '2. ENUM 类型验证' as section;

-- 检查 regex_pattern_type ENUM 的所有值
SELECT 
    enumlabel as enum_value,
    enumsortorder as sort_order
FROM pg_enum 
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'regex_pattern_type')
ORDER BY enumsortorder;

-- ====================================================================
-- 3. 索引验证
-- ====================================================================

SELECT '3. 索引验证' as section;

-- 检查新创建的优化索引
SELECT 
    indexname,
    tablename,
    indexdef
FROM pg_indexes 
WHERE indexname IN (
    'idx_service_names_active_code',
    'idx_regex_patterns_active_valid',
    'idx_regex_patterns_metadata'
)
ORDER BY indexname;

-- ====================================================================
-- 4. 数据完整性验证
-- ====================================================================

SELECT '4. 数据完整性验证' as section;

-- 验证 metadata 字段迁移
SELECT 
    'regex_patterns metadata 迁移' as check_item,
    COUNT(*) as total_records,
    COUNT(metadata) as records_with_metadata,
    CASE 
        WHEN COUNT(*) = COUNT(metadata) THEN '✓ 通过'
        ELSE '✗ 失败'
    END as status
FROM regex_patterns;

-- 验证 pattern_type ENUM 转换
SELECT 
    'pattern_type ENUM 转换' as check_item,
    COUNT(*) as total_records,
    COUNT(CASE WHEN pattern_type::text IN ('SERVICE_NAME', 'TIMEZONE', 'CLI_COMMAND', 'URL', 'GENERAL', 'CONTEXT_PROTECTED') THEN 1 END) as valid_enum_records,
    CASE 
        WHEN COUNT(*) = COUNT(CASE WHEN pattern_type::text IN ('SERVICE_NAME', 'TIMEZONE', 'CLI_COMMAND', 'URL', 'GENERAL', 'CONTEXT_PROTECTED') THEN 1 END) THEN '✓ 通过'
        ELSE '✗ 失败'
    END as status
FROM regex_patterns;

-- ====================================================================
-- 5. 性能优化验证
-- ====================================================================

SELECT '5. 性能优化验证' as section;

-- 检查部分索引的效果
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM service_names 
WHERE is_active = TRUE AND service_code = 'ec2';

EXPLAIN (ANALYZE, BUFFERS)
SELECT * FROM regex_patterns 
WHERE is_active = TRUE AND validation_status = 'valid'
ORDER BY priority DESC, id ASC
LIMIT 10;

-- ====================================================================
-- 6. 数据统计
-- ====================================================================

SELECT '6. 数据统计' as section;

-- 服务统计
SELECT 
    'service_names' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_records,
    COUNT(DISTINCT service_code) as unique_service_codes
FROM service_names

UNION ALL

-- 正则模式统计
SELECT 
    'regex_patterns' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_records,
    COUNT(CASE WHEN is_active = TRUE AND validation_status = 'valid' THEN 1 END) as active_valid_records
FROM regex_patterns

UNION ALL

-- 品牌术语统计
SELECT 
    'brand_term_mappings' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_records,
    NULL as extra_info
FROM brand_term_mappings

UNION ALL

-- URL映射统计
SELECT 
    'url_mappings' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_records,
    NULL as extra_info
FROM url_mappings;

-- ====================================================================
-- 7. metadata 字段内容示例
-- ====================================================================

SELECT '7. metadata 字段内容示例' as section;

-- 显示一些 metadata 字段的示例内容
SELECT 
    pattern_name,
    pattern_type,
    priority,
    metadata,
    CASE 
        WHEN metadata->>'isCompoundWithSuffix' = 'true' THEN '复合后缀模式'
        ELSE '标准模式'
    END as pattern_category
FROM regex_patterns 
WHERE metadata IS NOT NULL
ORDER BY priority DESC
LIMIT 10;

-- ====================================================================
-- 8. 验证总结
-- ====================================================================

SELECT '8. 验证总结' as section;

DO $$
DECLARE
    total_checks integer := 0;
    passed_checks integer := 0;
    check_result text;
BEGIN
    -- 检查 metadata 字段
    total_checks := total_checks + 1;
    SELECT CASE WHEN COUNT(*) = COUNT(metadata) THEN 1 ELSE 0 END 
    INTO passed_checks FROM regex_patterns;
    
    -- 检查索引
    total_checks := total_checks + 1;
    IF EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_regex_patterns_active_valid') THEN
        passed_checks := passed_checks + 1;
    END IF;
    
    -- 检查 ENUM 类型
    total_checks := total_checks + 1;
    SELECT CASE WHEN COUNT(*) = COUNT(CASE WHEN pattern_type::text IN ('SERVICE_NAME', 'TIMEZONE', 'CLI_COMMAND', 'URL', 'GENERAL', 'CONTEXT_PROTECTED') THEN 1 END) THEN 1 ELSE 0 END 
    INTO check_result FROM regex_patterns;
    passed_checks := passed_checks + check_result::integer;
    
    RAISE NOTICE '=================================================================';
    RAISE NOTICE '数据库优化迁移验证完成';
    RAISE NOTICE '验证时间: %', NOW();
    RAISE NOTICE '=================================================================';
    RAISE NOTICE '验证结果: %/% 项检查通过', passed_checks, total_checks;
    
    IF passed_checks = total_checks THEN
        RAISE NOTICE '✓ 所有验证项目通过，迁移成功！';
    ELSE
        RAISE NOTICE '✗ 部分验证项目失败，请检查迁移过程';
    END IF;
    
    RAISE NOTICE '=================================================================';
END $$;