#!/usr/bin/env python3
"""
AWS中国区域服务网页抓取演示脚本
用于验证aws-service-sync功能规范需求1的可行性

该脚本演示如何使用BeautifulSoup解析AWS中国区域服务页面，
提取"提供的服务"字段的值作为服务的权威全称。
"""

import requests
from bs4 import BeautifulSoup
import re
import json
from typing import List, Dict, Optional
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AWSServiceScraper:
    """AWS中国区域服务抓取器"""
    
    def __init__(self):
        self.url = "https://www.amazonaws.cn/about-aws/regional-product-services/"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def fetch_page(self) -> Optional[str]:
        """
        获取AWS中国区域服务页面内容
        
        Returns:
            Optional[str]: 页面HTML内容，失败时返回None
        """
        try:
            logger.info(f"正在访问页面: {self.url}")
            response = self.session.get(self.url, timeout=30)
            response.raise_for_status()
            
            logger.info(f"页面获取成功，状态码: {response.status_code}")
            logger.info(f"页面内容长度: {len(response.text)} 字符")
            
            return response.text
            
        except requests.RequestException as e:
            logger.error(f"页面获取失败: {e}")
            return None
    
    def parse_services(self, html_content: str) -> List[str]:
        """
        解析HTML内容，提取服务名称列表
        
        Args:
            html_content (str): HTML页面内容
            
        Returns:
            List[str]: 提取的服务名称列表
        """
        try:
            logger.info("开始解析HTML内容")
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找包含服务列表的表格
            services = []
            
            # 方法1: 查找表格中的服务名称
            # 寻找包含"提供的服务"的表格
            tables = soup.find_all('table')
            logger.info(f"找到 {len(tables)} 个表格")
            
            for table in tables:
                # 查找表格中的所有行
                rows = table.find_all('tr')
                logger.info(f"表格中有 {len(rows)} 行")
                
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 3:  # 至少有3列：服务名称、北京、宁夏
                        service_cell = cells[0]
                        service_name = service_cell.get_text(strip=True)
                        
                        # 过滤掉表头和空行
                        if (service_name and 
                            service_name != "提供的服务:" and 
                            not service_name.startswith("北京") and
                            not service_name.startswith("宁夏") and
                            "Amazon" in service_name or "AWS" in service_name or "Elastic" in service_name):
                            services.append(service_name)
            
            # 方法2: 如果表格方法失败，尝试直接查找服务名称模式
            if not services:
                logger.info("表格解析未找到服务，尝试模式匹配")
                # 查找所有可能的AWS服务名称
                text_content = soup.get_text()
                
                # 使用正则表达式匹配AWS服务名称模式
                service_patterns = [
                    r'Amazon [A-Z][A-Za-z\s\-()]+',
                    r'AWS [A-Z][A-Za-z\s\-()]+',
                    r'Elastic [A-Z][A-Za-z\s\-()]+',
                ]
                
                for pattern in service_patterns:
                    matches = re.findall(pattern, text_content)
                    for match in matches:
                        clean_match = match.strip()
                        if len(clean_match) > 5 and clean_match not in services:
                            services.append(clean_match)
            
            # 数据清理和去重
            services = self._clean_and_deduplicate_services(services)
            
            logger.info(f"成功提取 {len(services)} 个服务名称")
            return services
            
        except Exception as e:
            logger.error(f"HTML解析失败: {e}")
            return []
    
    def _clean_and_deduplicate_services(self, services: List[str]) -> List[str]:
        """
        清理和去重服务名称列表
        
        Args:
            services (List[str]): 原始服务名称列表
            
        Returns:
            List[str]: 清理后的服务名称列表
        """
        cleaned_services = []
        seen_services = set()
        
        for service in services:
            # 基本清理
            cleaned = service.strip()
            
            # 移除多余的空格
            cleaned = re.sub(r'\s+', ' ', cleaned)
            
            # 跳过过短或无效的服务名称
            if len(cleaned) < 5:
                continue
                
            # 跳过重复的服务
            if cleaned.lower() in seen_services:
                continue
                
            seen_services.add(cleaned.lower())
            cleaned_services.append(cleaned)
        
        # 按字母顺序排序
        cleaned_services.sort()
        
        return cleaned_services
    
    def validate_data_integrity(self, services: List[str]) -> Dict[str, any]:
        """
        验证提取数据的完整性
        
        Args:
            services (List[str]): 提取的服务名称列表
            
        Returns:
            Dict[str, any]: 验证结果
        """
        validation_result = {
            'total_services': len(services),
            'is_valid': False,
            'validation_errors': [],
            'sample_services': services[:10] if services else [],
            'timestamp': datetime.now().isoformat()
        }
        
        # 验证1: 确保提取的服务数量大于0
        if len(services) == 0:
            validation_result['validation_errors'].append("未提取到任何服务名称")
        
        # 验证2: 确保服务数量在合理范围内（预期50-200个服务）
        if len(services) < 50:
            validation_result['validation_errors'].append(f"服务数量过少: {len(services)}，预期至少50个")
        elif len(services) > 200:
            validation_result['validation_errors'].append(f"服务数量过多: {len(services)}，预期不超过200个")
        
        # 验证3: 检查服务名称格式
        invalid_services = []
        for service in services:
            if not (service.startswith('Amazon ') or 
                   service.startswith('AWS ') or 
                   service.startswith('Elastic ')):
                invalid_services.append(service)
        
        if invalid_services:
            validation_result['validation_errors'].append(
                f"发现 {len(invalid_services)} 个格式异常的服务名称: {invalid_services[:5]}"
            )
        
        # 验证4: 检查是否包含核心服务
        core_services = ['Amazon EC2', 'Amazon S3', 'Amazon RDS', 'Amazon Lambda']
        missing_core_services = []
        
        for core_service in core_services:
            found = any(core_service in service for service in services)
            if not found:
                missing_core_services.append(core_service)
        
        if missing_core_services:
            validation_result['validation_errors'].append(
                f"缺少核心服务: {missing_core_services}"
            )
        
        # 如果没有验证错误，标记为有效
        validation_result['is_valid'] = len(validation_result['validation_errors']) == 0
        
        return validation_result
    
    def scrape_services(self) -> Dict[str, any]:
        """
        执行完整的服务抓取流程
        
        Returns:
            Dict[str, any]: 抓取结果，包含服务列表和验证信息
        """
        logger.info("开始AWS服务抓取流程")
        
        # 1. 获取页面内容
        html_content = self.fetch_page()
        if not html_content:
            return {
                'success': False,
                'error': '页面获取失败',
                'services': [],
                'validation': None
            }
        
        # 2. 解析服务名称
        services = self.parse_services(html_content)
        
        # 3. 验证数据完整性
        validation_result = self.validate_data_integrity(services)
        
        # 4. 构建结果
        result = {
            'success': True,
            'services': services,
            'validation': validation_result,
            'metadata': {
                'source_url': self.url,
                'extraction_time': datetime.now().isoformat(),
                'total_services': len(services)
            }
        }
        
        logger.info(f"抓取流程完成，成功提取 {len(services)} 个服务")
        
        return result

def main():
    """主函数：演示AWS服务抓取功能"""
    print("=== AWS中国区域服务抓取演示 ===")
    print("验证aws-service-sync功能规范需求1的可行性\n")
    
    # 创建抓取器实例
    scraper = AWSServiceScraper()
    
    # 执行抓取
    result = scraper.scrape_services()
    
    # 输出结果
    if result['success']:
        print(f"✅ 抓取成功！")
        print(f"📊 总计提取 {len(result['services'])} 个服务名称")
        print(f"🔍 数据验证: {'通过' if result['validation']['is_valid'] else '失败'}")
        
        if result['validation']['validation_errors']:
            print("\n⚠️ 验证警告:")
            for error in result['validation']['validation_errors']:
                print(f"   - {error}")
        
        print(f"\n📋 前10个服务名称示例:")
        for i, service in enumerate(result['services'][:10], 1):
            print(f"   {i:2d}. {service}")
        
        if len(result['services']) > 10:
            print(f"   ... 还有 {len(result['services']) - 10} 个服务")
        
        # 保存结果到文件
        output_file = f"aws_services_extracted_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 完整结果已保存到: {output_file}")
        
    else:
        print(f"❌ 抓取失败: {result.get('error', '未知错误')}")
    
    print("\n=== 演示完成 ===")

if __name__ == "__main__":
    main()