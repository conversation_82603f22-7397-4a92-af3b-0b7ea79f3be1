-- ====================================================================
-- 测试迁移脚本修复 - 简化验证脚本
-- 用途: 验证 migration_v2_optimization.sql 的修复是否有效
-- ====================================================================

-- 设置输出格式
\echo '=== 测试迁移脚本修复验证 ==='

-- 1. 检查表结构
\echo '1. 检查 regex_patterns 表结构:'
SELECT 
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'regex_patterns' 
    AND column_name IN ('pattern_type', 'metadata', 'notes')
ORDER BY column_name;

-- 2. 检查 ENUM 类型
\echo '2. 检查 regex_pattern_type ENUM 值:'
SELECT 
    enumlabel as enum_value,
    enumsortorder as sort_order
FROM pg_enum 
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'regex_pattern_type')
ORDER BY enumsortorder;

-- 3. 模拟迁移逻辑测试
\echo '3. 测试 metadata 字段操作:'

-- 创建测试表来验证逻辑
CREATE TEMP TABLE test_regex_patterns AS 
SELECT 
    id,
    pattern_name,
    pattern_type,
    notes,
    NULL::jsonb as metadata
FROM regex_patterns 
LIMIT 5;

-- 测试 metadata 迁移逻辑
UPDATE test_regex_patterns 
SET metadata = jsonb_build_object(
    'isCompoundWithSuffix', 
    CASE 
        WHEN notes LIKE '%isCompoundWithSuffix: true%' THEN true 
        ELSE false 
    END,
    'suffixGroup',
    CASE 
        WHEN notes ~ 'suffixGroup: (\d+)' THEN 
            (regexp_match(notes, 'suffixGroup: (\d+)'))[1]::int
        ELSE null
    END,
    'patternCategory',
    CASE 
        WHEN pattern_name LIKE '%_FULL_%' THEN 'full_name'
        WHEN pattern_name LIKE '%_SHORT_%' THEN 'short_name'
        WHEN pattern_name LIKE '%_ACRONYM_%' THEN 'acronym'
        ELSE 'general'
    END,
    'hasBoundaryProtection',
    CASE 
        WHEN notes LIKE '%boundary protection%' THEN true
        ELSE false
    END,
    'test_migration', true
)
WHERE notes IS NOT NULL;

-- 显示测试结果
SELECT 
    pattern_name,
    notes,
    metadata
FROM test_regex_patterns
WHERE metadata IS NOT NULL;

-- 4. 检查约束
\echo '4. 检查表约束:'
SELECT 
    constraint_name,
    constraint_type
FROM information_schema.table_constraints 
WHERE table_name = 'regex_patterns' 
    AND constraint_name LIKE '%regex%';

-- 5. 检查索引
\echo '5. 检查优化索引:'
SELECT 
    indexname,
    tablename,
    indexdef
FROM pg_indexes 
WHERE indexname IN (
    'idx_service_names_active_code',
    'idx_regex_patterns_active_valid',
    'idx_regex_patterns_metadata'
)
ORDER BY indexname;

\echo '=== 测试完成 ==='