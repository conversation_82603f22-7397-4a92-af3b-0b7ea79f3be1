# 数据库设计文档

## 文档范围（Scope）与定位

本设计文档面向数据库工程/后端工程，聚焦：
- 数据模型与约束（表/枚举/外键/检查约束）
- 性能与索引（部分索引、JSONB GIN）
- 查询模式与存储过程/函数
- 面向组件的数据库契约（PatternLoader/SuffixAssembler/MentionTracker/BoundaryGuard 所需的查询与索引）

不包含（Out-of-scope）：
- 应用层/服务层的算法实现与类/方法细节（例如 Aho-Corasick 自动机构建、分段正则的具体代码、占位符替换流程）
- 相关内容已迁移至：.kiro/steering/pattern-matching-engine-guide.md

## Overview

本设计文档基于"逻辑外化"设计哲学，为AWS中国区Mass Email批量邮件翻译和用户反馈收集系统提供了完整的数据库架构设计。系统**严格要求PostgreSQL 13.0或更高版本**作为核心数据存储引擎，支持ACID事务特性和高并发访问，旨在实现业务规则的数据库化管理、4阶段翻译流水线的完整追踪，以及8种核心正则表达式模式类型的智能匹配系统。

### 技术要求

**数据库版本要求**: PostgreSQL 13.0+（必需支持增强的JSONB功能和GIN索引优化）
**扩展依赖**: uuid-ossp扩展（用于UUID主键生成）
**字符编码**: UTF-8（支持中英文混合内容）

### 核心设计理念

**逻辑外化 (Externalization of Logic)**
将硬编码的业务规则（服务名称、品牌术语、URL映射、正则表达式模式）从应用程序代码移动到数据库中，实现动态配置和无需重新部署的规则更新。基于Amazon_service_name_matching_solutio.md的"最佳解决方案"，采用模块化组件设计和高性能算法优化。

**过程可追溯 (Traceability)**
通过`translation_jobs`表记录翻译任务的4阶段完整生命周期（Stage 0-3），支持精确的调试、质量审计和性能分析。支持时间分区以提高查询性能，每个阶段的输入输出都被完整保存。

**原子化与规范化**
不同类型的业务规则分离到独立的专用表中：`brand_term_mappings`（品牌术语）、`url_mappings`（URL本地化）、`regex_patterns`（正则表达式模式），采用ENUM类型提供类型安全和数据一致性。

**JSONB优化与组件化支持**
对于半结构化数据（如占位符映射、服务提及状态），使用PostgreSQL的JSONB字段配合GIN索引实现高效存储和查询。支持SuffixAssembler、MentionTracker等组件化架构的元数据存储需求。

**高性能策略 (v2.0 核心优化)**
- **部分索引 (Partial Index)**: 只索引活跃数据，减少索引大小60-80%，查询速度提升10-50x
- **Aho-Corasick预扫描**: O(n)复杂度的候选词预扫描，配合分段正则表达式
- **组件化架构**: PatternLoader、ServiceMatcher、SuffixAssembler、BoundaryGuard等模块化设计
- **热更新机制**: 基于SNS Pub/Sub的近实时规则更新，无需服务重启

## Architecture

### 整体架构设计

系统采用分层架构设计，包含以下核心层次：

```mermaid
graph TB
    A[应用层 - Lambda Functions] --> B[业务逻辑层 - Translation Pipeline]
    B --> C[数据访问层 - RDS Client]
    C --> D[数据存储层 - PostgreSQL Database]
    
    E[外部数据源] --> F[同步服务层 - AWS Service Sync]
    F --> C
    
    G[用户反馈层 - Tampermonkey Scripts] --> H[API网关层 - API Gateway]
    H --> A
    
    I[S3存储] --> A
    
    subgraph "数据库核心表"
        D --> J[service_names - 服务名称权威数据]
        D --> K[regex_patterns - 8种模式类型]
        D --> L[translation_jobs - 4阶段流水线]
        D --> M[feedback_submissions - 用户反馈]
        D --> N[brand_term_mappings - 品牌术语]
        D --> O[url_mappings - URL本地化]
    end
```

### 核心架构组件

**1. 翻译自动化模块**
- **4阶段处理流水线**: Stage 0（原始文本）→ Stage 1（预处理标准化）→ Stage 2（LLM翻译）→ Stage 3（最终中文）
- **占位符系统**: 在翻译过程中保护技术内容，使用`__XXX_YYY__`格式的占位符
- **服务名称追踪**: 全局和行级别的首次/后续提及状态管理，支持JSONB字段存储service_mention_state
- **时区转换机制**: 
  - PDT (UTC-7) → 原始时间 + 15小时
  - PST (UTC-8) → 原始时间 + 16小时  
  - UTC/GMT → 原始时间 + 8小时
  - 最终格式：`YYYY年M月D日 上午/下午 h:mm 北京时间`
- **品牌术语标准化**: 
  - `Amazon` → `亚马逊云科技`
  - `Amazon Support` → `亚马逊云科技中国支持团队`
  - `Amazon console` → `亚马逊云科技控制台`
  - `AWS Management Console` → `Amazon Web Services Console`

**2. 服务名称同步模块 (v2架构)**
- **功能精简原则**: 遵循"只实现核心业务功能和基本错误处理"的设计理念，避免复杂的监控和报告生成功能
- **多数据源集成**: 支持网页抓取（web_scrape）、PDF解析（pdf_parse）、手动维护（manual）三种数据来源
- **权威数据管理**: 使用`authoritative_full_name`作为业务主键，确保数据的唯一性和权威性
- **v2架构字段**: 
  - `base_name`: 翻译逻辑用，规范化基础名称（不含括号）
  - `internal_name`: 来自PDF的internal name
  - `full_name_en/short_name_en`: 首次/后续提及的替换值
- **自动化同步**: 通过EventBridge调度的Lambda函数实现定期同步
- **数据完整性**: 通过UPSERT操作和事务管理确保数据一致性
- **简洁日志记录**: 使用基本的成功/失败状态日志，保持代码简洁和可维护性

**3. 正则表达式模式管理模块 (v2.0优化版)**
- **8种核心模式类型**: 从特殊变体（优先级125）到上下文保护（优先级90）的分层匹配机制
  - 全称复合后缀模式 (120) - Amazon EC2 P3 instances
  - 全称标准模式 (115) - Amazon Elastic Compute Cloud (EC2)
  - 简称复合后缀模式 (110) - Amazon EC2 instances
  - 简称标准模式 (105) - Amazon EC2
  - 缩写复合后缀模式 (100) - EC2 instances
  - 缩写标准模式 (95) - EC2
  - 特殊变体模式 (125) - Aurora PostgreSQL等
  - 上下文保护模式 (90) - 避免ARN/URL中误匹配
- **ENUM类型安全**: 使用regex_pattern_type枚举提供类型安全（SERVICE_NAME、TIMEZONE、CLI_COMMAND、URL、GENERAL、CONTEXT_PROTECTED）
- **metadata JSONB字段**: 存储结构化元数据，支持复合后缀处理和组件化架构
- **边界保护机制**: ARN保护、URL保护、代码保护，避免在特定上下文中的误匹配
- **复合后缀处理**: 智能识别和保留服务名称后的技术术语（如"EC2 P3 instances"）
- **长度约束**: regex_string字段长度约束<10000字符作为安全屏障
- **验证状态管理**: validation_status字段支持pending、valid、invalid状态追踪

**4. 反馈收集模块**
- **S3集成**: 与S3存储的无缝集成，支持结构化的反馈数据存储
- **CORS安全**: 严格的跨域访问控制，仅允许`issues.cn-northwest-1.amazonaws.cn`
- **元数据收集**: 完整的用户环境信息收集（User Agent、页面URL等）
- **满意度分析**: 支持satisfied、unsatisfied、neutral三级满意度评估
- **翻译任务关联**: 通过translation_job_id外键支持反馈与翻译任务的关联分析

**5. 高性能优化模块 (v2.0新增)**
- **部分索引策略**: 只索引活跃数据，减少索引大小60-80%，查询速度提升10-50x
- **组件化架构支持**: 
  - PatternLoader组件：高性能模式加载和Aho-Corasick自动机构建
  - ServiceMatcher组件：分段正则匹配和候选词预扫描
  - SuffixAssembler组件：复合后缀模式处理
  - MentionTracker组件：服务提及状态追踪
  - BoundaryGuard组件：边界保护和上下文验证
- **表分区支持**: translation_jobs表按时间分区，支持大量历史数据管理
- **GIN索引优化**: 为JSONB字段创建GIN索引，支持高效的元数据查询

## Components and Interfaces

### 核心数据表组件

#### 1. service_names表 - 服务名称权威数据源

**设计目标**: 作为AWS服务名称的单一权威数据源，支持多数据源同步和翻译逻辑的精确控制。

**核心字段设计**:
```sql
CREATE TABLE service_names (
    id BIGSERIAL PRIMARY KEY,
    authoritative_full_name VARCHAR(255) NOT NULL UNIQUE,  -- 业务主键
    base_name VARCHAR(255) NOT NULL,                       -- 翻译逻辑用
    internal_name VARCHAR(255),                            -- PDF数据源
    full_name_en VARCHAR(255) NOT NULL,                    -- 首次提及用
    short_name_en VARCHAR(100) NOT NULL,                   -- 后续提及用
    service_code VARCHAR(50),                              -- AWS官方代码
    source rule_source NOT NULL DEFAULT 'manual',         -- 数据来源
    last_synced_at TIMESTAMPTZ                             -- 同步时间戳
);
```

**接口设计**:
- `get_service_by_authoritative_name(name)`: 根据权威全称获取服务信息
- `upsert_service_batch(services_data)`: 批量更新或插入服务数据
- `get_services_by_source(source_type)`: 按数据源类型查询服务
- `get_stale_services(hours)`: 获取超过指定时间未同步的服务

#### 2. regex_patterns表 - 8种模式类型系统 (v2.0优化版)

**设计目标**: 实现完全数据库化的正则表达式模式管理，支持8种核心模式类型和复合后缀处理，采用ENUM类型提供类型安全。

**核心字段设计**:
```sql
CREATE TABLE regex_patterns (
    id BIGSERIAL PRIMARY KEY,
    pattern_name VARCHAR(100) NOT NULL UNIQUE,     -- 模式的易读名称
    pattern_type regex_pattern_type NOT NULL,      -- ENUM类型提供类型安全
    regex_string TEXT NOT NULL,                    -- 正则表达式本身
    related_service_id BIGINT,                     -- 关联的服务ID (外键)
    service_code VARCHAR(50),                      -- 服务代码，如 "ec2", "s3"
    priority INTEGER NOT NULL DEFAULT 100,         -- 匹配优先级，数字越大优先级越高
    notes TEXT,                                    -- 传统备注字段，保持向后兼容
    metadata JSONB,                                -- 新增JSONB字段存储结构化元数据
    is_active BOOLEAN NOT NULL DEFAULT TRUE,       -- 是否启用
    validation_status validation_status_type NOT NULL DEFAULT 'pending', -- 验证状态: pending, valid, invalid
    -- 长度约束作为安全屏障
    CONSTRAINT chk_regex_string_length CHECK (length(regex_string) < 10000)
);
```

**ENUM类型定义**:
```sql
CREATE TYPE regex_pattern_type AS ENUM (
    'SERVICE_NAME',         -- 服务名称
    'TIMEZONE',             -- 时区
    'CLI_COMMAND',          -- CLI命令
    'URL',                  -- URL
    'GENERAL',              -- 通用
    'CONTEXT_PROTECTED'     -- 上下文保护模式
);
```

**8种核心模式类型架构**:
```
优先级层次 (90-130):
├── 特殊变体模式 (125) - Aurora PostgreSQL等特殊情况
├── 全称复合后缀模式 (120) - Amazon EC2 P3 instances
├── 全称标准模式 (115) - Amazon Elastic Compute Cloud (EC2)
├── 简称复合后缀模式 (110) - Amazon EC2 instances
├── 简称标准模式 (105) - Amazon EC2
├── 缩写复合后缀模式 (100) - EC2 instances
├── 缩写标准模式 (95) - EC2
└── 上下文保护模式 (90) - 避免ARN/URL中误匹配
```

**metadata JSONB字段结构**:
```json
{
    "isCompoundWithSuffix": true,
    "suffixGroup": 1,
    "patternCategory": "full_complex_suffix",
    "hasBoundaryProtection": true,
    "generatedBy": "auto_generator_v2",
    "timestamp": "2025-01-08T10:30:00Z"
}
```

**复合后缀处理机制**:
- `isCompoundWithSuffix: true`: 标识复合模式
- `suffixGroup: N`: 指定捕获组编号
- `patternCategory`: 模式分类标识
- `hasBoundaryProtection`: 边界保护标识
- 支持的后缀类型: instance/instances, volume/volumes, policy/policies, for MySQL/PostgreSQL等

**接口设计**:
- `get_patterns_by_priority()`: 按优先级降序获取活跃模式，利用部分索引优化
- `generate_service_patterns(service_data)`: 为服务生成8种模式类型，包含metadata
- `validate_pattern_conflicts()`: 检测模式冲突和重叠
- `batch_insert_patterns_with_metadata(patterns)`: 批量插入优化模式，支持JSONB字段
- `get_compound_patterns()`: 获取复合后缀模式，利用GIN索引查询metadata
- `update_validation_status(pattern_id, status)`: 更新模式验证状态

#### 3. translation_jobs表 - 4阶段流水线追踪

**设计目标**: 提供翻译任务的完整生命周期管理和4阶段处理流程的精确追踪。

**4阶段处理架构**:
```
Stage 0: original_text (原始文本)
    ↓
Stage 1: stage1_standardized_en (预处理标准化)
    ├── 服务名称标准化
    ├── 占位符生成 (__SRVCNM_X__, __CLICMD_X__, etc.)
    ├── 时区转换处理
    └── 边界保护应用
    ↓
Stage 2: stage2_llm_input → stage2_llm_output (LLM翻译)
    ├── 保持占位符不变
    ├── 翻译非占位符内容
    └── 维持结构完整性
    ↓
Stage 3: stage3_final_cn (最终中文)
    ├── 占位符恢复
    ├── 品牌术语替换
    └── 最终格式化
```

**JSONB字段设计**:
- `placeholder_map`: 占位符到原文的映射关系
- `service_mention_state`: 服务首次/后续提及状态追踪

**接口设计**:
- `create_translation_job(original_text, submitted_by)`: 创建新翻译任务
- `update_job_stage(job_id, stage, content)`: 更新任务阶段状态
- `get_job_performance_metrics()`: 获取任务性能指标
- `get_failed_jobs_by_stage()`: 按失败阶段查询异常任务

### 业务逻辑组件

#### 1. 服务名称标准化引擎

**核心算法**:
```python
def standardize_service_name(text, service_mention_state):
    """
    服务名称标准化处理
    
    Args:
        text: 输入文本
        service_mention_state: 服务提及状态JSONB
    
    Returns:
        standardized_text: 标准化后的文本
        updated_state: 更新后的提及状态
    """
    patterns = get_patterns_by_priority()
    
    for pattern in patterns:
        matches = find_matches(text, pattern.regex_string)
        
        for match in matches:
            if pattern.is_compound_with_suffix:
                # 复合后缀处理
                service_name = get_service_name_with_mention_tracking(
                    pattern.related_service, service_mention_state
                )
                suffix = match.group(pattern.suffix_group)
                replacement = f"{service_name} {suffix}"
            else:
                # 标准处理
                replacement = get_service_name_with_mention_tracking(
                    pattern.related_service, service_mention_state
                )
            
            text = text.replace(match.group(0), replacement)
    
    return text, service_mention_state
```

#### 2. 占位符管理系统

**占位符类型定义**:
- `__SRVCNM_X__`: 服务名称占位符
- `__CLICMD_X__`: CLI命令占位符
- `__ARNSTR_X__`: ARN字符串占位符
- `__JSONBLOCK_X__`: JSON块占位符
- `__TIMESTAMP_X__`: 时间戳占位符
- `__URL_X__`: URL占位符

**占位符生成算法**:
```python
def generate_placeholders(text, placeholder_map):
    """
    生成占位符并建立映射关系
    
    Args:
        text: 输入文本
        placeholder_map: 现有占位符映射
    
    Returns:
        processed_text: 处理后的文本
        updated_map: 更新后的映射关系
    """
    placeholder_types = [
        ('CLI_COMMAND', r'aws\s+[a-z-]+(?:\s+[a-z-]+)*(?:\s+--[a-z-]+(?:\s+\S+)?)*'),
        ('ARN_STRING', r'arn:aws[^:]*:[^:]*:[^:]*:[^:]*:[^\s]+'),
        ('JSON_BLOCK', r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'),
        ('URL', r'https?://[^\s]+'),
        ('TIMESTAMP', r'\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}:\d{2}(?:\.\d{3})?(?:Z|[+-]\d{2}:\d{2})?')
    ]
    
    for placeholder_type, pattern in placeholder_types:
        text, placeholder_map = process_placeholder_type(
            text, pattern, placeholder_type, placeholder_map
        )
    
    return text, placeholder_map
```

#### 3. 边界保护机制 (v2.0统一边界保护原则)

**统一边界保护原则（BoundaryGuard + CONTEXT_PROTECTED）**:
- 模式本体禁止使用"可变宽度负向后行"，确保Python正则表达式兼容性
- 通过 `CONTEXT_PROTECTED` 类型的基线模式识别受保护区间，由 BoundaryGuard 在匹配后统一剔除：

**ARN保护模式**:
```sql
INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, priority)
VALUES ('CONTEXT_ARN_GENERIC', 'CONTEXT_PROTECTED', 'arn:(?:aws|aws-cn):\S+', 90);
```

**URL保护模式**:
```sql
INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, priority)
VALUES ('CONTEXT_URL_GENERIC', 'CONTEXT_PROTECTED', 'https?://\S+', 90);
```

**代码标识符保护**:
- 业务模式本体仅保留固定宽度的轻量边界保护（如 `(?<![:_-])` 与 `(?![:_-])`）
- 不使用可变宽度负向后行，确保Python re模块兼容性

## Data Models

### 数据模型关系图

```mermaid
erDiagram
    service_names ||--o{ regex_patterns : "generates"
    service_names {
        bigint id PK
        varchar authoritative_full_name UK
        varchar base_name
        varchar internal_name
        varchar full_name_en
        varchar short_name_en
        varchar service_code
        enum source
        boolean is_active
        timestamptz last_synced_at
    }
    
    regex_patterns {
        bigint id PK
        varchar pattern_name UK
        enum pattern_type
        text regex_string
        bigint related_service_id FK
        varchar service_code
        integer priority
        text notes
        jsonb metadata
        boolean is_active
        enum validation_status
    }
    
    translation_jobs {
        uuid id PK
        enum status
        text original_text
        text stage1_standardized_en
        text stage2_llm_input
        text stage2_llm_output
        text stage3_final_cn
        jsonb placeholder_map
        jsonb service_mention_state
        text error_message
        varchar error_stage
        integer processing_time_ms
    }
    
    feedback_submissions {
        uuid id PK
        uuid translation_job_id FK
        text page_url
        enum satisfaction
        text comments
        text user_agent
        varchar s3_object_key UK
        timestamptz submitted_at
    }
    
    translation_jobs ||--o{ feedback_submissions : "receives"
    
    brand_term_mappings {
        bigint id PK
        varchar term_en UK
        varchar term_cn
        boolean is_active
        text notes
    }
    
    url_mappings {
        bigint id PK
        text source_pattern
        text target_pattern
        boolean is_regex
        integer priority
        boolean is_active
        text notes
    }
```

### 核心数据类型设计

#### 1. 枚举类型定义

```sql
-- 翻译任务状态
CREATE TYPE translation_job_status AS ENUM (
    'pending',              -- 等待处理
    'processing_stage1',    -- 文本预处理标准化阶段
    'processing_stage2',    -- LLM翻译阶段
    'processing_stage3',    -- 占位符恢复阶段
    'completed',            -- 已完成
    'failed'                -- 失败
);

-- 数据来源类型
CREATE TYPE rule_source AS ENUM (
    'web_scrape',           -- 网页抓取
    'pdf_parse',            -- PDF解析
    'manual'                -- 手动添加
);

-- 用户反馈满意度
CREATE TYPE feedback_satisfaction AS ENUM (
    'satisfied',            -- 满意
    'unsatisfied',          -- 不满意
    'neutral'               -- 中性
);

-- 正则表达式验证状态
CREATE TYPE validation_status_type AS ENUM (
    'pending',              -- 待验证
    'valid',                -- 验证通过
    'invalid'               -- 验证失败
);
```

#### 2. JSONB字段结构设计

**placeholder_map字段结构**:
```json
{
    "__SRVCNM_0__": "Amazon Elastic Compute Cloud (EC2)",
    "__SRVCNM_1__": "Amazon Simple Storage Service (S3)",
    "__CLICMD_0__": "aws ec2 describe-instances --region cn-north-1",
    "__URL_0__": "https://console.amazonaws.com.cn/ec2/",
    "__TIMESTAMP_0__": "2024-01-15T10:30:00+08:00"
}
```

**service_mention_state字段结构**:
```json
{
    "Amazon Elastic Compute Cloud": {
        "mentioned": true,
        "usedFullName": true,
        "firstMentionPosition": 45,
        "subsequentMentions": 3
    },
    "Amazon Simple Storage Service": {
        "mentioned": true,
        "usedFullName": false,
        "firstMentionPosition": 120,
        "subsequentMentions": 1
    }
}
```

### 索引策略设计 (v2.0高性能优化版)

#### 1. 部分索引策略 - 核心性能提升

**关键性能提升**: 只索引活跃数据，减少索引大小60-80%，查询速度提升10-50x

```sql
-- 服务名称表的部分索引 - 只索引活跃的服务
CREATE INDEX idx_service_names_active_code 
ON service_names(service_code) WHERE is_active = TRUE;

-- 正则表达式模式表的部分索引 - 只索引活跃且有效的模式
CREATE INDEX idx_regex_patterns_active_valid 
ON regex_patterns(priority DESC, id ASC) 
WHERE is_active = TRUE AND validation_status = 'valid';
```

#### 2. 性能关键索引

```sql
-- 正则表达式模式表的核心索引
CREATE INDEX idx_regex_patterns_priority ON regex_patterns(priority DESC, id ASC);
CREATE INDEX idx_regex_patterns_type_active ON regex_patterns(pattern_type, is_active);
CREATE INDEX idx_regex_patterns_service_code ON regex_patterns(service_code);
CREATE INDEX idx_regex_patterns_validation_status ON regex_patterns(validation_status);

-- JSONB metadata字段的GIN索引 - 支持SuffixAssembler组件
CREATE INDEX idx_regex_patterns_metadata ON regex_patterns USING GIN (metadata);

-- 翻译任务表的JSONB索引
CREATE INDEX idx_translation_jobs_placeholder_map ON translation_jobs USING GIN (placeholder_map);
CREATE INDEX idx_translation_jobs_service_mention_state ON translation_jobs USING GIN (service_mention_state);

-- 服务名称表的业务索引
CREATE INDEX idx_service_names_base_name ON service_names(base_name);
CREATE INDEX idx_service_names_service_code ON service_names(service_code);
CREATE INDEX idx_service_names_source ON service_names(source);
CREATE INDEX idx_service_names_last_synced ON service_names(last_synced_at);

-- 品牌术语和URL映射表索引
CREATE INDEX idx_brand_term_mappings_is_active ON brand_term_mappings(is_active);
CREATE INDEX idx_url_mappings_priority ON url_mappings(priority DESC);
CREATE INDEX idx_url_mappings_is_active ON url_mappings(is_active);

-- 反馈提交表索引
CREATE INDEX idx_feedback_submissions_satisfaction ON feedback_submissions(satisfaction);
CREATE INDEX idx_feedback_submissions_submitted_at ON feedback_submissions(submitted_at);
CREATE INDEX idx_feedback_submissions_translation_job ON feedback_submissions(translation_job_id);
```

#### 3. 表分区支持 (可扩展性优化)

```sql
-- translation_jobs表按时间分区 - 解决历史数据膨胀
CREATE TABLE translation_jobs (
    -- ... 字段定义
) PARTITION BY RANGE (submitted_at);

-- 按月分区示例
CREATE TABLE translation_jobs_2025_01 PARTITION OF translation_jobs
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE translation_jobs_2025_02 PARTITION OF translation_jobs
    FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

-- 创建默认分区以处理未来数据
CREATE TABLE translation_jobs_default PARTITION OF translation_jobs DEFAULT;
```

#### 2. 查询优化策略

**优先级查询优化**:
```sql
-- 按优先级获取活跃模式，使用复合索引优化
SELECT regex_string, priority, service_code
FROM regex_patterns 
WHERE is_active = true 
ORDER BY priority DESC, id ASC
LIMIT 100;
```

**JSONB查询优化**:
```sql
-- 查询包含特定占位符的翻译任务
SELECT id, status, placeholder_map
FROM translation_jobs 
WHERE placeholder_map ? '__SRVCNM_0__'
AND status = 'completed';
```

## Error Handling

### 错误处理策略

#### 1. 翻译流水线错误处理

**阶段性错误捕获**:
```python
def process_translation_stage(job_id, stage, input_data):
    """
    翻译阶段处理与错误捕获
    """
    try:
        if stage == 'stage1':
            result = process_stage1_standardization(input_data)
        elif stage == 'stage2':
            result = process_stage2_llm_translation(input_data)
        elif stage == 'stage3':
            result = process_stage3_finalization(input_data)
        
        # 更新成功状态
        update_job_status(job_id, f'processing_{stage}', result)
        
    except ValidationError as e:
        # 数据验证错误
        log_error(job_id, stage, 'VALIDATION_ERROR', str(e))
        update_job_error(job_id, stage, f'Validation failed: {e}')
        
    except LLMServiceError as e:
        # LLM服务错误
        log_error(job_id, stage, 'LLM_SERVICE_ERROR', str(e))
        update_job_error(job_id, stage, f'LLM service unavailable: {e}')
        
    except DatabaseError as e:
        # 数据库错误
        log_error(job_id, stage, 'DATABASE_ERROR', str(e))
        update_job_error(job_id, stage, f'Database operation failed: {e}')
        
    except Exception as e:
        # 未预期错误
        log_error(job_id, stage, 'UNEXPECTED_ERROR', str(e))
        update_job_error(job_id, stage, f'Unexpected error: {e}')
```

#### 2. 数据同步错误处理

**多数据源同步容错**:
```python
def sync_service_data():
    """
    多数据源同步的容错处理
    """
    sync_results = {
        'web_scrape': {'success': False, 'error': None, 'count': 0},
        'pdf_parse': {'success': False, 'error': None, 'count': 0}
    }
    
    # 网页抓取容错
    try:
        web_services = scrape_web_services()
        sync_results['web_scrape'] = {
            'success': True, 
            'count': len(web_services)
        }
    except NetworkError as e:
        sync_results['web_scrape']['error'] = f'Network error: {e}'
        logger.warning(f'Web scraping failed: {e}')
    except ParsingError as e:
        sync_results['web_scrape']['error'] = f'Parsing error: {e}'
        logger.error(f'Web parsing failed: {e}')
    
    # PDF解析容错
    try:
        pdf_services = parse_pdf_services()
        sync_results['pdf_parse'] = {
            'success': True, 
            'count': len(pdf_services)
        }
    except FileNotFoundError as e:
        sync_results['pdf_parse']['error'] = f'PDF file not found: {e}'
        logger.error(f'PDF file missing: {e}')
    except PDFParsingError as e:
        sync_results['pdf_parse']['error'] = f'PDF parsing error: {e}'
        logger.error(f'PDF parsing failed: {e}')
    
    # 至少一个数据源成功才继续处理
    if not any(result['success'] for result in sync_results.values()):
        raise SyncError('All data sources failed')
    
    return sync_results
```

#### 3. 正则表达式验证错误处理

**模式验证与冲突检测**:
```python
def validate_regex_patterns():
    """
    正则表达式模式验证和冲突检测
    """
    validation_results = []
    
    patterns = get_all_active_patterns()
    
    for pattern in patterns:
        try:
            # 语法验证
            compiled_regex = re.compile(pattern.regex_string)
            
            # 性能测试
            test_performance(compiled_regex)
            
            # 冲突检测
            conflicts = detect_pattern_conflicts(pattern, patterns)
            
            if conflicts:
                validation_results.append({
                    'pattern_id': pattern.id,
                    'status': 'warning',
                    'message': f'Conflicts with patterns: {conflicts}'
                })
            else:
                validation_results.append({
                    'pattern_id': pattern.id,
                    'status': 'valid',
                    'message': 'Pattern validated successfully'
                })
                
        except re.error as e:
            validation_results.append({
                'pattern_id': pattern.id,
                'status': 'invalid',
                'message': f'Regex syntax error: {e}'
            })
            
        except PerformanceError as e:
            validation_results.append({
                'pattern_id': pattern.id,
                'status': 'invalid',
                'message': f'Performance issue: {e}'
            })
    
    return validation_results
```

### 监控和告警机制

#### 1. 关键指标监控

**翻译任务健康监控**:
- 任务失败率: `failed_jobs / total_jobs * 100`
- 平均处理时间: `AVG(processing_time_ms)`
- 各阶段失败分布: `GROUP BY error_stage`
- 积压任务数量: `COUNT(*) WHERE status = 'pending'`

**数据同步健康监控**:
- 数据新鲜度: `NOW() - MAX(last_synced_at)`
- 同步成功率: 基于同步日志的成功/失败统计
- 数据源覆盖度: 各数据源的服务数量对比

**正则表达式质量监控**:
- 无效模式比例: `invalid_patterns / total_patterns * 100`
- 验证积压: `COUNT(*) WHERE validation_status = 'pending'`
- 优先级冲突: 相同优先级的活跃模式数量

#### 2. 自动化告警规则

```python
ALERT_RULES = {
    'translation_failure_rate': {
        'threshold': 0.05,  # 5%失败率
        'window': '1h',
        'action': 'send_alert'
    },
    'sync_data_staleness': {
        'threshold': 24,    # 24小时未同步
        'window': '1h',
        'action': 'send_warning'
    },
    'invalid_pattern_ratio': {
        'threshold': 0.10,  # 10%无效模式
        'window': '1h',
        'action': 'send_alert'
    },
    'pending_validation_backlog': {
        'threshold': 50,    # 50个待验证模式
        'window': '1h',
        'action': 'send_warning'
    }
}
```

## 组件化架构支持 (v2.0模块化设计)

基于Amazon_service_name_matching_solutio.md的组件化架构设计，数据库层需要支持以下核心组件。

> **注意**：本章节包含的应用层算法实现细节（类/方法/流程示例）已迁移至匹配引擎文档：.kiro/steering/pattern-matching-engine-guide.md。本设计文档仅保留与数据库相关的接口契约、关键查询与索引配置。

### DB侧要点

以下为"组件化架构支持"在数据库层面的关键支撑点：

1. **模式数据装载与排序**
   - 仅加载活跃且验证通过的模式：is_active = TRUE AND validation_status = 'valid'
   - 稳定排序：ORDER BY priority DESC, id ASC
   - 依赖部分索引：idx_regex_patterns_active_valid

2. **复合后缀元数据（JSONB）**
   - 使用 metadata JSONB 字段标注：isCompoundWithSuffix、suffixGroup、patternCategory、hasBoundaryProtection
   - 依赖 GIN 索引：idx_regex_patterns_metadata

3. **上下文保护（CONTEXT_PROTECTED）**
   - 通过 pattern_type = 'CONTEXT_PROTECTED' 提供边界保护模式集合

4. **服务名称数据契约**
   - service_names: authoritative_full_name（业务主键）、base_name、full_name_en、short_name_en、service_code
   - 部分索引：idx_service_names_active_code

5. **翻译任务状态与占位符映射（JSONB）**
   - translation_jobs: placeholder_map、service_mention_state（均为 JSONB）
   - GIN 索引：idx_translation_jobs_placeholder_map、idx_translation_jobs_service_mention_state

### PatternLoader组件支持
**职责**：从数据库一次性拉取活跃且有效的正则规则，构建高效内存查找结构

```python
class PatternLoader:
    """高性能模式加载器 - 利用部分索引优化"""
    
    async def load_patterns(self):
        """利用优化索引加载模式"""
        query = """
        SELECT pattern_name, regex_string, metadata, priority, service_code
        FROM regex_patterns 
        WHERE is_active = TRUE AND validation_status = 'valid'
        ORDER BY priority DESC, id ASC;
        """
        # 此查询将利用 idx_regex_patterns_active_valid 部分索引
        # 性能提升：10-50x
        return await self.db_pool.fetch(query)
    
    def build_aho_corasick_automaton(self, patterns):
        """构建Aho-Corasick自动机用于预扫描"""
        # 提取非正则文本关键词
        keywords = self.extract_keywords(patterns)
        # 构建Trie树映射
        return self.build_automaton(keywords)
```

### SuffixAssembler组件支持
**职责**：处理复合后缀模式，利用metadata JSONB字段

```python
class SuffixAssembler:
    """后缀组装器 - 利用JSONB元数据"""
    
    def get_compound_patterns(self):
        """获取复合后缀模式"""
        query = """
        SELECT pattern_name, regex_string, 
               metadata->>'suffixGroup' as suffix_group,
               metadata->>'isCompoundWithSuffix' as is_compound
        FROM regex_patterns 
        WHERE metadata->>'isCompoundWithSuffix' = 'true'
          AND is_active = TRUE;
        """
        # 利用 idx_regex_patterns_metadata GIN索引
        return self.db_pool.fetch(query)
    
    def assemble_service_with_suffix(self, service_name, suffix, is_first_mention):
        """组装服务名称和后缀"""
        if is_first_mention:
            return f"{service_name} {suffix}"
        else:
            # 使用简称
            return f"{self.get_short_name(service_name)} {suffix}"
```

### MentionTracker组件支持
**职责**：跟踪服务首次/后续提及状态

```python
class MentionTracker:
    """提及状态追踪器 - 利用JSONB状态存储"""
    
    def update_mention_state(self, job_id, service_base_name, used_full_name):
        """更新服务提及状态"""
        query = """
        UPDATE translation_jobs 
        SET service_mention_state = 
            COALESCE(service_mention_state, '{}'::jsonb) || 
            jsonb_build_object(%s, jsonb_build_object(
                'mentioned', true,
                'used_full_name', %s,
                'timestamp', NOW()::text
            ))
        WHERE id = %s;
        """
        return self.db_pool.execute(query, service_base_name, used_full_name, job_id)
    
    def get_mention_history(self, job_id):
        """获取提及历史 - 利用GIN索引"""
        query = """
        SELECT service_mention_state
        FROM translation_jobs 
        WHERE id = %s AND service_mention_state IS NOT NULL;
        """
        return self.db_pool.fetchrow(query, job_id)
```

### BoundaryGuard组件支持
**职责**：边界保护，利用CONTEXT_PROTECTED类型模式

```python
class BoundaryGuard:
    """边界保护器 - 利用上下文保护模式"""
    
    def load_protection_patterns(self):
        """加载上下文保护模式"""
        query = """
        SELECT regex_string, metadata
        FROM regex_patterns 
        WHERE pattern_type = 'CONTEXT_PROTECTED'
          AND is_active = TRUE;
        """
        return self.db_pool.fetch(query)
    
    def validate_matches(self, matches, text):
        """验证匹配结果，剔除边界内误匹配"""
        protected_ranges = self.find_protected_ranges(text)
        return [m for m in matches if not self.in_protected_range(m, protected_ranges)]
```

## 高性能匹配算法实现 (v2.0核心创新)

> **注意**：本章节包含的应用层算法实现细节（类/方法/流程示例）已迁移至匹配引擎文档：.kiro/steering/pattern-matching-engine-guide.md。本设计文档仅保留与数据库相关的接口契约、关键查询与索引配置。

### DB侧要点

以下为"高性能匹配算法实现"在数据库层面的关键支撑点：

1. **模式数据装载与排序**
   ```sql
   SELECT pattern_name, regex_string, metadata, priority, service_code, related_service_id
   FROM regex_patterns
   WHERE is_active = TRUE AND validation_status = 'valid'
   ORDER BY priority DESC, id ASC;
   ```

2. **复合后缀元数据（JSONB）**
   ```sql
   SELECT pattern_name, regex_string, metadata->>'suffixGroup' AS suffix_group
   FROM regex_patterns
   WHERE metadata->>'isCompoundWithSuffix' = 'true' AND is_active = TRUE;
   ```

3. **上下文保护（CONTEXT_PROTECTED）**
   ```sql
   SELECT regex_string, metadata
   FROM regex_patterns
   WHERE pattern_type = 'CONTEXT_PROTECTED' AND is_active = TRUE;
   ```

4. **批量写入与并发优化**
   - 批量插入/更新 regex_patterns 时支持 metadata JSONB
   - 提供批量过程：batch_insert_patterns_optimized(patterns JSONB)
   - 并发 UPSERT：示例 upsert_service_name_safe() 使用 SKIP LOCKED 避免死锁

> **注**：应用层匹配流程与类/方法实现已迁移，DB 设计专注于数据模型、查询、索引与过程。

### 数据清理策略 (v2.0优化版)

**实际实现方式**：基于需求文档的更新，数据库使用按外键依赖顺序的DELETE FROM语句和ALTER SEQUENCE RESTART的可重复执行脚本，确保数据完全清理且序列重置：

```sql
-- 清空现有数据以确保脚本可重复执行
-- 按照外键依赖顺序删除数据，保留表结构和约束
DELETE FROM feedback_submissions;
DELETE FROM translation_jobs;
DELETE FROM regex_patterns;
DELETE FROM url_mappings;
DELETE FROM brand_term_mappings;
DELETE FROM service_names;

-- 重置序列
ALTER SEQUENCE service_names_id_seq RESTART WITH 1;
ALTER SEQUENCE brand_term_mappings_id_seq RESTART WITH 1;
ALTER SEQUENCE url_mappings_id_seq RESTART WITH 1;
ALTER SEQUENCE regex_patterns_id_seq RESTART WITH 1;
```

**优势**:
1. **保留外键约束**: DELETE操作不会影响表结构和约束定义，避免TRUNCATE CASCADE的问题
2. **安全性**: 按照外键依赖顺序删除，避免违反引用完整性
3. **一致性**: 序列重置确保ID从1开始，支持可重复执行
4. **可测试性**: 外键约束保持活跃，确保数据完整性测试有效
5. **维护友好**: 提供标准化的数据清理和重置流程

## Testing Strategy

### 测试策略概述

数据库设计的测试策略采用多层次验证方法，确保数据完整性、性能表现和业务逻辑的正确性，特别关注v2.0版本的新特性验证。

#### 1. 数据完整性测试

**约束验证测试**:
```sql
-- === 基础约束测试 ===

-- 测试1: service_names表的authoritative_full_name唯一约束
INSERT INTO service_names (authoritative_full_name, base_name, full_name_en, short_name_en) 
VALUES ('Test Service Unique', 'Test Service Unique', 'Test Service Unique', 'TSU');
-- 应该成功

INSERT INTO service_names (authoritative_full_name, base_name, full_name_en, short_name_en) 
VALUES ('Test Service Unique', 'Different Base Name', 'Test Service Unique', 'TSU');
-- 应该失败，违反authoritative_full_name唯一约束

-- 测试2: regex_patterns表的pattern_name唯一约束
INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string) 
VALUES ('UNIQUE_PATTERN_TEST', 'SERVICE_NAME', 'test.*pattern');
-- 应该成功

INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string) 
VALUES ('UNIQUE_PATTERN_TEST', 'GENERAL', 'different.*pattern');
-- 应该失败，违反pattern_name唯一约束

-- 测试3: brand_term_mappings表的term_en唯一约束
INSERT INTO brand_term_mappings (term_en, term_cn) 
VALUES ('Unique Test Term', '唯一测试术语');
-- 应该成功

INSERT INTO brand_term_mappings (term_en, term_cn) 
VALUES ('Unique Test Term', '不同的中文翻译');
-- 应该失败，违反term_en唯一约束

-- === 外键约束测试 ===

-- 测试4: regex_patterns表与service_names表的外键关联
-- 首先确保有一个有效的service记录用于测试
INSERT INTO service_names (authoritative_full_name, base_name, full_name_en, short_name_en) 
VALUES ('Test Service for FK', 'Test Service for FK', 'Test Service for FK', 'TSFK') 
ON CONFLICT (authoritative_full_name) DO NOTHING;

-- 测试4a: 测试有效外键（应该成功）
INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id) 
SELECT 'TEST_PATTERN_VALID_FK', 'SERVICE_NAME', 'valid.*test', id 
FROM service_names WHERE authoritative_full_name = 'Test Service for FK';

-- 测试4b: 测试NULL值外键（应该成功，因为外键允许NULL）
INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id) 
VALUES ('TEST_PATTERN_NULL_FK', 'SERVICE_NAME', 'null.*test', NULL);

-- 测试4c: 外键约束验证（修正版 - 避免事务问题）
-- 方法1: 简化版功能测试（推荐）
DO $$
DECLARE
    constraint_exists BOOLEAN;
    test_pattern_name TEXT;
BEGIN
    -- 检查外键约束是否存在
    SELECT EXISTS(
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_type = 'FOREIGN KEY' 
        AND table_name = 'regex_patterns' 
        AND constraint_name = 'fk_related_service'
        AND table_schema = current_schema()
    ) INTO constraint_exists;
    
    IF NOT constraint_exists THEN
        RAISE WARNING 'CRITICAL: Foreign key constraint "fk_related_service" does not exist! 这可能是由TRUNCATE CASCADE操作造成的。';
        RAISE NOTICE 'RECOMMENDATION: 请运行外键约束修复脚本';
        RETURN;
    END IF;
    
    RAISE NOTICE 'INFO: Foreign key constraint "fk_related_service" exists, proceeding with validation test';
    
    -- 生成唯一的测试模式名称
    test_pattern_name := 'FK_TEST_INVALID_' || extract(epoch from now())::text;
    
    -- 测试外键约束功能
    BEGIN
        INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id) 
        VALUES (test_pattern_name, 'SERVICE_NAME', 'invalid.*test', 99999);
        
        -- 如果插入成功，说明外键约束有问题
        RAISE NOTICE '✗ ERROR: regex_patterns 外键约束未正常工作 - 无效的service_id被接受了';
        -- 清理测试数据
        DELETE FROM regex_patterns WHERE pattern_name = test_pattern_name;
        
    EXCEPTION
        WHEN foreign_key_violation THEN
            RAISE NOTICE '✓ SUCCESS: regex_patterns 外键约束工作正常 - 正确拒绝了无效的外键引用';
        WHEN unique_violation THEN
            RAISE NOTICE '! NOTE: 模式名称冲突，但外键约束正常（这通常不应该发生）';
    END;
END $$;

-- 测试4c-alternative: 分步测试方法（如果DO块仍有問題時使用）
-- 注意：下面的INSERT語句应该失败並返回外键違規錯誤
-- INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id) 
-- VALUES ('FK_TEST_STEP_BY_STEP', 'SERVICE_NAME', 'test.*pattern', 99999);
-- 如果上述INSERT成功執行而沒有錯誤，說明外鍵約束有問題
-- 清理測試數據: DELETE FROM regex_patterns WHERE pattern_name = 'FK_TEST_STEP_BY_STEP';

-- 测试4d: 外键约束修复（如果约束被意外删除）
DO $$
DECLARE
    constraint_exists BOOLEAN;
BEGIN
    -- 检查外键约束是否存在
    SELECT EXISTS(
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_type = 'FOREIGN KEY' 
        AND table_name = 'regex_patterns' 
        AND constraint_name = 'fk_related_service'
    ) INTO constraint_exists;
    
    IF NOT constraint_exists THEN
        RAISE NOTICE 'WARNING: Foreign key constraint missing, attempting to recreate...';
        
        -- 重新创建外键约束
        ALTER TABLE regex_patterns 
        ADD CONSTRAINT fk_related_service 
        FOREIGN KEY(related_service_id) 
        REFERENCES service_names(id) 
        ON DELETE SET NULL;
        
        RAISE NOTICE 'INFO: Foreign key constraint recreated successfully';
        
        -- 重新测试约束
        BEGIN
            INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id) 
            VALUES ('TEST_PATTERN_FK_RETEST', 'SERVICE_NAME', 'retest.*pattern', 99999);
            RAISE EXCEPTION 'ERROR: Recreated foreign key constraint is not working';
        EXCEPTION
            WHEN foreign_key_violation THEN
                RAISE NOTICE 'SUCCESS: Recreated foreign key constraint is working correctly';
        END;
    ELSE
        RAISE NOTICE 'INFO: Foreign key constraint exists and should be working';
    END IF;
END $$;

-- 测试5: feedback_submissions表与translation_jobs表的外键关联
-- 创建测试用的translation_job
INSERT INTO translation_jobs (original_text, submitted_by) 
VALUES ('Test translation for FK constraint', 'test_user');

-- 测试5a: 有效外键关联（应该成功）
INSERT INTO feedback_submissions (translation_job_id, page_url, satisfaction, s3_object_key)
SELECT id, 'https://test.example.com', 'satisfied', 'test/feedback/valid_fk.json'
FROM translation_jobs WHERE original_text = 'Test translation for FK constraint' LIMIT 1;

-- 测试5b: 无效外键关联测试（修正版）
DO $$
DECLARE
    test_s3_key TEXT;
BEGIN
    test_s3_key := 'test/feedback/invalid_fk_' || extract(epoch from now())::text || '.json';
    
    BEGIN
        INSERT INTO feedback_submissions (translation_job_id, page_url, satisfaction, s3_object_key)
        VALUES ('00000000-0000-0000-0000-000000000000', 'https://test.example.com', 'unsatisfied', test_s3_key);
        
        -- 如果插入成功，说明外键约束有问题
        RAISE NOTICE '✗ ERROR: feedback_submissions 外键约束未正常工作 - 无效的translation_job_id被接受了';
        -- 清理测试数据
        DELETE FROM feedback_submissions WHERE s3_object_key = test_s3_key;
        
    EXCEPTION
        WHEN foreign_key_violation THEN
            RAISE NOTICE '✓ SUCCESS: feedback_submissions 外键约束工作正常';
        WHEN unique_violation THEN
            RAISE NOTICE '! NOTE: S3对象键可能冲突，但外键约束正常';
    END;
END $$;

-- 测试5b-alternative: 分步测试方法（如果DO块有問題時使用）
-- 注意：下面的INSERT語句应该失败並返回外键違規錯誤
-- INSERT INTO feedback_submissions (translation_job_id, page_url, satisfaction, s3_object_key)
-- VALUES ('00000000-0000-0000-0000-000000000000', 'https://test.example.com', 'unsatisfied', 'test/feedback/step_by_step.json');
-- 如果上述INSERT成功執行而沒有錯誤，說明外鍵約束有問題
-- 清理測試數據: DELETE FROM feedback_submissions WHERE s3_object_key = 'test/feedback/step_by_step.json';

-- === 枚举约束测试 ===

-- 测试6: translation_job_status枚举约束（修正版）
DO $$
DECLARE
    test_job_text TEXT;
BEGIN
    test_job_text := 'test text for enum validation ' || extract(epoch from now())::text;
    
    BEGIN
        INSERT INTO translation_jobs (status, original_text) 
        VALUES ('invalid_status', test_job_text);
        
        -- 如果插入成功，说明枚举约束有问题
        RAISE NOTICE '✗ ERROR: translation_job_status 枚举约束未正常工作 - 无效的status被接受了';
        -- 清理测试数据
        DELETE FROM translation_jobs WHERE original_text = test_job_text;
        
    EXCEPTION
        WHEN invalid_text_representation THEN
            RAISE NOTICE '✓ SUCCESS: translation_job_status 枚举约束工作正常';
    END;
END $$;

-- 测试7: rule_source枚举约束（修正版）
DO $$
DECLARE
    test_service_name TEXT;
BEGIN
    test_service_name := 'Test Enum Service ' || extract(epoch from now())::text;
    
    BEGIN
        INSERT INTO service_names (authoritative_full_name, base_name, full_name_en, short_name_en, source) 
        VALUES (test_service_name, test_service_name, test_service_name, 'TES', 'invalid_source');
        
        -- 如果插入成功，说明枚举约束有问题
        RAISE NOTICE '✗ ERROR: rule_source 枚举约束未正常工作 - 无效的source被接受了';
        -- 清理测试数据
        DELETE FROM service_names WHERE authoritative_full_name = test_service_name;
        
    EXCEPTION
        WHEN invalid_text_representation THEN
            RAISE NOTICE '✓ SUCCESS: rule_source 枚举约束工作正常';
    END;
END $$;

-- 测试8: feedback_satisfaction枚举约束（修正版）
DO $$
DECLARE
    test_s3_key TEXT;
BEGIN
    test_s3_key := 'test/feedback/enum_test_' || extract(epoch from now())::text || '.json';
    
    BEGIN
        INSERT INTO feedback_submissions (page_url, satisfaction, s3_object_key)
        VALUES ('https://test.example.com', 'invalid_satisfaction', test_s3_key);
        
        -- 如果插入成功，说明枚举约束有问题
        RAISE NOTICE '✗ ERROR: feedback_satisfaction 枚举约束未正常工作 - 无效的satisfaction被接受了';
        -- 清理测试数据
        DELETE FROM feedback_submissions WHERE s3_object_key = test_s3_key;
        
    EXCEPTION
        WHEN invalid_text_representation THEN
            RAISE NOTICE '✓ SUCCESS: feedback_satisfaction 枚举约束工作正常';
    END;
END $$;

-- === 外键约束系统状态诊断 ===

-- 测试9: 验证所有外键约束定义
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints AS rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = current_schema()
ORDER BY tc.table_name, tc.constraint_name;

-- 测试10: 验证所有枚举类型定义
SELECT 
    t.typname AS enum_name,
    e.enumlabel AS enum_value,
    e.enumsortorder AS sort_order
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid  
WHERE t.typname IN ('translation_job_status', 'rule_source', 'feedback_satisfaction', 'regex_pattern_type')
ORDER BY t.typname, e.enumsortorder;

-- === 数据库架构问题诊断和修复 ===

-- 测试11: 检查TRUNCATE CASCADE对外键约束的影响
DO $$
DECLARE
    fk_count INTEGER;
    missing_constraints TEXT[] := ARRAY[]::TEXT[];
BEGIN
    -- 检查所有预期的外键约束是否存在
    SELECT COUNT(*) INTO fk_count
    FROM information_schema.table_constraints 
    WHERE constraint_type = 'FOREIGN KEY' 
    AND table_schema = current_schema();
    
    RAISE NOTICE 'INFO: Found % foreign key constraints in current schema', fk_count;
    
    -- 检查specific外键约束
    IF NOT EXISTS(
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_type = 'FOREIGN KEY' 
        AND table_name = 'regex_patterns' 
        AND constraint_name = 'fk_related_service'
    ) THEN
        missing_constraints := array_append(missing_constraints, 'regex_patterns.fk_related_service');
    END IF;
    
    IF NOT EXISTS(
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_type = 'FOREIGN KEY' 
        AND table_name = 'feedback_submissions' 
        AND constraint_name = 'fk_translation_job'
    ) THEN
        missing_constraints := array_append(missing_constraints, 'feedback_submissions.fk_translation_job');
    END IF;
    
    IF array_length(missing_constraints, 1) > 0 THEN
        RAISE WARNING 'CRITICAL: Missing foreign key constraints: %', array_to_string(missing_constraints, ', ');
        RAISE WARNING 'This is likely caused by TRUNCATE CASCADE in the database schema script.';
        RAISE WARNING 'RECOMMENDATION: Replace TRUNCATE CASCADE with DELETE operations to preserve constraints.';
    ELSE
        RAISE NOTICE 'SUCCESS: All expected foreign key constraints are present';
    END IF;
END $$;

-- 测试12: 数据库架构修复建议
DO $$
BEGIN
    RAISE NOTICE '=== DATABASE SCHEMA IMPROVEMENT RECOMMENDATIONS ===';
    RAISE NOTICE '1. TRUNCATE CASCADE Issue:';
    RAISE NOTICE '   Current: TRUNCATE TABLE ... RESTART IDENTITY CASCADE;';
    RAISE NOTICE '   Problem: May remove foreign key constraints';
    RAISE NOTICE '   Solution: Use DELETE FROM instead of TRUNCATE CASCADE';
    RAISE NOTICE '';
    RAISE NOTICE '2. Recommended replacement in schema script:';
    RAISE NOTICE '   -- DELETE FROM feedback_submissions;';
    RAISE NOTICE '   -- DELETE FROM translation_jobs;';
    RAISE NOTICE '   -- DELETE FROM regex_patterns;';
    RAISE NOTICE '   -- DELETE FROM url_mappings;';
    RAISE NOTICE '   -- DELETE FROM brand_term_mappings;';
    RAISE NOTICE '   -- DELETE FROM service_names;';
    RAISE NOTICE '   -- ALTER SEQUENCE service_names_id_seq RESTART WITH 1;';
    RAISE NOTICE '   -- ALTER SEQUENCE brand_term_mappings_id_seq RESTART WITH 1;';
    RAISE NOTICE '   -- ALTER SEQUENCE url_mappings_id_seq RESTART WITH 1;';
    RAISE NOTICE '   -- ALTER SEQUENCE regex_patterns_id_seq RESTART WITH 1;';
    RAISE NOTICE '';
    RAISE NOTICE '3. This preserves foreign key constraints while clearing data';
END $$;

-- === 清理测试数据 ===
-- 清理测试中创建的临时数据（按外键依赖顺序）
-- 注意：修正版测试使用时间戳确保唯一性，大部分数据在测试失败时会自动清理

DELETE FROM feedback_submissions WHERE s3_object_key LIKE 'test/feedback/%';
DELETE FROM translation_jobs WHERE original_text LIKE 'Test%' OR original_text LIKE '%enum validation%';
DELETE FROM regex_patterns WHERE pattern_name LIKE 'TEST_%' OR pattern_name LIKE 'UNIQUE_%' OR pattern_name LIKE 'FK_TEST_%';
DELETE FROM brand_term_mappings WHERE term_en LIKE '%Test%';
DELETE FROM service_names WHERE authoritative_full_name LIKE 'Test%';

-- === 测试修正说明 ===
/*
修正版测试的主要改进：
1. 解决 "there is no transaction in progress" 错误
2. 使用时间戳生成唯一的测试数据，避免冲突
3. 在DO块中使用嵌套BEGIN-EXCEPTION处理，避免事务问题
4. 提供分步测试方法作为备选方案
5. 改进错误消息，使用视觉指示符（✓ ✗ !）
6. 自动清理失败的测试数据

推荐测试执行顺序：
1. 先运行系统表查询，确认约束存在
2. 运行修正版DO块测试
3. 如果DO块仍有问题，使用分步测试方法
4. 最后运行清理脚本
*/
```

**JSONB数据验证测试**:
```python
def test_jsonb_data_integrity():
    """测试JSONB字段的数据完整性"""
    
    # 测试placeholder_map结构
    valid_placeholder_map = {
        "__SRVCNM_0__": "Amazon EC2",
        "__CLICMD_0__": "aws ec2 describe-instances"
    }
    
    invalid_placeholder_map = {
        "invalid_key": "value"  # 不符合占位符格式
    }
    
    # 测试service_mention_state结构
    valid_mention_state = {
        "Amazon EC2": {
            "mentioned": True,
            "usedFullName": True,
            "firstMentionPosition": 10
        }
    }
    
    # 验证数据插入和查询
    assert validate_jsonb_structure(valid_placeholder_map)
    assert not validate_jsonb_structure(invalid_placeholder_map)
```

#### 2. 性能测试

**索引效率测试**:
```sql
-- 测试优先级查询性能
EXPLAIN ANALYZE 
SELECT regex_string, priority 
FROM regex_patterns 
WHERE is_active = true 
ORDER BY priority DESC, id ASC 
LIMIT 50;

-- 测试JSONB查询性能
EXPLAIN ANALYZE 
SELECT id, placeholder_map 
FROM translation_jobs 
WHERE placeholder_map ? '__SRVCNM_0__';

-- 测试复合索引效率
EXPLAIN ANALYZE 
SELECT * FROM regex_patterns 
WHERE pattern_type = 'SERVICE_NAME' 
AND is_active = true;
```

**并发性能测试**:
```python
def test_concurrent_operations():
    """测试并发操作的性能和一致性"""
    
    import concurrent.futures
    import threading
    
    def concurrent_insert_test():
        """并发插入测试"""
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = []
            
            for i in range(100):
                future = executor.submit(
                    insert_translation_job, 
                    f"test text {i}", 
                    f"user_{i}"
                )
                futures.append(future)
            
            # 等待所有任务完成
            results = [future.result() for future in futures]
            
            # 验证所有插入都成功
            assert all(results)
            
            # 验证数据一致性
            job_count = count_translation_jobs()
            assert job_count >= 100
    
    def concurrent_update_test():
        """并发更新测试"""
        job_id = create_test_translation_job()
        
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = []
            
            # 并发更新不同阶段
            stages = ['stage1', 'stage2', 'stage3']
            for stage in stages:
                future = executor.submit(
                    update_job_stage, 
                    job_id, 
                    stage, 
                    f"content for {stage}"
                )
                futures.append(future)
            
            # 等待所有更新完成
            results = [future.result() for future in futures]
            
            # 验证最终状态一致性
            final_job = get_translation_job(job_id)
            assert final_job.stage3_final_cn is not None
```

#### 3. 业务逻辑测试

**4阶段翻译流水线测试**:
```python
def test_translation_pipeline():
    """测试完整的4阶段翻译流水线"""
    
    # 准备测试数据
    original_text = """
    We detected an issue with some EC2 P3 instances in your Amazon VPC. 
    Please check the AWS console at https://console.aws.amazon.com/ec2/ 
    and run: aws ec2 describe-instances --region us-west-2
    """
    
    # Stage 0: 创建翻译任务
    job_id = create_translation_job(original_text, "test_user")
    assert job_id is not None
    
    # Stage 1: 文本预处理标准化
    stage1_result = process_stage1_standardization(job_id)
    
    # 验证服务名称标准化
    assert "Amazon Elastic Compute Cloud (EC2) P3 instances" in stage1_result
    assert "Amazon Virtual Private Cloud (VPC)" in stage1_result
    
    # 验证占位符生成
    assert "__CLICMD_0__" in stage1_result
    assert "__URL_0__" in stage1_result
    
    # Stage 2: LLM翻译
    stage2_result = process_stage2_llm_translation(job_id)
    
    # 验证占位符保持不变
    assert "__CLICMD_0__" in stage2_result
    assert "__URL_0__" in stage2_result
    
    # Stage 3: 最终中文输出
    stage3_result = process_stage3_finalization(job_id)
    
    # 验证占位符恢复
    assert "aws ec2 describe-instances --region cn-north-1" in stage3_result
    assert "https://console.amazonaws.com.cn/ec2/" in stage3_result
    
    # 验证品牌术语替换
    assert "亚马逊云科技控制台" in stage3_result
    
    # 验证任务状态
    final_job = get_translation_job(job_id)
    assert final_job.status == 'completed'
    assert final_job.processing_time_ms > 0
```

**正则表达式模式测试**:
```python
def test_regex_pattern_matching():
    """测试8种正则表达式模式类型的匹配效果"""
    
    test_cases = [
        {
            'text': 'Amazon Elastic Compute Cloud (EC2) P3 instances',
            'expected_pattern': 'EC2_FULL_COMPLEX_SUFFIX',
            'expected_suffix': 'P3 instances'
        },
        {
            'text': 'Amazon EC2 instance family',
            'expected_pattern': 'EC2_SHORT_COMPLEX_SUFFIX',
            'expected_suffix': 'instance family'
        },
        {
            'text': 'EC2 instances',
            'expected_pattern': 'EC2_ACRONYM_COMPLEX_SUFFIX',
            'expected_suffix': 'instances'
        },
        {
            'text': 'arn:aws:ec2:us-west-2:123456789012:instance/i-1234567890abcdef0',
            'expected_pattern': None,  # 应该被ARN保护机制排除
            'expected_suffix': None
        }
    ]
    
    for test_case in test_cases:
        result = match_service_patterns(test_case['text'])
        
        if test_case['expected_pattern']:
            assert result['matched_pattern'] == test_case['expected_pattern']
            if test_case['expected_suffix']:
                assert result['captured_suffix'] == test_case['expected_suffix']
        else:
            assert result is None or result['matched_pattern'] is None
```

#### 4. 集成测试

**端到端系统测试**:
```python
def test_end_to_end_system():
    """端到端系统集成测试"""
    
    # 1. 数据同步测试
    sync_result = trigger_service_sync()
    assert sync_result['success'] == True
    assert sync_result['processed_count'] > 0
    
    # 2. 模式生成测试
    pattern_result = generate_regex_patterns()
    assert pattern_result['generated_count'] > 0
    assert pattern_result['validation_errors'] == 0
    
    # 3. 翻译流水线测试
    translation_result = process_full_translation(
        "Test EC2 instances in VPC"
    )
    assert translation_result['status'] == 'completed'
    assert '实例' in translation_result['final_text']
    
    # 4. 反馈收集测试
    feedback_result = submit_user_feedback(
        translation_result['job_id'],
        'satisfied',
        'Translation quality is good'
    )
    assert feedback_result['s3_key'] is not None
    
    # 5. 数据一致性验证
    consistency_check = validate_data_consistency()
    assert consistency_check['issues'] == []
```

**性能基准测试**:
```python
def test_performance_benchmarks():
    """性能基准测试"""
    
    benchmarks = {
        'translation_throughput': {
            'target': 100,  # 每分钟100个翻译任务
            'unit': 'jobs/minute'
        },
        'pattern_matching_latency': {
            'target': 50,   # 50毫秒内完成模式匹配
            'unit': 'milliseconds'
        },
        'database_query_performance': {
            'target': 100,  # 100毫秒内完成复杂查询
            'unit': 'milliseconds'
        }
    }
    
    for benchmark_name, config in benchmarks.items():
        actual_performance = measure_performance(benchmark_name)
        assert actual_performance <= config['target'], \
            f"{benchmark_name} performance {actual_performance} {config['unit']} " \
            f"exceeds target {config['target']} {config['unit']}"
```

### 测试数据管理

**测试数据生成**:
```python
def generate_test_data():
    """生成测试数据"""
    
    # 生成测试服务数据
    test_services = [
        {
            'authoritative_full_name': 'Test Service 1',
            'base_name': 'Test Service 1',
            'full_name_en': 'Test Service 1',
            'short_name_en': 'TS1',
            'service_code': 'ts1'
        },
        # ... 更多测试服务
    ]
    
    # 生成测试正则表达式模式
    test_patterns = generate_test_patterns(test_services)
    
    # 生成测试翻译任务
    test_jobs = generate_test_translation_jobs(100)
    
    return {
        'services': test_services,
        'patterns': test_patterns,
        'jobs': test_jobs
    }

def cleanup_test_data():
    """清理测试数据"""
    
    # 删除测试数据，保持生产数据不变
    delete_test_services()
    delete_test_patterns()
    delete_test_jobs()
```

通过这个全面的测试策略，我们确保数据库设计能够满足所有业务需求，同时保持高性能和高可靠性。测试覆盖了从基础的数据完整性到复杂的业务逻辑，从单元测试到端到端集成测试的完整范围。