# API测试命令集合 - 已解决中文乱码问题

## 概述
本文档包含针对内部LLM API服务器的完整测试命令，已解决中文编码显示问题。

**API服务器**: `http://internal-ai-tao-llm-apiserver-dev-1126944677.cn-northwest-1.elb.amazonaws.com.cn`

**认证Token**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwcm9qZWN0IjoidGVzdCJ9.ebcS-y4S0g9qAaBh_GHfagWOgLKrySGAwvUJsJb2Ak8`

## 快速测试命令

### 1. 测试模型列表API
```powershell
curl.exe -X GET "http://internal-ai-tao-llm-apiserver-dev-1126944677.cn-northwest-1.elb.amazonaws.com.cn/v1/models" -H "accept: application/json" -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwcm9qZWN0IjoidGVzdCJ9.ebcS-y4S0g9qAaBh_GHfagWOgLKrySGAwvUJsJb2Ak8" -w "Status: %{http_code}\n"
```

### 2. 测试Tokenize API
```powershell
curl.exe -X POST "http://internal-ai-tao-llm-apiserver-dev-1126944677.cn-northwest-1.elb.amazonaws.com.cn/v1/tokenize" -H "accept: application/json" -H "Content-Type: application/json" -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwcm9qZWN0IjoidGVzdCJ9.ebcS-y4S0g9qAaBh_GHfagWOgLKrySGAwvUJsJb2Ak8" --data-raw "{\"messages\":[{\"role\":\"user\",\"content\":\"Hello world\"}]}" -w "Status: %{http_code}\n"
```

### 3. 测试Invocations API
```powershell
curl.exe -X POST "http://internal-ai-tao-llm-apiserver-dev-1126944677.cn-northwest-1.elb.amazonaws.com.cn/v1/invocations" -H "accept: application/json" -H "Content-Type: application/json" -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwcm9qZWN0IjoidGVzdCJ9.ebcS-y4S0g9qAaBh_GHfagWOgLKrySGAwvUJsJb2Ak8" --data-raw "{\"messages\":[{\"role\":\"user\",\"content\":\"Hello world\"}],\"temperature\":0.3,\"max_tokens\":100}" -w "Status: %{http_code}\n"
```

## 解决中文乱码的PowerShell脚本

### 方法1: 改进的curl测试脚本（推荐）
```powershell
# 设置PowerShell编码
$PSDefaultParameterValues['*:Encoding'] = 'utf8'
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# API配置
$baseUrl = "http://internal-ai-tao-llm-apiserver-dev-1126944677.cn-northwest-1.elb.amazonaws.com.cn"
$token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwcm9qZWN0IjoidGVzdCJ9.ebcS-y4S0g9qAaBh_GHfagWOgLKrySGAwvUJsJb2Ak8"

# 创建测试请求
$invokeMessage = @{
    messages = @(
        @{
            role = "system"
            content = "请将以下英文翻译成中文"
        },
        @{
            role = "user"
            content = "Hello world, this is a test message."
        }
    )
    temperature = 0.3
    max_tokens = 1000
} | ConvertTo-Json -Depth 10

# 保存请求到临时文件
$tempFile = [System.IO.Path]::GetTempFileName()
[System.IO.File]::WriteAllText($tempFile, $invokeMessage, [System.Text.Encoding]::UTF8)

# 保存响应到文件以正确处理编码
$responseFile = [System.IO.Path]::GetTempFileName()

Write-Host "正在调用API..." -ForegroundColor Green

# 调用API并保存响应到文件
curl.exe -X POST "$baseUrl/v1/invocations" `
  -H "accept: application/json" `
  -H "Content-Type: application/json; charset=utf-8" `
  -H "Authorization: Bearer $token" `
  --data-binary "@$tempFile" `
  --output "$responseFile" `
  --silent --show-error

# 读取并正确显示响应
if (Test-Path $responseFile) {
    $responseContent = [System.IO.File]::ReadAllText($responseFile, [System.Text.Encoding]::UTF8)
    
    Write-Host "`n=== API响应 ===" -ForegroundColor Green
    
    # 解析JSON并美化显示
    try {
        $jsonResponse = $responseContent | ConvertFrom-Json
        
        Write-Host "状态: 成功" -ForegroundColor Green
        Write-Host "模型响应内容:" -ForegroundColor Yellow
        Write-Host $jsonResponse.choices[0].message.content -ForegroundColor White
        
        Write-Host "`nToken使用情况:" -ForegroundColor Cyan
        Write-Host "  提示Token: $($jsonResponse.usage.prompt_tokens)"
        Write-Host "  完成Token: $($jsonResponse.usage.completion_tokens)" 
        Write-Host "  总Token: $($jsonResponse.usage.total_tokens)"
        
        Write-Host "`n完整JSON响应:" -ForegroundColor Gray
        $jsonResponse | ConvertTo-Json -Depth 10 | Write-Host
        
    } catch {
        Write-Host "JSON解析失败，原始响应:" -ForegroundColor Red
        Write-Host $responseContent
    }
} else {
    Write-Host "响应文件不存在" -ForegroundColor Red
}

# 清理临时文件
Remove-Item $tempFile, $responseFile -ErrorAction SilentlyContinue
```

### 方法2: 使用Invoke-RestMethod（更简洁）
```powershell
# 设置编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# API配置
$baseUrl = "http://internal-ai-tao-llm-apiserver-dev-1126944677.cn-northwest-1.elb.amazonaws.com.cn"
$token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwcm9qZWN0IjoidGVzdCJ9.ebcS-y4S0g9qAaBh_GHfagWOgLKrySGAwvUJsJb2Ak8"

# 请求头
$headers = @{
    "accept" = "application/json"
    "Content-Type" = "application/json; charset=utf-8"
    "Authorization" = "Bearer $token"
}

# 请求体
$body = @{
    messages = @(
        @{
            role = "system"
            content = "请将以下英文翻译成中文"
        },
        @{
            role = "user"
            content = "Hello world, this is a test message."
        }
    )
    temperature = 0.3
    max_tokens = 1000
} | ConvertTo-Json -Depth 10

try {
    Write-Host "正在调用API..." -ForegroundColor Green
    
    $response = Invoke-RestMethod -Uri "$baseUrl/v1/invocations" `
                                  -Method POST `
                                  -Headers $headers `
                                  -Body ([System.Text.Encoding]::UTF8.GetBytes($body)) `
                                  -ContentType "application/json; charset=utf-8"
    
    Write-Host "`n=== API调用成功 ===" -ForegroundColor Green
    Write-Host "翻译结果: " -ForegroundColor Yellow -NoNewline
    Write-Host $response.choices[0].message.content -ForegroundColor White
    
    Write-Host "`nToken统计:" -ForegroundColor Cyan
    Write-Host "  输入: $($response.usage.prompt_tokens) tokens"
    Write-Host "  输出: $($response.usage.completion_tokens) tokens"
    Write-Host "  总计: $($response.usage.total_tokens) tokens"
    
} catch {
    Write-Host "API调用失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "HTTP状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}
```

## 完整的API测试套件

### 保存为test-api.ps1文件
```powershell
# 完整的API测试脚本
param(
    [string]$TestType = "all"  # all, models, tokenize, invoke
)

# 设置编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$PSDefaultParameterValues['*:Encoding'] = 'utf8'

# API配置
$baseUrl = "http://internal-ai-tao-llm-apiserver-dev-1126944677.cn-northwest-1.elb.amazonaws.com.cn"
$token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwcm9qZWN0IjoidGVzdCJ9.ebcS-y4S0g9qAaBh_GHfagWOgLKrySGAwvUJsJb2Ak8"

$headers = @{
    "accept" = "application/json"
    "Content-Type" = "application/json; charset=utf-8"
    "Authorization" = "Bearer $token"
}

function Test-ModelsAPI {
    Write-Host "`n=== 测试模型列表API ===" -ForegroundColor Magenta
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/v1/models" -Method GET -Headers $headers
        Write-Host "✅ 模型API调用成功" -ForegroundColor Green
        
        if ($response.data) {
            Write-Host "可用模型:" -ForegroundColor Yellow
            $response.data | ForEach-Object { 
                Write-Host "  - $($_.id)" -ForegroundColor White
            }
        } else {
            Write-Host "模型列表: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor White
        }
        return $true
    } catch {
        Write-Host "❌ 模型API调用失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-TokenizeAPI {
    Write-Host "`n=== 测试Tokenize API ===" -ForegroundColor Magenta
    
    $body = @{
        messages = @(
            @{
                role = "system"
                content = "你是一个有用的助手"
            },
            @{
                role = "user"
                content = "请翻译：Hello world"
            }
        )
    } | ConvertTo-Json -Depth 10
    
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/v1/tokenize" `
                                      -Method POST `
                                      -Headers $headers `
                                      -Body ([System.Text.Encoding]::UTF8.GetBytes($body))
        
        Write-Host "✅ Tokenize API调用成功" -ForegroundColor Green
        Write-Host "Token数量: $($response.count)" -ForegroundColor Yellow
        Write-Host "模型最大长度: $($response.max_model_len)" -ForegroundColor Yellow
        return $response
    } catch {
        Write-Host "❌ Tokenize API调用失败: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

function Test-InvokeAPI {
    Write-Host "`n=== 测试Invocations API ===" -ForegroundColor Magenta
    
    $body = @{
        messages = @(
            @{
                role = "system"
                content = "请将以下英文翻译成中文，保持简洁准确"
            },
            @{
                role = "user"
                content = "Hello world, this is a test message for API validation."
            }
        )
        temperature = 0.3
        max_tokens = 500
    } | ConvertTo-Json -Depth 10
    
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/v1/invocations" `
                                      -Method POST `
                                      -Headers $headers `
                                      -Body ([System.Text.Encoding]::UTF8.GetBytes($body))
        
        Write-Host "✅ Invocations API调用成功" -ForegroundColor Green
        Write-Host "翻译结果: " -ForegroundColor Yellow -NoNewline
        Write-Host "`"$($response.choices[0].message.content)`"" -ForegroundColor White
        
        Write-Host "`nToken使用统计:" -ForegroundColor Cyan
        Write-Host "  输入Token: $($response.usage.prompt_tokens)"
        Write-Host "  输出Token: $($response.usage.completion_tokens)"
        Write-Host "  总Token: $($response.usage.total_tokens)"
        Write-Host "  完成原因: $($response.choices[0].finish_reason)"
        
        return $response
    } catch {
        Write-Host "❌ Invocations API调用失败: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

function Test-ChineseTranslation {
    Write-Host "`n=== 测试中文翻译功能 ===" -ForegroundColor Magenta
    
    $body = @{
        messages = @(
            @{
                role = "system"
                content = "## 英文术语标准化规则`n请将AWS服务名称标准化，然后翻译成中文"
            },
            @{
                role = "user"
                content = "We are investigating increased API error rates for DynamoDB Access Control APIs in the us-east-1 Region. Time: December 9, 2024, 12:01 AM PDT."
            }
        )
        temperature = 0.3
        max_tokens = 800
    } | ConvertTo-Json -Depth 10
    
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/v1/invocations" `
                                      -Method POST `
                                      -Headers $headers `
                                      -Body ([System.Text.Encoding]::UTF8.GetBytes($body))
        
        Write-Host "✅ 中文翻译测试成功" -ForegroundColor Green
        Write-Host "原文: We are investigating increased API error rates for DynamoDB..." -ForegroundColor Gray
        Write-Host "翻译结果: " -ForegroundColor Yellow
        Write-Host $response.choices[0].message.content -ForegroundColor White
        
        return $response
    } catch {
        Write-Host "❌ 中文翻译测试失败: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# 执行测试
Write-Host "🚀 开始API测试..." -ForegroundColor Blue
Write-Host "目标服务器: $baseUrl" -ForegroundColor Gray

switch ($TestType.ToLower()) {
    "models" { Test-ModelsAPI }
    "tokenize" { Test-TokenizeAPI }
    "invoke" { Test-InvokeAPI }
    "chinese" { Test-ChineseTranslation }
    "all" {
        $modelsOk = Test-ModelsAPI
        $tokenizeResult = Test-TokenizeAPI
        $invokeResult = Test-InvokeAPI
        $chineseResult = Test-ChineseTranslation
        
        Write-Host "`n=== 测试总结 ===" -ForegroundColor Blue
        Write-Host "模型API: $(if($modelsOk){'✅ 通过'}else{'❌ 失败'})" -ForegroundColor $(if($modelsOk){'Green'}else{'Red'})
        Write-Host "Tokenize API: $(if($tokenizeResult){'✅ 通过'}else{'❌ 失败'})" -ForegroundColor $(if($tokenizeResult){'Green'}else{'Red'})
        Write-Host "Invocations API: $(if($invokeResult){'✅ 通过'}else{'❌ 失败'})" -ForegroundColor $(if($invokeResult){'Green'}else{'Red'})
        Write-Host "中文翻译: $(if($chineseResult){'✅ 通过'}else{'❌ 失败'})" -ForegroundColor $(if($chineseResult){'Green'}else{'Red'})
    }
}

Write-Host "`n🎉 测试完成！" -ForegroundColor Blue
```

## 使用方法

### 1. 快速单行测试
直接复制粘贴上面的curl命令到PowerShell中运行。

### 2. 使用PowerShell脚本（推荐）
复制方法1或方法2的代码到PowerShell中运行，可以正确显示中文。

### 3. 完整测试套件
```powershell
# 保存完整测试脚本为test-api.ps1，然后运行：

# 测试所有API
.\test-api.ps1 -TestType all

# 只测试特定API
.\test-api.ps1 -TestType invoke
.\test-api.ps1 -TestType chinese
```

## 预期结果

### 成功响应示例
```json
{
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "你好，世界，这是一条测试信息。"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 33,
    "completion_tokens": 11,
    "total_tokens": 44
  }
}
```

### 错误排查
- **400错误**: 检查JSON格式
- **401错误**: 检查认证Token
- **500错误**: 服务器内部错误
- **中文乱码**: 使用本文档提供的编码解决方案

## 注意事项

1. **编码设置**: 必须正确设置PowerShell的UTF-8编码
2. **JSON格式**: 确保JSON格式正确，特别是引号转义
3. **Token有效性**: 确认认证Token未过期
4. **网络连接**: 确保能访问内部API服务器
5. **中文显示**: 使用文件方式或Invoke-RestMethod避免乱码

# 修复JSON转义问题的API测试命令

## 问题分析
400错误原因：PowerShell中JSON字符串的转义处理有问题
- 错误信息：`JSON decode error: Expecting property name enclosed in double quotes`
- 根本原因：双引号转义在PowerShell中被错误处理

## 解决方案

### 方法1: 使用单引号包围JSON (推荐)
```powershell
curl.exe -X POST "http://internal-ai-tao-llm-apiserver-dev-1126944677.cn-northwest-1.elb.amazonaws.com.cn/v1/invocations" -H "accept: application/json" -H "Content-Type: application/json" -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwcm9qZWN0IjoidGVzdCJ9.ebcS-y4S0g9qAaBh_GHfagWOgLKrySGAwvUJsJb2Ak8" -d '{\"messages\":[{\"role\":\"user\",\"content\":\"Hello\"}],\"temperature\":0.3,\"max_tokens\":50}' -w "Status: %{http_code}\n"
```

### 方法2: 使用文件方式 (最可靠)
```powershell
# 先创建JSON文件
echo '{"messages":[{"role":"user","content":"Hello"}],"temperature":0.3,"max_tokens":50}' > test_payload.json

# 然后使用文件
curl.exe -X POST "http://internal-ai-tao-llm-apiserver-dev-1126944677.cn-northwest-1.elb.amazonaws.com.cn/v1/invocations" -H "accept: application/json" -H "Content-Type: application/json" -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwcm9qZWN0IjoidGVzdCJ9.ebcS-y4S0g9qAaBh_GHfagWOgLKrySGAwvUJsJb2Ak8" -d @test_payload.json -w "Status: %{http_code}\n"
```

### 方法3: 使用PowerShell变量
```powershell
$json = '{"messages":[{"role":"user","content":"Hello"}],"temperature":0.3,"max_tokens":50}'
curl.exe -X POST "http://internal-ai-tao-llm-apiserver-dev-1126944677.cn-northwest-1.elb.amazonaws.com.cn/v1/invocations" -H "accept: application/json" -H "Content-Type: application/json" -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwcm9qZWN0IjoidGVzdCJ9.ebcS-y4S0g9qAaBh_GHfagWOgLKrySGAwvUJsJb2Ak8" -d $json -w "Status: %{http_code}\n"
```

### 方法4: 简化的测试命令
```powershell
# 最简单的测试
curl.exe -X POST "http://internal-ai-tao-llm-apiserver-dev-1126944677.cn-northwest-1.elb.amazonaws.com.cn/v1/invocations" -H "accept: application/json" -H "Content-Type: application/json" -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwcm9qZWN0IjoidGVzdCJ9.ebcS-y4S0g9qAaBh_GHfagWOgLKrySGAwvUJsJb2Ak8" --data-raw "{\"messages\":[{\"role\":\"user\",\"content\":\"Hello\"}],\"temperature\":0.3,\"max_tokens\":50}" -w "Status: %{http_code}\n"
```

## 完整的测试序列

### 1. 测试Tokenize API (先测试这个)
```powershell
curl.exe -X POST "http://internal-ai-tao-llm-apiserver-dev-1126944677.cn-northwest-1.elb.amazonaws.com.cn/v1/tokenize" -H "accept: application/json" -H "Content-Type: application/json" -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwcm9qZWN0IjoidGVzdCJ9.ebcS-y4S0g9qAaBh_GHfagWOgLKrySGAwvUJsJb2Ak8" --data-raw "{\"messages\":[{\"role\":\"user\",\"content\":\"Hello\"}]}" -w "Status: %{http_code}\n"
```

### 2. 测试Invocations API
```powershell
curl.exe -X POST "http://internal-ai-tao-llm-apiserver-dev-1126944677.cn-northwest-1.elb.amazonaws.com.cn/v1/invocations" -H "accept: application/json" -H "Content-Type: application/json" -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwcm9qZWN0IjoidGVzdCJ9.ebcS-y4S0g9qAaBh_GHfagWOgLKrySGAwvUJsJb2Ak8" --data-raw "{\"messages\":[{\"role\":\"user\",\"content\":\"Hello\"}],\"temperature\":0.3,\"max_tokens\":50}" -w "Status: %{http_code}\n"
```

## 预期结果
- **成功**: Status: 200，返回包含生成文本的JSON响应
- **失败**: Status: 400/500，检查JSON格式或服务器状态

## 调试技巧
如果仍然有问题，可以使用 `-v` 参数查看详细信息：
```powershell
curl.exe -v -X POST "http://internal-ai-tao-llm-apiserver-dev-1126944677.cn-northwest-1.elb.amazonaws.com.cn/v1/invocations" -H "accept: application/json" -H "Content-Type: application/json" -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwcm9qZWN0IjoidGVzdCJ9.ebcS-y4S0g9qAaBh_GHfagWOgLKrySGAwvUJsJb2Ak8" --data-raw "{\"messages\":[{\"role\":\"user\",\"content\":\"Hello\"}],\"temperature\":0.3,\"max_tokens\":50}"
``` 