---
description: 
globs: 
alwaysApply: true
---
# chat-aitao_massemail_code.html 技术文档 (v11.21.5)

本文档详细描述 `chat-aitao_massemail_code.html` (版本 v11.21.5) 的技术实现。该 HTML 文件实现了一个 AI 助手聊天界面，其核心功能是接收用户输入，通过JavaScript预处理、LLM翻译、JavaScript后处理的多阶段流程，调用内部 LLM API，并显示结果。文件内亦包含一个调试面板。

## 核心逻辑与流程 (基于 v11.21.5 `sendMessage` 实现)

系统严格遵循一个由JavaScript预处理、LLM翻译、JavaScript后处理组成的多阶段流程，旨在生成符合亚马逊云科技官方风格和术语规范的简体中文翻译。整个流程由 `sendMessage` 函数驱动：

1.  **用户输入**:
    *   通过 `userInput.value` 获取用户原始输入文本。

2.  **阶段 1: JavaScript 英文术语标准化与初步处理 (`standardizeTextByLines`)**:
    *   调用 `standardizeTextByLines(messageText)` 对整个用户输入进行初步标准化。此函数内部按行处理：
        *   识别元数据行（如 `Service:`, `Region:`, `Wording:` 等键名）和普通内容行。
        *   对元数据行的键进行保护（后续在 `replaceTermsWithPlaceholders` 中转为 `__METAKEY_X__`），其值部分及普通内容行则调用 `standardizeEnglishChunk`。
    *   `standardizeEnglishChunk(textChunk, serviceMentionState, scopeType)` 是JS预处理的核心，执行以下操作：
        *   **特殊代码保护**: 优先保护 `AWS_` 开头的特定大写错误码/类型码 (如 `AWS_DYNAMODB_ACCESS_CONTROL_API_ISSUE`)，JS内部替换为临时占位符，后续在 `replaceTermsWithPlaceholders` 中转为 `__KEYCODE_X__`。
        *   **CLI命令处理**: 识别以 `aws ` 开头的命令，标准化 ` --region` 参数为中国区 (默认 `cn-north-1` 或 `cn-northwest-1` 基于上下文)，JS内部替换为临时占位符，后续在 `replaceTermsWithPlaceholders` 中转为 `__CLICMD_X__`。
        *   **JSON策略块保护**: 识别并临时保护 `{\"Version\": ...}` 结构，后续转为 `__JSONBLOCK_X__`。
        *   **URL处理**: 识别URL，对特定AWS域名进行初步本地化转换 (如 `docs.aws.amazon.com` -> `docs.amazonaws.cn`；`console.aws.amazon.com` -> `console.amazonaws.com.cn` 并标准化区域)，JS内部替换为临时占位符，后续在 `replaceTermsWithPlaceholders` 中转为 `__URL_X__`。
        *   **IAM策略字符串保护**: 形式如 `'action:Resource*'` 的字符串被临时保护，后续转为 `__POLICY_X__`。
        *   **服务名称规范化 (核心)**:
            *   权威数据源: `servicePatterns` 数组 (定义在 `chat-aitao_massemail_code.html` 中)。
            *   作用域: Wording/First Post Wording行内容部分使用行内作用域；普通内容行使用全局作用域 (`window.finalGlobalServiceMentionState`) 进行服务名首次/后续追踪。
            *   处理: 将文本中出现的各种服务名称变体（包括复合词如 `EC2-specific`）替换为标准全称 (首次) 或标准简称 (后续)。标准化后的服务名称本身将在 `replaceTermsWithPlaceholders` 中被替换为 `__SRVCNM_X__`。
        *   **AWS基础通用术语标准化**: 处理如 `AWS Account` -> `Amazon Web Services Account`, `AWS Support` -> `Amazon Web Services Support`, `AWS Management Console` -> `Amazon Web Services Console`。独立 `AWS` 根据上下文转换为 `Amazon` (临时，可能最终变为"亚马逊云科技"或被服务名吸收)。这些标准化后的术语也会在 `replaceTermsWithPlaceholders` 中被相应占位符保护。
        *   **时间日期处理**: 识别多种时间日期格式 (PDT, PST, UTC等)，精确计算并统一转换为 `YYYY-MM-DD HH:MM UTC+8` (日期时间) 或 `YYYY-MM-DD` (仅日期) 的中间格式。此中间格式将在 `replaceTermsWithPlaceholders` 中被替换为 `__TIMESTAMP_X__`。
    *   此阶段的输出是 `standardizedEnglish` 文本。

3.  **阶段 2: JavaScript 占位符生成 (`replaceTermsWithPlaceholders`)**:
    *   调用 `replaceTermsWithPlaceholders(standardizedEnglish, window.finalGlobalServiceMentionState)`。
    *   此函数将阶段 1 输出的 `standardizedEnglish` 文本中所有已标准化的服务名称 (`__SRVCNM_X__`)、产品名 (`__PRDNM_X__`)、缩写 (`__ACRNYM_X__`)、之前临时保护的特殊代码 (`__KEYCODE_X__`)、CLI命令 (`__CLICMD_X__`)、JSON块 (`__JSONBLOCK_X__`)、URL (`__URL_X__`)、策略字符串 (`__POLICY_X__`)、时间戳 (`__TIMESTAMP_X__`)、Mustache模板占位符 (`__MTPLHDR_X__`)、文档引用 (`__DOCREF_X__`)、元数据键名 (`__METAKEY_X__`) 和 ARN (`__ARNSTR_X__`，ARN的保护也在此阶段完成) 等替换为最终的 `__TYPE_COUNTER__` 格式占位符。
    *   所有被替换的原始术语和其占位符被存储在全局 `placeholderMap` 中，供后续恢复使用。
    *   此阶段的输出是 `textWithPlaceholders`，这是准备发送给 LLM 进行翻译的文本。

4.  **阶段 3: LLM 翻译 (`callLLMApi` 调用 `llmTranslationPrompt`)**:
    *   将 `textWithPlaceholders` 与 `llmTranslationPrompt` (定义在 `chat-aitao_massemail_code.html` 中) 组合。
    *   调用 `callLLMApi` 将组合后的内容发送给 LLM。
    *   **`llmTranslationPrompt` 的核心指令包括**:
        *   **占位符处理**: LLM 必须原封不动地保留所有 `__TYPE_COUNTER__` 占位符。严禁翻译、修改、删除或添加任何占位符。这是LLM任务的绝对核心，任何对占位符的错误处理都将直接影响最终结果的准确性。
        *   **翻译核心**: LLM 必须翻译所有占位符之外的、且确实是英文自然语言表述的文本。
        *   **风格与格式**: 翻译应流畅、准确，符合亚马逊云科技官方专业技术文档风格，使用简体中文和中文标点，并保持原始文本的段落和行结构。
        *   **方括号 `[...]` 内容处理**: 如果方括号内不是一个被 `__DOCREF_X__` 等占位符保护的文档引用标记，而是普通英文文本，则LLM必须翻译方括号内的文本，并使用合适的中文标点（如全角圆括号 `（...）`）或将其自然融入句子。
        *   **通用技术术语翻译**: 对于未被JavaScript阶段的占位符捕获的通用技术术语（例如，'Fast Launch'），LLM应将其翻译成中文（例如，'快速启动'）。
        *   **特定术语指导**: 如独立 "Amazon" 的翻译规则（仅在非服务名/产品名部分时翻译为"亚马逊云科技"），以及特定支持场景句子（如联系支持团队）的简化重写指导。
    *   LLM 返回包含占位符的翻译后文本: `translatedTextWithPlaceholders`。

5.  **阶段 4: JavaScript 占位符恢复与中文最终格式化 (`restorePlaceholdersAndFinalizeChinese`)**:
    *   调用 `restorePlaceholdersAndFinalizeChinese(translatedTextWithPlaceholders)`。
    *   此函数使用 `placeholderMap` 将LLM翻译结果中的所有 `__TYPE_COUNTER__` 占位符恢复为其对应的原始英文值或特殊格式化后的中文值。
        *   `__TIMESTAMP_X__` 占位符会被恢复并格式化为 `YYYY年M月D日 上午/下午 h:mm 北京时间` 或 `YYYY年M月D日`。
        *   元数据键名占位符 (`__METAKEY_X__`) 会恢复为原始键名 (如 "Service:")。
        *   ARN、CLI命令、JSON块、代码等会精确恢复。
    *   执行最终的中文品牌术语替换 (如 "Amazon Web Services Support" -> "亚马逊云科技中国支持团队", "Amazon Web Services Console" -> "亚马逊云科技控制台", 独立 "Amazon Web Services" -> "亚马逊云科技", 以及根据复杂规则处理独立的 "Amazon" -> "亚马逊云科技")。
    *   此阶段输出 `finalChineseOutput`，即最终呈现给用户的翻译结果。

6.  **显示结果 (`appendMessage`)**:
    *   `appendMessage(displayFinalOutput, 'ai-message')` 将最终结果显示在聊天框。
    *   在调试模式 (`debugMode`)下，各中间步骤 (`standardizedEnglish`, `textWithPlaceholders`, `translatedTextWithPlaceholders`) 的结果也会通过 `appendMessage` 显示。
    *   AI消息会经过HTML转义 (`escapeHTML`)。Markdown加粗 (`**text**`) 会被转换为 `<strong>text</strong>`。单星号斜体不被处理。换行符 (`\n`) 转换为 `<br>`。

7.  **质量检查 (`checkTranslationQuality_Optimized`)**:
    *   对 `standardizedEnglish` 和 `finalChineseOutput` 及 `placeholderMap` 进行比较，检查翻译质量，如占位符是否正确还原、关键术语是否按预期翻译等。
    *   若存在问题且 `debugMode` 开启，则显示警告。

8.  **调试 (`logDebug`)**:
    *   `logDebug()` 函数在整个流程中用于记录详细的调试信息和各阶段数据，显示在可切换的调试面板 (`debug-panel`) 中。

## 关键函数

### 核心处理与JS标准化函数
*   `sendMessage()`: 处理用户输入和启动完整处理流程的入口。
*   `standardizeTextByLines(text)`: 接收原始用户输入，按行分发调用 `standardizeEnglishChunk`，管理服务名称提及的全局/行内作用域。
*   `standardizeEnglishChunk(textChunk, currentServiceMentionState, scopeType)`: 执行英文术语、服务名称、时间、URL等的具体标准化逻辑。是JS预处理的核心。
    *   **服务名称标准化数据源**: 内部 `servicePatterns` 数组 (定义在 `chat-aitao_massemail_code.html` 中) 是识别和标准化AWS服务名称的权威来源。它包含了各种服务的正则表达式、标准全称、标准简称和基础名称。
*   `replaceTermsWithPlaceholders(text, currentGlobalServiceState)`: 将标准化后的英文文本中的特定术语替换为 `__TYPE_COUNTER__` 占位符，并填充 `placeholderMap`。
*   `restorePlaceholdersAndFinalizeChinese(textWithPlaceholders)`: 将LLM翻译后的文本中的占位符恢复，并进行最终的中文格式化和品牌术语替换。

### LLM交互函数
*   `callLLMApi(history, stageName)`: 负责调用内部 Tokenize 和 Invocations API。
    *   动态获取和使用模型名称 (从 `tokenize` API响应中获取，或在特定错误时重试不带模型参数)。
    *   计算 `max_tokens`。
    *   包含错误处理和重试逻辑 (例如，模型不存在时)。
*   `llmTranslationPrompt` (常量字符串): **极其重要**，这是传递给LLM的核心指令集，定义了翻译阶段的行为和规则。其内容直接影响翻译质量和占位符的保留。位于 `chat-aitao_massemail_code.html` 中。

### UI显示和工具函数
*   `appendMessage(content, type)`: 将消息显示在聊天界面的核心函数。
    *   对AI消息内容进行HTML转义 (`escapeHTML`)。
    *   将 `\n` 替换为 `<br>`。
    *   将 `/**(.*?)/**` 替换为 `<strong>$1</strong>`。
    *   **注意**: 单星号 `*italic*` 不再被转换为 `<em>` (如 `chat-aitao_massemail_code.html` 代码所示)，以避免与ARN中的 `*` 字符冲突。
*   `logDebug(message, data)`: 调试信息记录。
*   `escapeHTML(str)`: HTML转义工具函数。
*   `adjustTextareaHeight(textarea)`, `handleKeyDown(event)`: UI辅助函数。
*   调试面板相关函数: `toggleDebug()`, `clearDebug()`, `exportDebug()`, `filterDebugInfo()`, `updateDebugStats()`, `toggleDebugMode()`.

### 服务名称与模式定义 (源文件: `chat-aitao_massemail_code.html`)
*   `servicePatterns` (数组): 定义了大量AWS服务的识别正则表达式、标准全称、简称、基础名称以及是否为后缀复合词等信息。**此数组是服务名称标准化的核心数据源。**
*   `robustArnRegexGlobal` 和 `robustArnRegexSourceForBoundary`: 用于ARN识别的正则表达式。

### 质量检查函数
*   `checkTranslationQuality_Optimized(standardizedEng, finalChinese, currentPlaceholderMap)`: 用于检查最终翻译质量，例如占位符是否正确还原、关键术语是否按预期翻译等。

## JS标准化输出与UI显示特性

### 关于强调格式 (如加粗 `**text**`)

1.  **JS标准化阶段 (`standardizeEnglishChunk`)**:
    *   The JavaScript function `standardizeEnglishChunk` is responsible for standardizing English terms, service names, times, URLs, etc.
    *   This function itself **does not actively add** Markdown emphasis like bold (`**...**`) or italics (`*...*`) to the terms it standardizes. Its primary role is to convert terms to their canonical forms before they are turned into placeholders.

2.  **LLM翻译阶段 (`llmTranslationPrompt`)**:
    *   The `llmTranslationPrompt` provided to the LLM instructs it to translate the natural language text *around* the placeholders.
    *   The prompt **does not explicitly instruct the LLM** to add bold formatting to translated text or to the placeholders themselves.

3.  **Source of Emphasis**:
    *   If Markdown emphasis (like `**text**`) appears in the AI's output, it would typically originate from:
        *   The user's original input, if such formatting survived the JS standardization and placeholder process (which is unlikely for parts of the text that are identified and replaced).
        *   The LLM *autonomously* deciding to use Markdown bolding for emphasis while translating the non-placeholder natural language segments. This is not a behavior strictly controlled or mandated by the system.

4.  **UI Rendering (`appendMessage` function)**:
    *   The `appendMessage` function is responsible for displaying messages in the chat UI.
    *   For AI messages (`type === 'ai-message'`), it performs the following conversions:
        ```javascript
        // Snippet from appendMessage in chat-aitao_massemail_code.html
        if (type === 'ai-message') {
            messageDiv.innerHTML = escapedContent
                                       .replace(/\n/g, '<br>')
                                       .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                                    // Single asterisk for italics is intentionally removed to prevent conflict with ARNs containing '*'
        }
        ```
    *   This means:
        *   Line breaks (`\n`) are converted to `<br>`.
        *   Markdown bold (`**text**`) is converted to HTML `<strong>text</strong>`.
        *   Markdown italics (`*text*`) are **not** processed for AI messages to prevent conflicts with characters like `*` in ARNs or other technical strings.

5.  **Development Considerations**:
    *   The system should not rely on the LLM consistently applying bold formatting.
    *   The UI correctly renders bold if `**text**` is present in the AI's message content.
    *   Debugging should involve checking the raw output from the LLM (before `restorePlaceholdersAndFinalizeChinese`) to see if `**` marks are present, and then verifying `appendMessage`'s HTML conversion.

## 核心处理机制与特性

### 复合词处理机制 (主要在 JS `standardizeEnglishChunk` 中)
系统通过 `servicePatterns` 数组中的定义（例如 `isCompoundWithSuffix` 和 `suffixGroup` 属性）以及 `standardizeEnglishChunk` 内的逻辑来处理复合词中的服务名称。

*   **识别模式**:
    *   `servicePatterns` 中定义的正则表达式能够捕获服务名称及其相关的后缀（如 `EC2-specific` 中的 `-specific`，或 `Amazon EC2 P3 instances` 中的 `P3 instances`）。
*   **处理逻辑**:
    1.  当 `servicePatterns` 中的条目匹配时，JS 会区分服务名称核心部分和任何捕获到的后缀/限定词。
    2.  服务名称核心部分根据其在当前作用域（全局或行内）的首次/后续出现状态，被替换为标准全称或简称。
    3.  后缀/限定词（如 `-specific`, ` P3 instances`）通常会保留并附加到标准化后的服务名称之后。
        *   示例 (首次): `EC2-specific` -> `Amazon Elastic Compute Cloud (EC2)-specific`
        *   示例 (后续): `EC2 P3 instances` -> `Amazon EC2 P3 instances`
    4.  处理状态由 `currentServiceMentionState` (传递给 `standardizeEnglishChunk`) 和全局的 `window.finalGlobalServiceMentionState` 追踪。

### 服务名称与术语标准化 (JS `standardizeEnglishChunk` 核心)
`standardizeEnglishChunk` 函数是JavaScript预处理阶段的核心，负责对文本进行深度标准化：
*   **权威数据源**: `servicePatterns` 数组 (定义在 `chat-aitao_massemail_code.html`) 是所有AWS服务名称识别、标准化（全称/简称、复合词处理）的唯一来源。
*   **特殊代码/CLI/JSON/URL/策略/ARN保护**: 如前文"核心逻辑与流程"所述，这些结构被优先识别和保护，最终转换为占位符。
*   **AWS基础通用术语**: 标准化如 `AWS Account`、`AWS Support`、`AWS Management Console` 等。
*   **时间日期转换**: 统一转换为 `YYYY-MM-DD HH:MM UTC+8` 或 `YYYY-MM-DD` 的中间格式。

### LLM 提示 (`llmTranslationPrompt`) 关键特性
`llmTranslationPrompt` (位于 `chat-aitao_massemail_code.html`) 是指导LLM进行翻译的核心指令集。其关键特性包括：
*   **占位符绝对保留**: 强调所有 `__TYPE_COUNTER__` 占位符必须原样保留，不得修改或翻译。
*   **翻译范围**: 明确指示LLM仅翻译占位符之外的英文自然语言文本。
*   **输出风格**: 要求翻译流畅、准确，符合亚马逊云科技官方简体中文风格，使用中文标点。
*   **结构保持**: 要求LLM保持原文的段落和行结构。
*   **特定场景指导**:
    *   对独立 "Amazon" 的翻译提供上下文指导。
    *   对特定支持场景的句子（如联系支持团队）提供简化重写指导。
    *   指导如何处理方括号 `[...]` 内的非引用文本（翻译并使用中文圆括号或融入句子）。
*   **视觉强化提示**: `llmTranslationPrompt` 本身也使用如 `**[占位符处理 - 最高指令]**` 等方式强调关键规则，以期LLM更严格遵守。

### 后处理与质量验证
*   **占位符恢复 (`restorePlaceholdersAndFinalizeChinese`)**:
    *   精确恢复 `placeholderMap` 中的所有占位符为其原始值或格式化后的中文（特别是时间戳）。
    *   执行最终的中文品牌术语替换（如 `Amazon Web Services Support` → `亚马逊云科技中国支持团队`）。
*   **质量检查 (`checkTranslationQuality_Optimized`)**:
    *   对最终输出进行一系列检查，例如：
        *   占位符是否都已正确替换。
        *   关键AWS术语（如 "Amazon Web Services Support", "console.aws.amazon.com"链接）是否按预期本地化。
        *   ARN 是否被错误修改或丢失。
        *   时间格式是否正确转换为北京时间。
        *   是否存在未翻译的、应翻译的片段（通过启发式方法检查）。
    *   问题会记录到调试日志，并在调试模式下显示警告。

### 调试与监控 (`logDebug` 及浏览器控制台)
*   **详细日志**: `logDebug` 函数在代码的关键节点（如各处理阶段的输入输出、API调用、错误处理、状态变化等）记录详细信息到调试面板。
*   **数据快照**: 记录如 `placeholderMap`、`serviceMentionState`、`standardizedEnglish`、`textWithPlaceholders` 等关键数据结构或中间文本。
*   **浏览器控制台镜像**: `logDebug` 的信息也会输出到浏览器控制台，便于开发者使用更强大的原生调试工具。
*   **调试模式切换**: `debugMode` 变量控制部分详细日志的输出和中间步骤在UI上的显示。
*   **统计信息**: 调试面板显示日志条数、运行时间等统计。

## LLM API 调用详情 (v11.21.5)

### API 端点配置
```javascript
const baseUrl = "http://internal-ai-tao-llm-apiserver-dev-1126944677.cn-northwest-1.elb.amazonaws.com.cn";
const tokenizeUrl = `${baseUrl}/v1/tokenize`;
const invokeUrl = `${baseUrl}/v1/invocations`;
const modelsUrl = `${baseUrl}/v1/models`; // 用于调试模式下获取模型列表

const authHeader = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwcm9qZWN0IjoidGVzdCJ9.ebcS-y4S0g9qAaBh_GHfagWOgLKrySGAwvUJsJb2Ak8';
```

### API 调用流程 (`callLLMApi` 函数)
1.  **Tokenize API 调用**:
    *   请求体: `{ messages: history }` (可能包含 `model: currentModel` 字段)。
    *   首先尝试使用从上一次 `tokenize` 响应中获取的 `model_name` (如果存在)。
    *   **错误处理**:
        *   如果请求失败（如模型不存在），且请求中指定了模型，会尝试移除模型参数后重试一次 `tokenize`。
        *   如果重试成功，使用重试响应中的 `model_name` (或 `null`) 继续。
    *   **响应解析**: 从成功的 `tokenize` 响应中获取 `count` (token数量), `max_model_len`, 和 `model_name` (供后续调用)。

2.  **Invocations API 调用 (`invokeLLM` 子函数)**:
    *   **`max_tokens` 计算**: `max_tokens = max_model_len - tokenCount - 150` (缓冲区150，若结果 <= 100，则设为2000)。
    *   请求体: `{ messages: history, temperature: 0.05, max_tokens: calculated_max_tokens }` (可能包含 `model: currentModel` 字段，此 `currentModel` 来自成功的 `tokenize` 步骤)。
    *   **错误处理**: 若调用失败，抛出错误。
    *   **响应解析**: 获取 `choices[0].message.content` 作为LLM的输出。

3.  **模型列表获取 (调试模式)**:
    *   如果 `debugMode` 为 `true`，在主调用流程后，会尝试调用 `modelsUrl` (`/v1/models`) 获取并记录可用模型列表。此信息仅用于调试，不影响主翻译流程的模型选择。

## 开发与调试指南

### 提示词 (`llmTranslationPrompt`) 修改注意事项
*   `llmTranslationPrompt` 是AI翻译行为的核心。任何修改都必须极其小心，并进行充分测试。
*   确保提示词清晰指导LLM正确处理占位符（绝对保留）和翻译非占位符文本。
*   复合词、特殊格式等主要由JS预处理，LLM层面只需关注占位符的保留。

### 服务名称规则维护 (`servicePatterns` 数组)
*   添加、修改或删除服务时，必须更新位于 `chat-aitao_massemail_code.html` 中的 `servicePatterns` 数组。
*   确保正则表达式的准确性和特异性，避免与其他模式冲突。
*   测试新规则对标准格式、复合词格式及各种变体的影响。

### 测试和验证
*   **端到端测试**: 测试从用户输入到最终中文输出的完整流程。
*   **JS标准化测试**: 单独验证 `standardizeTextByLines` 和 `standardizeEnglishChunk` 的输出，确保术语、时间、URL等按预期标准化。
*   **占位符测试**: 验证 `replaceTermsWithPlaceholders` 是否正确生成占位符和填充 `placeholderMap`，以及 `restorePlaceholdersAndFinalizeChinese` 是否正确恢复。
*   **LLM交互测试**: 检查发送给LLM的文本（带占位符）是否正确，以及LLM返回的文本是否保留了占位符并翻译了其余内容。
*   **复合词测试**: 专门测试包含各种复合词（如 `EC2-specific`, `RDS for PostgreSQL`）的文本。
*   **Markdown渲染**: 验证 `appendMessage` 是否正确将 `**bold**` 渲染为 `<strong>bold</strong>`，并正确处理换行。

### 调试工具
*   利用浏览器内置的调试面板 (`debug-panel`) 和 `logDebug` 输出的信息。
*   检查 `console.log` 输出，特别是标记了 `[直接打印 PRE-APPEND]` 的部分，可以看到各阶段原始数据。
*   在浏览器开发者工具中直接检查 `window.placeholderMap` 和 `window.finalGlobalServiceMentionState` (通过调试器访问)。

### 错误排查
*   **占位符未还原/错误还原**: 检查 `placeholderMap` 的内容和 `restorePlaceholdersAndFinalizeChinese` 的逻辑。
*   **英文未翻译/翻译错误**: 检查 `llmTranslationPrompt` 是否清晰，以及发送给LLM的文本是否正确（即占位符之外的英文是否确实是应翻译内容）。
*   **服务名称标准化错误**: 检查 `servicePatterns` 中的对应正则表达式和规则，以及 `standardizeEnglishChunk` 中的处理逻辑。
*   **时间日期错误**: 检查 `standardizeEnglishChunk` 中的时间解析和转换逻辑，以及 `restorePlaceholdersAndFinalizeChinese` 中的最终格式化。

## 版本历史

### v11.21.5 (本文档对应版本 - 2025年6月+)
*   **核心架构**: 明确为 JavaScript预处理 -> LLM翻译 -> JavaScript后处理。
    *   **JS预处理**: `standardizeTextByLines` (调用 `standardizeEnglishChunk`) 进行深度标准化 (服务名、术语、时间、URL等)，然后 `replaceTermsWithPlaceholders` 将这些标准化术语转为 `__TYPE_COUNTER__` 占位符。
    *   **LLM翻译**: 使用单一核心提示词 `llmTranslationPrompt`，指示LLM翻译占位符以外的自然语言，并严格保留占位符。
    *   **JS后处理**: `restorePlaceholdersAndFinalizeChinese` 恢复占位符为原文或格式化后的中文，并进行最终品牌术语调整。
*   **Prompt**: `llmTranslationPrompt` (定义于 `chat-aitao_massemail_code.html`) 作为指导LLM翻译阶段的核心。
*   **服务名称处理**: 权威来源为 `chat-aitao_massemail_code.html` 内的 `servicePatterns` 数组。
*   **UI渲染**: `appendMessage` 仅处理 `**bold**` 为 `<strong>`，不再处理 `*italic*` (以避免ARN `*` 冲突)。
*   **API调用**: `callLLMApi` 包含动态模型获取提示（基于tokenize响应）和特定错误重试逻辑。
*   **文档同步**: 本 `pocdeveloment.mdc` 更新以全面反映 `chat-aitao_massemail_code.html v11.21.5` 的实际行为和 `提示词v11.16代码版本.md` 中的核心流程思想。

### v3.3 (2025年1月) - 系统提示词完全同步 🔄 (历史版本 - 架构与当前v11.x有差异)
*   **主要更新**: 更新 `stage2SystemPrompt` 使其与当时的 `提示词优化.md` 文档同步；系统提示词反映当时的5步骤架构。
*   **技术细节**: 当时的 `stage2SystemPrompt` 包含5步骤说明 (英文术语不翻译确认, 术语保护标记, 文本翻译与本地化, 时间格式转换, 【】标记强制移除)。
*   **注意**: 此版本描述的5步骤 **阶段二LLM处理** 架构与当前v11.21.5的JS主导预处理、单一核心`llmTranslationPrompt`、JS主导后处理的架构有显著不同。

### v3.2 (2025年1月) - 阶段二5步骤架构优化 🚀 (历史版本 - 架构与当前v11.x有差异)
*   **主要更新**: 将阶段二（中文本地化）细化为5步LLM处理。
*   **新的5步骤架构**: (同v3.3的技术细节中描述的5步骤)。
*   **注意**: 同v3.3，此版本描述的LLM多步骤处理与当前v11.21.5的架构不同。

### v3.0 (历史版本)
*   实现分步提示词处理架构。
*   精简核心服务列表。
*   优化复合词处理机制。
*   增强验证和纠错功能。

### v2.1 (历史版本)
*   新增复合词处理机制。
*   增强后处理验证功能。
*   优化Amazon EBS和IAM识别。
*   改进调试和监控机制。

### v2.0 (历史版本)
*   实现两阶段处理架构。
*   添加服务名称首次/后续追踪。
*   集成动态模型获取机制。

### v1.0 (历史版本)
*   基础翻译功能。
*   简单的服务名称替换。
*   基本的API调用机制。 