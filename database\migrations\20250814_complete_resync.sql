-- Migration: perform_complete_data_resync with advisory lock and orphan hard-fail
-- Date: 2025-08-14

BEGIN;

-- NOTE: External backup should be performed by application before calling this function when tables are non-empty.
-- This function targets empty-table initialization and full-reset execution with atomicity.

CREATE OR REPLACE FUNCTION perform_complete_data_resync(
    p_services_data JSONB,
    p_patterns_data JSONB,
    p_batch_size   INTEGER DEFAULT 100
) RETURNS TABLE (
    operation_status TEXT,
    services_inserted INTEGER,
    patterns_inserted INTEGER,
    execution_time_seconds NUMERIC
) LANGUAGE plpgsql AS $$
DECLARE
    v_t0 TIMESTAMPTZ := clock_timestamp();
    v_locked BOOLEAN;
    v_svc_count INTEGER;
    v_pat_count INTEGER;
    v_orphans INTEGER;
BEGIN
    SELECT pg_try_advisory_xact_lock(845932017) INTO v_locked;
    IF NOT v_locked THEN
        RAISE EXCEPTION 'Another complete resync is in progress';
    END IF;

    SELECT COUNT(*) INTO v_svc_count FROM service_names;
    SELECT COUNT(*) INTO v_pat_count FROM regex_patterns;
    IF v_svc_count > 0 OR v_pat_count > 0 THEN
        RAISE EXCEPTION 'Tables are not empty. Use full resync with external backup instead.';
    END IF;

    WITH svc AS (
        SELECT jsonb_array_elements(p_services_data) AS j
    )
    INSERT INTO service_names(
        authoritative_full_name, base_name, internal_name,
        full_name_en, short_name_en, service_code, source, is_active,
        last_synced_at
    )
    SELECT
        (j->>'authoritative_full_name')::VARCHAR(255),
        (j->>'base_name')::VARCHAR(255),
        NULLIF(j->>'internal_name','')::VARCHAR(255),
        (j->>'authoritative_full_name')::VARCHAR(255),
        (j->>'short_name_en')::VARCHAR(100),
        NULLIF(j->>'service_code','')::VARCHAR(50),
        COALESCE(j->>'source','web_scrape')::rule_source,
        TRUE,
        NOW()
    FROM svc
    WHERE (j->>'authoritative_full_name') IS NOT NULL AND length(j->>'authoritative_full_name')>0;

    GET DIAGNOSTICS services_inserted = ROW_COUNT;

    WITH pat AS (
        SELECT jsonb_array_elements(p_patterns_data) AS j
    )
    INSERT INTO regex_patterns(
        pattern_name, pattern_type, regex_string, related_service_id,
        service_code, priority, metadata, is_active, validation_status
    )
    SELECT
        (j->>'pattern_name')::VARCHAR(100),
        (j->>'pattern_type')::regex_pattern_type,
        (j->>'regex_string')::TEXT,
        COALESCE(
            (SELECT s.id FROM service_names s WHERE s.authoritative_full_name = j->>'authoritative_full_name' LIMIT 1),
            (SELECT s2.id FROM service_names s2 WHERE s2.service_code = j->>'service_code' LIMIT 1)
        ),
        NULLIF(j->>'service_code','')::VARCHAR(50),
        COALESCE((j->>'priority')::INTEGER, 100),
        COALESCE(j->'metadata', '{}'::JSONB),
        TRUE,
        'pending'::validation_status_type
    FROM pat
    WHERE (j->>'regex_string') IS NOT NULL AND length(j->>'regex_string')>0 AND length(j->>'regex_string')<10000;

    GET DIAGNOSTICS patterns_inserted = ROW_COUNT;

    SELECT COUNT(*) INTO v_orphans
    FROM regex_patterns rp
    LEFT JOIN service_names sn ON sn.id = rp.related_service_id
    WHERE rp.pattern_type = 'SERVICE_NAME' AND rp.is_active = TRUE AND sn.id IS NULL;

    IF v_orphans > 0 THEN
        RAISE EXCEPTION 'Found % orphan SERVICE_NAME patterns. Abort and rollback.', v_orphans;
    END IF;

    ANALYZE service_names;
    ANALYZE regex_patterns;

    operation_status := 'SUCCESS';
    execution_time_seconds := EXTRACT(EPOCH FROM (clock_timestamp() - v_t0));
    RETURN NEXT;
END;
$$;

COMMIT;

