// ==UserScript==
// @name         Extract Pre Content
// @namespace    http://tampermonkey.net/
// @version      0.1
// @description  Extract pre content from AWS Issues page
// <AUTHOR>
// @match        https://issues.cn-northwest-1.amazonaws.cn/issues/*
// @icon         data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==
// @grant        GM_xmlhttpRequest
// @connect      qek0wlrzwb.execute-api.cn-north-1.amazonaws.com.cn
// ==/UserScript==

(function() {
    'use strict';

    // Function to check if text is English (简化版，仅作为备用)
    function isEnglishContent(text) {
        if (!text || text.trim().length === 0) {
            console.log('Empty text content found');
            return false;
        }
        return /[a-zA-Z]/.test(text);
    }

    // Function to wait for an element to be present in the DOM
    function waitForElement(selector, maxWaitTime = 30000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            function checkElement() {
                const element = document.evaluate(
                    selector,
                    document,
                    null,
                    XPathResult.FIRST_ORDERED_NODE_TYPE,
                    null
                ).singleNodeValue;

                if (element) {
                    resolve(element);
                    return;
                }

                if (Date.now() - startTime > maxWaitTime) {
                    reject(new Error(`Timeout waiting for element: ${selector}`));
                    return;
                }

                setTimeout(checkElement, 500);
            }

            checkElement();
        });
    }

    // Function to find three English pre elements in container
    function findThreeEnglishPres(container) {
        const preElements = container.getElementsByTagName('pre');
        console.log(`Found ${preElements.length} pre elements in container`);

        if (preElements.length === 3) {
            const contents = Array.from(preElements).map(pre => {
                // 递归查找合适的标题元素
                let title = '';
                let currentElement = pre.previousElementSibling;
                
                // 定义可能作为标题的标签
                const possibleTitleTags = ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'strong', 'b', 'div'];
                
                while (currentElement) {
                    // 检查当前元素是否是可能的标题元素
                    if (possibleTitleTags.includes(currentElement.tagName.toLowerCase())) {
                        const possibleTitle = currentElement.textContent.trim();
                        
                        // 如果找到内容非空的标题，使用它
                        if (possibleTitle) {
                            title = possibleTitle;
                            // 不立即退出，继续查找更好的标题
                        }
                    }
                    
                    currentElement = currentElement.previousElementSibling;
                }
                
                // 获取原始内容
                let contentText = pre.textContent.trim();
                
                // 过滤掉AWS元数据描述文本
                const metadataPatterns = [
                    /^The following is taken directly from.*$/m,
                    /^The following message was posted:.*$/m,
                    /^AWS_[A-Z0-9_]+$/m,
                    /^https?:\/\/command-center\.support\.aws\.a2z\.com.*$/m,
                    /^\/+\*\[@data-link="visible\{:state == 'synced'\}"\/\w+\[\d+\]\]?$/m,  // 精确匹配问题中的XPath模式
                    /^\/+\*\[@data-link="visible.*$/m  // 更通用的data-link XPath模式
                ];
                
                for (const pattern of metadataPatterns) {
                    contentText = contentText.replace(pattern, '').trim();
                }
                
                return {
                    title: title,
                    content: contentText
                };
            });

            // Check if all contents are English
            const allEnglish = contents.every(item => isEnglishContent(item.content));
            if (allEnglish) {
                return contents;
            }
        }
        return null;
    }

    // Function to wait for dynamic content to load
    async function waitForDynamicContent() {
        try {
            // First, try to find the description container
            let descContainer = null;
            try {
                console.log('Attempting to find description container...');
                descContainer = await Promise.race([
                    waitForElement('//*[@id="issue-description-container"]'),
                    waitForElement('//div[contains(@class, "issue-description-container")]'),
                    new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('Description container not found')), 5000)
                    )
                ]);
            } catch (error) {
                console.log('Description container not found, proceeding to activity containers...', error);
            }

            // If description container exists, check its content
            if (descContainer) {
                const descPreElements = descContainer.getElementsByTagName('pre');
                if (descPreElements.length > 0) {
                    const descContents = Array.from(descPreElements).map((pre, index) => {
                        let title = '';
                        let currentElement = pre.previousElementSibling;
                        while (currentElement) {
                            if (currentElement.tagName.toLowerCase() === 'p') {
                                title = currentElement.textContent.trim();
                                break;
                            }
                            currentElement = currentElement.previousElementSibling;
                        }
                        
                        // 获取原始内容
                        let contentText = pre.textContent.trim();
                        
                        // 过滤掉AWS元数据描述文本
                        const metadataPatterns = [
                            /^The following is taken directly from.*$/m,
                            /^The following message was posted:.*$/m,
                            /^AWS_[A-Z0-9_]+$/m,
                            /^https?:\/\/command-center\.support\.aws\.a2z\.com.*$/m
                        ];
                        
                        for (const pattern of metadataPatterns) {
                            contentText = contentText.replace(pattern, '').trim();
                        }
                        
                        return {
                            title: title,
                            content: contentText
                        };
                    });

                    if (descContents.some(item => item.content.length > 0)) {
                        const englishContents = descContents.filter(item => isEnglishContent(item.content));
                        if (englishContents.length > 0) {
                            return englishContents;
                        }
                    }
                }
            }

            // Get all activity containers
            const activityContainers = document.querySelectorAll('div.activity-container, [id^="activity-container"]');
            for (let container of activityContainers) {
                const englishContents = findThreeEnglishPres(container);
                if (englishContents) {
                    return englishContents;
                }
            }

            throw new Error('No suitable English content found in any container');
        } catch (error) {
            console.error('Error during content search:', error);
            throw error;
        }
    }

    // Function to split text into chunks
    function splitTextIntoChunks(text, maxChunkSize) {
        if (!text) return [];
        
        // 如果文本小于最大块大小，直接返回整个文本
        if (text.length <= maxChunkSize) {
            return [text];
        }
        
        const chunks = [];
        let startIndex = 0;
        
        while (startIndex < text.length) {
            // 确定当前块的结束位置
            let endIndex = startIndex + maxChunkSize;
            
            // 如果没有到达文本结尾，尝试在句子或段落结束处分割
            if (endIndex < text.length) {
                // 尝试在段落处分割（优先）
                const paragraphBreak = text.lastIndexOf('\n\n', endIndex);
                if (paragraphBreak !== -1 && paragraphBreak > startIndex + maxChunkSize / 2) {
                    endIndex = paragraphBreak + 2; // 包含换行符
                } else {
                    // 尝试在句子处分割
                    const sentenceBreaks = ['. ', '! ', '? ', '.\n', '!\n', '?\n'];
                    let bestBreakPos = -1;
                    
                    for (const breakChar of sentenceBreaks) {
                        const pos = text.lastIndexOf(breakChar, endIndex);
                        if (pos !== -1 && pos > startIndex + maxChunkSize / 2 && pos > bestBreakPos) {
                            bestBreakPos = pos + breakChar.length;
                        }
                    }
                    
                    if (bestBreakPos !== -1) {
                        endIndex = bestBreakPos;
                    }
                }
            }
            
            // 添加当前块到结果中
            chunks.push(text.substring(startIndex, endIndex));
            startIndex = endIndex;
        }
        
        return chunks;
    }

    // Function to make translate request
    async function makeTranslateRequest(text, translateToChinese = false) {
        // 添加临时前端预处理函数，处理特殊格式的服务名称
        const preprocessServiceNames = (content) => {
            // 特殊处理带型号的EC2实例
            // 示例：将"Amazon EC2 G3"预处理为便于识别的格式
            let processed = content;
            
            // 处理"Amazon EC2 X型实例"格式，其中X是型号标识（如G3, T2, M5等）
            const ec2InstancePattern = /(Amazon\s+EC2\s+)([A-Z][0-9](?:\.[a-z0-9]+)?)/gi;
            processed = processed.replace(ec2InstancePattern, (match, prefix, instanceType) => {
                console.log(`检测到EC2实例型号: ${match}`);
                // 在发送前暂时将其标记，以便后端可以正确识别
                return `${prefix}(${instanceType} Instance)`;
            });
            
            // 处理"End of Life Notice"格式，这通常出现在服务生命周期通知中
            const eolPattern = /(Amazon\s+[A-Za-z0-9]+(?:\s+[A-Za-z0-9]+)*)\s+(End\s+of\s+Life\s+Notice)/gi;
            processed = processed.replace(eolPattern, (match, serviceName, notice) => {
                console.log(`检测到生命周期通知: ${match}`);
                // 确保服务名称与通知之间有明确分隔
                return `${serviceName} - ${notice}`;
            });
            
            // 特殊处理"[Action may be required]"开头的消息
            if (processed.includes('[Action may be required]') && processed.includes('Amazon EC2 G3')) {
                console.log("检测到特殊格式: [Action may be required] Amazon EC2 G3");
                // 直接进行针对性替换
                processed = processed.replace(
                    /\[Action may be required\] Amazon EC2 G3 End of Life Notice/g, 
                    "[Action may be required] Amazon Elastic Compute Cloud (Amazon EC2) G3 End of Life Notice"
                );
            }
            
            // 标记处理状态，便于调试
            if (processed !== content) {
                console.log("服务名称预处理已应用");
            }
            
            return processed;
        };
        
        // 尝试最多3次请求，处理临时网络错误
        let retryCount = 0;
        const maxRetries = 3;
        const retryDelay = 2000; // 2秒
        
        let lastError = null;
        
        while (retryCount <= maxRetries) {
            try {
                // 分割长文本为较小的块，防止请求过大
                const maxChunkSize = 12000;
                const chunks = splitTextIntoChunks(text, maxChunkSize);
                console.log(`将文本分割为${chunks.length}个块进行处理`);
                
                let processedText = '';
                let translatedText = '';
                
                for (let i = 0; i < chunks.length; i++) {
                    console.log(`Processing chunk ${i + 1}/${chunks.length}, length: ${chunks[i].length}`);
                    
                    // 准备API请求参数
                    const requestData = {
                        text: chunks[i]
                    };
                    
                    // 根据不同的请求类型选择不同的API端点和参数
                    let apiEndpoint = "https://qek0wlrzwb.execute-api.cn-north-1.amazonaws.com.cn/translate";
                    
                    // 处理不同的请求类型
                    if (translateToChinese) {
                        // 中文翻译模式 - 使用翻译专用的API端点
                        apiEndpoint = "https://qek0wlrzwb.execute-api.cn-north-1.amazonaws.com.cn/translate/chinese";
                        
                        // 确保参数格式正确
                        requestData.translateToChinese = true;
                        requestData.translate = true;
                        requestData.translate_to_chinese = true;  // 兼容不同的API参数命名
                        requestData.mode = 'translate';  // 确保API能理解这是翻译请求
                        requestData.targetLang = 'zh';  // 明确指定目标语言
                        requestData.sourceLang = 'en';  // 明确指定源语言
                        
                        console.log('发送中文翻译请求:', JSON.stringify(requestData));
                    } else {
                        // 英文替换模式 - 使用标准API端点
                        requestData.translateToChinese = false;
                        requestData.mode = 'replace';  // 只进行服务名称替换
                        
                        console.log('发送英文替换请求:', JSON.stringify(requestData));
                    }
                    
                    try {
                        // 使用Promise包装GM_xmlhttpRequest来发送请求
                        const response = await new Promise((resolve, reject) => {
                            GM_xmlhttpRequest({
                                method: "POST",
                                url: apiEndpoint,
                        headers: {
                                    "Content-Type": "application/json",
                                    "Accept": "application/json",
                                    "X-Request-Source": "tampermonkey-script",
                                    "X-Client-Version": "1.5.1"
                                },
                                data: JSON.stringify(requestData),
                                onload: function(response) {
                                    if (response.status === 200) {
                                        resolve(response);
                                    } else {
                                        reject(new Error(`HTTP错误: ${response.status}, 响应: ${response.statusText || '未知错误'}`));
                                    }
                                },
                                onerror: function(error) {
                                    console.error('网络请求失败:', error);
                                    reject(new Error('网络错误，无法连接到API服务'));
                                },
                                ontimeout: function() {
                                    reject(new Error('API请求超时'));
                                },
                                timeout: 30000 // 30秒超时
                            });
                        });
                        
                        console.log(`接收到API响应，状态码: ${response.status}`);
                        
                        // 解析JSON响应
                        let result;
                        try {
                            const parsedResponse = JSON.parse(response.responseText);
                            console.log('响应结构:', Object.keys(parsedResponse));
                            
                            // 处理API网关格式的响应
                            if (parsedResponse.statusCode && parsedResponse.body) {
                                console.log('检测到API网关格式响应');
                                // API网关返回的body可能是字符串形式的JSON
                                if (typeof parsedResponse.body === 'string') {
                                    try {
                                        result = JSON.parse(parsedResponse.body);
                                        console.log('解析body:', Object.keys(result));
                                    } catch (e) {
                                        console.log('Body不是JSON格式，按原样使用');
                                        result = { 
                                            processedText: parsedResponse.body,
                                            translatedText: parsedResponse.body
                                        };
                                    }
                                    } else {
                                    result = parsedResponse.body;
                                    }
                            } else {
                                // 直接使用解析后的响应
                                result = parsedResponse;
                            }
                        } catch(e) {
                            console.error('无法解析响应JSON:', e);
                            // 如果无法解析JSON，则将整个响应作为文本使用
                            result = { 
                                processedText: response.responseText,
                                translatedText: response.responseText
                            };
                        }
                        
                        // 检查结果中是否包含预期的字段
                    if (translateToChinese) {
                        const chunkTranslatedText = result.translatedText || result.translation;
                        
                            // 获取处理后的英文文本（如果有）
                            const chunkProcessedText = result.processedText || result.result;
                        if (chunkProcessedText) {
                            processedText += chunkProcessedText;
                        }
                        
                        // 添加翻译后的中文文本
                        if (chunkTranslatedText) {
                            translatedText += chunkTranslatedText;
                            console.log('添加翻译后的中文文本，当前长度:', translatedText.length);
                        } else {
                            console.error('响应中没有找到中文翻译:', result);
                            console.error('响应字段列表:', Object.keys(result));
                            if (result.error) {
                                console.error('响应中的错误信息:', result.error);
                            }
                                
                                // 尝试备用方法：直接使用英文进行显示
                                translatedText += `【未找到中文翻译，显示处理后的英文】\n\n${chunkProcessedText || chunks[i]}`;
                            console.log('使用备用内容替代中文翻译');
                        }
                    } else {
                        // 原文关键词替换请求只需要处理后的英文
                            const chunkProcessedText = result.processedText || result.result;
                            
                        if (chunkProcessedText) {
                            processedText += chunkProcessedText;
                            console.log('添加处理后的英文文本，当前长度:', processedText.length);
                        } else {
                            console.error('未找到处理后的英文文本:', result);
                            console.error('响应字段列表:', Object.keys(result));
                            if (result.error) {
                                console.error('响应中的错误信息:', result.error);
                            }
                            throw new Error('处理后的文本不存在，请检查后端响应');
                        }
                        }
                    } catch (requestError) {
                        // 如果当前请求失败，但不是最后一次重试，则记录错误并继续下一次重试
                        console.error(`处理块 ${i + 1}/${chunks.length} 时出错:`, requestError);
                        
                        // 如果是最后一次重试，则抛出错误
                        if (retryCount >= maxRetries) {
                            throw requestError;
                        }
                        
                        // 否则继续重试
                        break;
                    }
                    
                    if (i < chunks.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                }
                
                console.log('Final response prepared:', { 
                    processedText: processedText || text,  
                    translatedText: translatedText || '' 
                });
                
                return {
                    processedText: processedText || text,
                    translatedText: translatedText || ''
                };
                
            } catch (error) {
                console.error(`请求失败 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, error);
                lastError = error;
                
                // 如果还有重试机会，则等待后重试
                if (retryCount < maxRetries) {
                    retryCount++;
                    console.log(`将在${retryDelay / 1000}秒后重试...`);
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                    continue;
                }
                
                // 所有重试都失败，返回错误信息和原始文本
                console.error('所有重试均失败，使用原始文本');
                return {
                    processedText: text,
                    translatedText: `【系统提示: 处理请求失败】\n\n错误信息: ${lastError.message}\n\n${text}`
                };
            }
        }
    }

    // Function to translate text with retry logic
    async function translateWithRetry(text, retryCount = 0, translateToChinese = false) {
        const maxRetries = 5;
        const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 10000);

        try {
            return await makeTranslateRequest(text, translateToChinese);
        } catch (error) {
            console.error(`Translation attempt ${retryCount + 1} failed:`, error);
            
            if (retryCount < maxRetries) {
                console.log(`Retrying in ${retryDelay}ms...`);
                await new Promise(resolve => setTimeout(resolve, retryDelay));
                return translateWithRetry(text, retryCount + 1, translateToChinese);
            }
            
            throw new Error(`Translation failed after ${maxRetries} retries: ${error.message}`);
        }
    }

    // Function to display translation
    function displayTranslation(container, result) {
        // 清除现有的翻译结果
        const existingResults = container.querySelectorAll('.translation-result');
        existingResults.forEach(elem => elem.remove());

        // 创建结果容器
        const resultDiv = document.createElement('div');
        resultDiv.className = 'translation-result';
        resultDiv.style.cssText = 'margin: 10px 0; padding: 10px; border: 1px solid #e0e0e0; border-radius: 4px; background: #fafafa;';

        // 显示替换后的英文 (如果存在)
        if (result.processedText) {
            const processedDiv = document.createElement('div');
            processedDiv.className = 'processed-text';
            processedDiv.style.cssText = 'margin-bottom: 10px; padding: 15px; background: #E8F5E9; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word; color: #2E7D32; font-size: 14px; line-height: 1.6;';
            processedDiv.textContent = result.processedText;
            resultDiv.appendChild(processedDiv);
            
            // 如果只有英文内容，为英文内容添加复制按钮
            if (!result.translatedText) {
                const englishCopyButton = document.createElement('button');
                englishCopyButton.textContent = '复制英文';
                englishCopyButton.className = 'copy-button';
                englishCopyButton.style.cssText = `
                    padding: 8px 16px;
                    margin-top: 10px;
                    border: none;
                    border-radius: 4px;
                    background: #5C8CBF;
                    color: white;
                    cursor: pointer;
                    font-size: 14px;
                    transition: background 0.3s ease;
                `;
                englishCopyButton.onmouseover = () => englishCopyButton.style.background = '#4A71A3';
                englishCopyButton.onmouseout = () => englishCopyButton.style.background = '#5C8CBF';
                englishCopyButton.onclick = () => {
                    navigator.clipboard.writeText(result.processedText).then(() => {
                        englishCopyButton.textContent = '已复制';
                        setTimeout(() => {
                            englishCopyButton.textContent = '复制英文';
                        }, 2000);
                    }).catch(err => {
                        console.error('复制失败:', err);
                        englishCopyButton.textContent = '复制失败';
                        setTimeout(() => {
                            englishCopyButton.textContent = '复制英文';
                        }, 2000);
                    });
                };
                resultDiv.appendChild(englishCopyButton);
            }
        }

        // 显示中文翻译（如果有）
        if (result.translatedText) {
            const translatedDiv = document.createElement('div');
            translatedDiv.className = 'translated-text';
            translatedDiv.style.cssText = 'padding: 15px; background: #F3E5F5; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word; color: #4A148C; font-size: 14px; line-height: 1.6;';
            
            // 确保显示的是中文翻译文本，而不是英文内容
            if (typeof result.translatedText === 'string' && result.translatedText.trim() !== '') {
                // 检查是否包含中文字符
                if (/[\u4e00-\u9fa5]/.test(result.translatedText)) {
                    console.log('显示中文翻译内容');
                    translatedDiv.textContent = result.translatedText;
                } else {
                    console.warn('警告: 翻译结果不含中文，可能是API返回了英文内容');
                    
                    // 显示警告消息并保留原文
                    translatedDiv.innerHTML = `
                        <div style="color: #F44336; margin-bottom: 10px; padding: 5px; background: #FFEBEE; border-radius: 4px;">
                            ⚠️ 系统未返回中文翻译结果，显示原始内容
                        </div>
                        ${result.translatedText}
                    `;
                }
            } else {
                // 如果没有中文翻译或为空，显示提示信息
                translatedDiv.innerHTML = `
                    <div style="color: #F44336; margin-bottom: 10px; padding: 5px; background: #FFEBEE; border-radius: 4px;">
                        ⚠️ 无翻译结果，可能是API服务暂时不可用
                    </div>
                `;
                console.error('未获取到中文翻译结果');
            }
            
            resultDiv.appendChild(translatedDiv);

            // 添加复制按钮
            const copyButton = document.createElement('button');
            copyButton.textContent = result.processedText ? '复制全部' : '复制中文';
            copyButton.className = 'copy-button';
            copyButton.style.cssText = `
                padding: 8px 16px;
                margin-top: 10px;
                border: none;
                border-radius: 4px;
                background: #7B1FA2;
                color: white;
                cursor: pointer;
                font-size: 14px;
                transition: background 0.3s ease;
            `;
            copyButton.onmouseover = () => copyButton.style.background = '#6A1B9A';
            copyButton.onmouseout = () => copyButton.style.background = '#7B1FA2';
            copyButton.onclick = () => {
                // 根据是否有英文内容决定复制内容
                const textToCopy = result.processedText ? 
                    `${result.processedText}\n\n${result.translatedText}` : 
                    result.translatedText;
                    
                navigator.clipboard.writeText(textToCopy).then(() => {
                    copyButton.textContent = '已复制';
                    setTimeout(() => {
                        copyButton.textContent = result.processedText ? '复制全部' : '复制中文';
                    }, 2000);
                }).catch(err => {
                    console.error('复制失败:', err);
                    copyButton.textContent = '复制失败';
                    setTimeout(() => {
                        copyButton.textContent = result.processedText ? '复制全部' : '复制中文';
                    }, 2000);
                });
            };
            resultDiv.appendChild(copyButton);
        }

        // 添加到容器
        container.appendChild(resultDiv);
    }

    // Function to handle translation button click
    async function handleTranslateButtonClick(event, mode = 'both') {
        const button = event.target;
        const contentWrapper = button.closest('.content-wrapper');
        const content = contentWrapper.querySelector('pre').textContent;

        if (!content || !contentWrapper) return;

        try {
            // 重置重定向检测器
            if (typeof redirectDetection !== 'undefined') {
                redirectDetection.reset();
                console.log("重置重定向检测器");
            }
            
            // 根据模式设置按钮状态
            button.disabled = true;
            const originalText = button.textContent;
            button.textContent = '处理中...';

            // 记录原始内容，用于调试比较
            console.log("原始内容:", content);

            // 进行关键词替换处理
            let processedContent = content;

            // 存储处理后的内容和原始内容
            contentWrapper.dataset.processedText = processedContent;
            contentWrapper.dataset.originalContent = content;

            // 设置最大重试次数
            const maxRetries = 2;
            let translationResult = null;
            let retryCount = 0;
            let chineseContent = null;

            // 第1步：关键词替换处理
            console.log('第1步：执行关键词替换处理...');
            
            try {
                // 先执行关键词替换（不翻译成中文）
                translationResult = await makeTranslateRequest(processedContent, false);
                
                // 检查是否包含期望的服务名称替换
                if (processedContent.includes('Amazon EC2 G3') && 
                    (!translationResult.processedText || 
                     !translationResult.processedText.includes('Amazon Elastic Compute Cloud') || 
                     !translationResult.processedText.includes('EC2'))) {
                    
                    console.log("检测到服务名称替换未正确处理，尝试应用本地替换规则");
                    
                    // 应用本地替换逻辑，然后重试
                    while (retryCount < maxRetries) {
                        console.log(`重试替换请求 (${retryCount + 1}/${maxRetries})`);
                        
                        // 手动应用服务名称替换
                        processedContent = processedContent.replace(
                            /Amazon EC2 G3/g, 
                            "Amazon Elastic Compute Cloud (Amazon EC2) G3"
                        );
                        
                        // 如果有"[Action may be required]"，确保它被保留
                        if (content.includes('[Action may be required]')) {
                            processedContent = processedContent.replace(
                                /\[Action may be required\] (Amazon Elastic Compute Cloud)/g,
                                "[Action may be required] $1"
                            );
                        }
                        
                        // 更新存储的处理内容
                        contentWrapper.dataset.processedText = processedContent;
                        
                        // 重试替换请求
                        translationResult = await makeTranslateRequest(processedContent, false);
                        
                        // 检查结果是否包含期望的服务名称格式
                        if (translationResult.processedText && 
                            translationResult.processedText.includes('Amazon Elastic Compute Cloud')) {
                            console.log("服务名称替换成功");
                            break;
                        }
                        
                        retryCount++;
                    }
                }

                // 添加测试用例 - 确保元素存在后再访问其属性
                const debugModeElement = document.getElementById('debug-mode');
                if (debugModeElement && debugModeElement.checked) {
                    const testContent = `${content}\n\n[测试案例] Amazon EC2 G3 End of Life Notice`;
                    console.log("添加测试案例：", testContent);
                    const testResult = await makeTranslateRequest(testContent, false);
                    console.log("测试案例结果：", testResult);
                    
                    // 检查测试结果
                    const expectedPhrase = "Amazon Elastic Compute Cloud";
                    if (testResult.processedText && testResult.processedText.includes(expectedPhrase)) {
                        console.log("测试案例服务名称替换成功");
                    } else {
                        console.warn("测试案例服务名称替换失败");
                    }
                }

                // 更新处理后的文本
                if (translationResult && translationResult.processedText) {
                    processedContent = translationResult.processedText;
                    contentWrapper.dataset.processedText = processedContent;
                }
                
                // 根据模式决定是否执行中文翻译
                if (mode === 'english' || mode === 'both') {
                    // 显示英文处理结果
                    displayTranslation(contentWrapper, {
                        processedText: processedContent,
                        translatedText: mode === 'both' ? '' : null // 不显示中文翻译区域
                    });
                    
                    // 如果只需要英文处理，则结束执行
                    if (mode === 'english') {
                        console.log('完成英文替换流程');
                        
                        // 恢复按钮状态
                        button.disabled = false;
                        button.textContent = originalText;
                        
                        // 已经显示了结果，但不隐藏原按钮，只添加重新处理按钮
                        addReprocessButton(contentWrapper);
                        return;
                    }
                }
                
                // 如果需要中文翻译，继续执行下面的代码
                if (mode === 'chinese' || mode === 'both') {
                    // 第2步：翻译成中文
                    console.log('第2步：执行中文翻译...');
                    console.log('待翻译的英文内容长度:', processedContent.length);
                    if (processedContent.length > 100) {
                        console.log('内容样本:', processedContent.substring(0, 100) + '...');
                    }
                    
                    try {
                        // 直接使用预处理后的英文文本进行中文翻译
                        console.log('调用中文翻译API...');
                        
                        // 确保使用正确的API端点
                        const correctApiEndpoint = "https://qek0wlrzwb.execute-api.cn-north-1.amazonaws.com.cn/translate/chinese";
                        
                        // 使用处理后的英文文本进行中文翻译
                        const translationToChineseResult = await makeTranslateRequest(processedContent, true);
                        console.log('中文翻译API返回结果结构:', Object.keys(translationToChineseResult));
                        
                        // 尝试从不同字段找到翻译结果
                        let chineseContent = null;
                        
                        // 按优先级检查可能包含中文翻译的字段
                        if (translationToChineseResult.translatedText) {
                            chineseContent = translationToChineseResult.translatedText;
                            console.log('使用translatedText字段作为中文翻译');
                        } else if (translationToChineseResult.translated) {
                            chineseContent = translationToChineseResult.translated;
                            console.log('使用translated字段作为中文翻译');
                        } else if (translationToChineseResult.translation) {
                            chineseContent = translationToChineseResult.translation;
                            console.log('使用translation字段作为中文翻译');
                        } else if (translationToChineseResult.chinese) {
                            chineseContent = translationToChineseResult.chinese;
                            console.log('使用chinese字段作为中文翻译');
                        } else {
                            // 尝试直接调用translate/chinese端点获取翻译结果
                            console.log('直接请求translate/chinese端点获取翻译结果');
                            
                            try {
                                // 实现后备翻译方案1：直接调用API
                                console.log("尝试备用翻译方案1：直接调用翻译API");
                                
                                // 使用不同的API接口格式
                                const translationApiEndpoint = "https://qek0wlrzwb.execute-api.cn-north-1.amazonaws.com.cn/translate/text";
                                
                                const directTranslationResult = await new Promise((resolve, reject) => {
                                    GM_xmlhttpRequest({
                                        method: "POST",
                                        url: translationApiEndpoint,
                                        data: JSON.stringify({ 
                                            text: processedContent,
                                            translateToChinese: true,
                                            targetLang: 'zh',
                                            sourceLang: 'en',
                                            disable_cache: true, // 禁用缓存以避免返回旧结果
                                            timestamp: new Date().getTime() // 添加时间戳确保请求唯一性
                                        }),
                                        headers: { 
                                            "Content-Type": "application/json",
                                            "X-Custom-Client-ID": "mass-email-extension-v1.0" // 添加客户端标识
                                        },
                                        onload: function(response) {
                                            // 详细记录响应信息，帮助调试
                                            console.log(`备用翻译响应状态: ${response.status}`);
                                            if (response.responseText && response.responseText.length < 1000) {
                                                console.log(`备用翻译响应内容: ${response.responseText}`);
                                            } else {
                                                console.log(`备用翻译响应内容长度: ${response.responseText ? response.responseText.length : 0}`);
                                            }
                                            
                                            // 检查URL重定向
                                            if (response.finalUrl) {
                                                console.log(`响应最终URL: ${response.finalUrl}`);
                                                const redirectResult = redirectDetection.detect(response.finalUrl);
                                                if (redirectResult.isDetected) {
                                                    console.error(redirectResult.message);
                                                    if (redirectResult.isLoop) {
                                                        // 重定向循环，显示详细错误信息
                                                        console.error("检测到API重定向循环，提供更多诊断信息");
                                                        console.error(`重定向URL: ${response.finalUrl}`);
                                                        console.error(`重定向次数: ${redirectDetection.redirectCount}`);
                                                        console.error(`请求时间: ${new Date().toISOString()}`);
                                                        
                                                        // 显示错误信息
                                                        displayTranslation(contentWrapper, {
                                                            processedText: mode === 'chinese' ? null : processedContent,
                                                            translatedText: `【翻译服务错误】\n\n错误类型: API重定向循环\n请求时间: ${new Date().toLocaleString()}\n\n此错误通常由API配置问题导致，请将此信息提供给管理员，以便排查问题。\n\n原文内容:\n${processedContent.substring(0, 300)}...`
                                                        });
                                                        
                                                        // 恢复按钮状态并添加重试按钮
                                                        button.disabled = false;
                                                        button.textContent = originalText;
                                                        addReprocessButton(contentWrapper);
                                                        return;
                                                    }
                                                    reject(new Error(redirectResult.message));
                                                    return;
                                                }
                                            }
                                            
                                            if (response.status === 200) {
                                                resolve(response);
                                            } else {
                                                // 错误处理改进
                                                console.error(`直接翻译请求失败，状态码: ${response.status}`);
                                                
                                                // 检查是否是不正确的重定向
                                                if (response.finalUrl && response.finalUrl.includes('issues.cn-northwest-1.amazonaws.cn')) {
                                                    console.error("检测到API错误重定向：", response.finalUrl);
                                                    reject(new Error(`API配置错误，请联系系统管理员`));
                                                } else {
                                                    reject(new Error(`翻译服务暂时不可用`));
                                                }
                                            }
                                        },
                                        onerror: function(error) {
                                            // 详细记录错误信息
                                            console.error("备用翻译请求错误:", error);
                                            reject(error || new Error('翻译服务连接失败'));
                                        },
                                        ontimeout: () => reject(new Error('翻译请求超时'))
                                    });
                                });
                                
                                const parsedResult = JSON.parse(directTranslationResult.responseText);
                                console.log('直接翻译请求返回结果:', Object.keys(parsedResult));
                                
                                if (parsedResult.body) {
                                    const bodyData = typeof parsedResult.body === 'string' ? 
                                        JSON.parse(parsedResult.body) : parsedResult.body;
                                        
                                    if (bodyData.translatedText) {
                                        chineseContent = bodyData.translatedText;
                                        console.log('从直接翻译请求获取到中文翻译');
                                    }
                                } else if (parsedResult.translatedText) {
                                    chineseContent = parsedResult.translatedText;
                                    console.log('从直接翻译请求获取到中文翻译');
                                }
                            } catch (directTranslateError) {
                                console.error('直接翻译请求失败:', directTranslateError);
                            }
                        }
                        
                        // 检查是否成功获取中文翻译
                        if (chineseContent && typeof chineseContent === 'string') {
                            console.log('获取到的中文翻译长度:', chineseContent.length);
                            if (chineseContent.length > 100) {
                                console.log('中文翻译样本:', chineseContent.substring(0, 100) + '...');
                            }
                            
                            // 确认翻译结果是否包含中文字符
                            if (!/[\u4e00-\u9fa5]/.test(chineseContent)) {
                                console.warn('警告: 翻译结果不包含中文字符！添加诊断信息');
                                console.log(`翻译结果长度: ${chineseContent ? chineseContent.length : 0}`);
                                console.log(`原文长度: ${processedContent.length}`);
                                console.log(`请求时间: ${new Date().toISOString()}`);
                                console.log(`浏览器UA: ${navigator.userAgent}`);
                                
                                // 返回更详细的错误信息
                                chineseContent = `【翻译服务返回了不包含中文的内容】

错误信息: 可能是翻译服务内部错误或配置问题
时间: ${new Date().toLocaleString()}
请求ID: ${Date.now().toString(36)}

请将此信息提供给管理员以便排查问题。

原文内容:
${processedContent.substring(0, 300)}...`;
                            }
                            
                            // 显示中文翻译结果
                            displayTranslation(contentWrapper, {
                                // 在'chinese'模式下不显示英文，只显示中文翻译
                                processedText: mode === 'chinese' ? null : processedContent,
                                translatedText: chineseContent
                            });
                            
                            console.log('完成中文翻译流程');
                            
                            // 恢复按钮状态
                            button.disabled = false;
                            button.textContent = originalText;
                            
                            // 添加"重新处理"按钮
                            addReprocessButton(contentWrapper);
                        } else {
                            console.error('未能获取有效的中文翻译结果');
                            
                            // 显示备用内容
                            displayTranslation(contentWrapper, {
                                processedText: null,
                                translatedText: `【翻译服务暂时不可用】\n\n${processedContent}`
                            });
                            
                            // 恢复按钮状态
                            button.disabled = false;
                            button.textContent = originalText;
                            addReprocessButton(contentWrapper);
                        }
                    } catch (error) {
                        console.error('中文翻译处理出错:', error);
                        console.error('错误栈:', error.stack);
                        console.error('浏览器信息:', navigator.userAgent);
                        console.error('请求时间:', new Date().toISOString());
                        console.error('原文长度:', processedContent.length);
                        
                        // 捕获更多可能有用的错误上下文
                        try {
                            console.error('网络状态:', navigator.onLine ? '在线' : '离线');
                            console.error('内存使用:', window.performance && window.performance.memory ? 
                                JSON.stringify(window.performance.memory) : '无法获取');
                        } catch (diagError) {
                            console.error('获取诊断信息出错:', diagError);
                        }
                        
                        // 尝试直接显示处理后的英文（作为备用方案）
                        if (mode === 'chinese') {
                            console.log('错误处理：在中文区域显示处理后的英文文本');
                            displayTranslation(contentWrapper, {
                                processedText: null,
                                translatedText: `【翻译服务错误】

错误类型: ${error.name}
错误消息: ${error.message}
时间: ${new Date().toLocaleString()}
请求ID: ${Date.now().toString(36)}

请将此错误信息提供给管理员，以便排查问题。

原文内容:
${processedContent.substring(0, 300)}...`
                            });
                            
                            // 恢复按钮状态
                            button.disabled = false;
                            button.textContent = originalText;
                            addReprocessButton(contentWrapper);
                        }
                    }
                }
            } catch (error) {
                console.error("处理过程中出错:", error);
                button.textContent = originalText;
                button.disabled = false;
                alert(`处理出错: ${error.message || '未知错误'}`);
            }
        } catch (error) {
            console.error('Operation failed:', error);

            // 格式化友好的错误信息
            let friendlyErrorMessage;
            if (error.message.includes('请检查响应格式')) {
                friendlyErrorMessage = '数据格式错误，请联系管理员';
            } else if (error.message.includes('处理后的文本不存在')) {
                friendlyErrorMessage = '处理失败，请重试或联系管理员';
            } else if (error.message.includes('翻译后的文本不存在')) {
                friendlyErrorMessage = '翻译失败，请重试或联系管理员';
            } else {
                friendlyErrorMessage = '操作失败: ' + error.message;
            }

            // 恢复按钮状态
            button.textContent = originalText;
            button.disabled = false;

            // 显示错误消息
            alert(friendlyErrorMessage);
        }
    }

    // 辅助函数：添加重新处理按钮
    function addReprocessButton(contentWrapper) {
        // 检查是否已经存在重新处理按钮
        if (contentWrapper.querySelector('.reprocess-button')) {
            return;
        }
        
        // 添加"重新处理"按钮
        const reprocessButton = document.createElement('button');
        reprocessButton.textContent = '重新处理';
        reprocessButton.className = 'reprocess-button';
        reprocessButton.style.cssText = `
            padding: 8px 16px;
            margin-top: 10px;
            margin-left: 10px;
            border: none;
            border-radius: 4px;
            background: #8BC9A3;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        `;
        reprocessButton.onmouseover = () => {
            reprocessButton.style.background = 'linear-gradient(145deg, #8BC9A3, #A8D8B9)';
            reprocessButton.style.transform = 'translateY(-2px)';
        };
        reprocessButton.onmouseout = () => {
            reprocessButton.style.background = '#8BC9A3';
            reprocessButton.style.transform = 'translateY(0)';
        };
        reprocessButton.onclick = (e) => {
            // 删除当前的结果显示
            const existingResults = contentWrapper.querySelectorAll('.translation-result');
            existingResults.forEach(elem => elem.remove());
            
            // 移除重新处理按钮
            reprocessButton.remove();
        };
        contentWrapper.appendChild(reprocessButton);
    }

    // Function to show results
    async function showResults() {
        console.log('开始显示结果');

        // 隐藏提取按钮
        extractButton.style.display = 'none';

        // 清除现有的结果容器
        const existingContainer = document.querySelector('.result-container');
        if (existingContainer) {
            existingContainer.remove();
        }

        try {
            // 获取内容 - 使用原有的waitForDynamicContent函数
            const contents = await waitForDynamicContent();

            // 计算内容总长度，用于自适应布局
            const totalTextLength = contents.reduce((sum, content) => sum + (content.content?.length || 0), 0);
            const isLongContent = totalTextLength > 3000;

            // 创建结果容器
            const resultContainer = document.createElement('div');
            resultContainer.className = 'result-container';

            // 优化面板整体布局
            resultContainer.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 85%;
                max-width: 1200px;
                max-height: 85vh;
                background: #FFFEF7;
                border-radius: 8px;
                box-shadow: 0 5px 20px rgba(0,0,0,0.15);
                z-index: 10000;
                padding: 20px;
                overflow: auto;
                display: flex;
                flex-direction: column;
                font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
            `;

            // 创建标题栏
            const titleBar = document.createElement('div');
            titleBar.style.cssText = `
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
                padding-bottom: 10px;
                border-bottom: 1px solid #E0E0E0;
            `;

            // 添加标题
            const titleElement = document.createElement('h2');
            titleElement.textContent = '邮件内容翻译';
            titleElement.style.cssText = `
                margin: 0;
                color: #2E5E4E;
                font-size: 18px;
                font-weight: 500;
            `;
            titleBar.appendChild(titleElement);

            // 添加控制按钮组
            const controlButtons = document.createElement('div');
            controlButtons.style.cssText = `
                display: flex;
                gap: 10px;
            `;

            // 创建关闭按钮
            const closeButton = document.createElement('button');
            closeButton.innerHTML = '&times;';
            closeButton.title = '关闭面板';
            closeButton.style.cssText = `
                background: none;
                border: none;
                font-size: 20px;
                cursor: pointer;
                padding: 5px;
                color: #666;
                transition: color 0.3s ease;
            `;
            closeButton.onmouseover = () => closeButton.style.color = '#ff4d4f';
            closeButton.onmouseout = () => closeButton.style.color = '#666';
            closeButton.onclick = function() {
                resultContainer.remove();
                // 重新显示提取按钮
                extractButton.style.display = 'block';
            };
            controlButtons.appendChild(closeButton);

            titleBar.appendChild(controlButtons);
            resultContainer.appendChild(titleBar);

            // 创建内容区域容器
            const contentArea = document.createElement('div');
            contentArea.style.cssText = `
                display: flex;
                flex-wrap: wrap;
                gap: 20px;
                margin-top: 10px;
                justify-content: center;
                overflow: visible;
            `;

            // 为每个内容创建独立的容器
            contents.forEach((content, index) => {
                const contentWrapper = document.createElement('div');
                contentWrapper.className = 'content-wrapper';

                // 根据内容长度动态调整布局
                const contentLength = content.content?.length || 0;
                const useFullWidth = isLongContent || contentLength > 1500;

                contentWrapper.style.cssText = `
                    flex: ${useFullWidth ? '1 1 100%' : '1 1 300px'};
                    min-width: ${useFullWidth ? '80%' : '300px'};
                    max-width: ${useFullWidth ? '95%' : '400px'};
                    overflow-y: visible;
                    padding: 15px;
                    border: 1px solid #E0E0E0;
                    border-radius: 8px;
                    background: #FFFFFF;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
                    transition: all 0.3s ease;
                    border: 1px solid #8BC9A3;
                `;

                contentWrapper.onmouseover = () => {
                    contentWrapper.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
                };
                contentWrapper.onmouseout = () => {
                    contentWrapper.style.boxShadow = '0 2px 8px rgba(0,0,0,0.05)';
                };

                // 添加标题
                const titleElement = document.createElement('h3');

                // 预定义的标准标题列表
                const standardTitles = [
                    'Message Subject', 
                    'Message Body', 
                    'PHD Message Body', 
                    'CloudWatch Event Template', 
                    'Health API'
                ];

                // 判断取得的标题是否为标准标题，如果不是则使用默认值
                let finalTitle = content.title || '';
                if (!standardTitles.some(standardTitle => finalTitle.includes(standardTitle))) {
                    // 如果标题不是标准标题，使用默认值
                    finalTitle = (index === 0 ? 'Message Subject' : 
                                index === 1 ? 'Message Body' : 
                                'PHD Message Body');
                }

                titleElement.textContent = finalTitle;
                titleElement.style.cssText = `
                    margin: 0 0 10px 0;
                    color: #2E5E4E;
                    font-size: 16px;
                    font-weight: 500;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                `;
                contentWrapper.appendChild(titleElement);

                // 添加内容
                const contentElement = document.createElement('pre');

                // 在显示前再次过滤元数据文本
                let displayContent = content.content || '处理失败';
                const uiMetadataPatterns = [
                    /^The following is taken directly from.*$/m,
                    /^The following message was posted:.*$/m,
                    /^AWS_[A-Z0-9_]+$/m,
                    /^https?:\/\/command-center\.support\.aws\.a2z\.com.*$/m,
                    /^\/+\*\[@data-link="visible\{:state == 'synced'\}"\/\w+\[\d+\]\]?$/m,  // 精确匹配问题中的XPath模式
                    /^\/+\*\[@data-link="visible.*$/m  // 更通用的data-link XPath模式
                ];

                for (const pattern of uiMetadataPatterns) {
                    displayContent = displayContent.replace(pattern, '').trim();
                }

                contentElement.textContent = displayContent;
                contentElement.className = contentLength > 500 ? 'collapsed' : '';

                // 优化内容区域，减少滚动需求
                contentElement.style.cssText = `
                    white-space: pre-wrap;
                    word-wrap: break-word;
                    margin: 0 0 10px 0;
                    font-family: "Consolas", "Monaco", monospace;
                    font-size: 14px;
                    line-height: 1.6;
                    padding: 12px;
                    background: ${index % 2 === 0 ? '#EFF8FF' : '#F0F9F0'};
                    border-radius: 6px;
                    color: #333333;
                    max-height: ${contentLength > 1000 ? '400px' : 'none'};
                    overflow-y: ${contentLength > 1000 ? 'auto' : 'visible'};
                    transition: max-height 0.3s ease;
                `;

                // 为长内容添加折叠/展开功能
                if (contentLength > 500) {
                    contentElement.style.maxHeight = '200px';

                    const expandButton = document.createElement('button');
                    expandButton.textContent = '展开全部';
                    expandButton.className = 'expand-button';
                    expandButton.style.cssText = `
                        padding: 4px 10px;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                        background: #f0f0f0;
                        color: #555;
                        font-size: 12px;
                        margin-left: 10px;
                        transition: all 0.3s ease;
                    `;

                    expandButton.onmouseover = () => {
                        expandButton.style.background = '#e0e0e0';
                    };
                    expandButton.onmouseout = () => {
                        expandButton.style.background = '#f0f0f0';
                    };

                    expandButton.onclick = function() {
                        if (contentElement.style.maxHeight === '200px') {
                            contentElement.style.maxHeight = '1000px';
                            expandButton.textContent = '收起';
                        } else {
                            contentElement.style.maxHeight = '200px';
                            expandButton.textContent = '展开全部';
                        }
                    };

                    titleElement.appendChild(expandButton);
                }

                contentWrapper.appendChild(contentElement);

                // 添加按钮容器
                const buttonContainer = document.createElement('div');
                buttonContainer.className = 'button-container';
                buttonContainer.style.cssText = `
                    display: flex;
                    gap: 10px;
                    margin-top: 10px;
                `;
                
                // 添加"生成英文"按钮
                const englishButton = document.createElement('button');
                englishButton.textContent = '生成英文';
                englishButton.className = 'action-button english-button';
                englishButton.style.cssText = `
                    padding: 8px 16px;
                    border: none;
                    border-radius: 6px;
                    cursor: pointer;
                    background: linear-gradient(145deg, #88B1E5, #5C8CBF);
                    color: #FFFFFF;
                    font-size: 14px;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
                    font-weight: 500;
                    flex: 1;
                `;

                englishButton.onmouseover = () => {
                    englishButton.style.background = 'linear-gradient(145deg, #5C8CBF, #88B1E5)';
                    englishButton.style.transform = 'translateY(-2px)';
                    englishButton.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
                };

                englishButton.onmouseout = () => {
                    englishButton.style.background = 'linear-gradient(145deg, #88B1E5, #5C8CBF)';
                    englishButton.style.transform = 'translateY(0)';
                    englishButton.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
                };

                // 为"生成英文"按钮添加点击事件
                englishButton.onclick = function(e) {
                    handleTranslateButtonClick(e, 'english');
                };
                
                // 添加"生成中文"按钮
                const chineseButton = document.createElement('button');
                chineseButton.textContent = '生成中文';
                chineseButton.className = 'action-button chinese-button';
                chineseButton.style.cssText = `
                    padding: 8px 16px;
                    border: none;
                    border-radius: 6px;
                    cursor: pointer;
                    background: linear-gradient(145deg, #A8D8B9, #8BC9A3);
                    color: #2E5E4E;
                    font-size: 14px;
                    transition: all 0.3s ease;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
                    font-weight: 500;
                    flex: 1;
                `;

                chineseButton.onmouseover = () => {
                    chineseButton.style.background = 'linear-gradient(145deg, #8BC9A3, #A8D8B9)';
                    chineseButton.style.transform = 'translateY(-2px)';
                    chineseButton.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
                };

                chineseButton.onmouseout = () => {
                    chineseButton.style.background = 'linear-gradient(145deg, #A8D8B9, #8BC9A3)';
                    chineseButton.style.transform = 'translateY(0)';
                    chineseButton.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
                };

                // 为"生成中文"按钮添加点击事件
                chineseButton.onclick = function(e) {
                    handleTranslateButtonClick(e, 'chinese');
                };
                
                // 将按钮添加到按钮容器
                buttonContainer.appendChild(englishButton);
                buttonContainer.appendChild(chineseButton);
                
                // 将按钮容器添加到内容包装器
                contentWrapper.appendChild(buttonContainer);

                contentArea.appendChild(contentWrapper);
            });

            resultContainer.appendChild(contentArea);
            document.body.appendChild(resultContainer);

            // 添加键盘事件监听
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    const container = document.querySelector('.result-container');
                    if (container) {
                        container.remove();
                        extractButton.style.display = 'block';
                    }
                }
            });

        } catch (error) {
            console.error('Error showing results:', error);
            alert('无法获取内容，请重试');
            // 出错时也要重新显示按钮
            extractButton.style.display = 'block';
        }
    }

    // Add Extract button to page
    const extractButton = document.createElement('div');
    extractButton.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        background: #A8D8B9;
        color: #2E5E4E;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: move;
        font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        font-size: 14px;
        z-index: 9999;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        user-select: none;
        font-weight: 500;
        text-shadow: none;
        transition: all 0.3s ease;
        border: 1px solid #8BC9A3;
    `;
    extractButton.textContent = 'mass email 翻译';

    // Add dragging functionality
    let isDragging = false;
    let currentX;
    let currentY;
    let initialX;
    let initialY;
    let xOffset = 0;
    let yOffset = 0;

    function setTranslate(xPos, yPos) {
        extractButton.style.transform = `translate(${xPos}px, ${yPos}px)`;
    }

    function dragStart(e) {
        if (e.type === "mousedown") {
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;
        }
        isDragging = true;
        e.preventDefault();
    }

    function dragEnd(e) {
        initialX = currentX;
        initialY = currentY;
        isDragging = false;
    }

    function drag(e) {
        if (isDragging) {
            e.preventDefault();
            currentX = e.clientX - initialX;
            currentY = e.clientY - initialY;
            xOffset = currentX;
            yOffset = currentY;
            setTranslate(currentX, currentY);
        }
    }

    // Add click handler
    let mouseDownTime;
    extractButton.addEventListener('mousedown', (e) => {
        mouseDownTime = new Date().getTime();
        dragStart(e);
    });

    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', (e) => {
        const mouseUpTime = new Date().getTime();
        dragEnd(e);
        if (mouseUpTime - mouseDownTime < 200 && e.target === extractButton) {
            showResults();
        }
    });

    document.addEventListener('mouseout', (e) => {
        if (e.target === document.documentElement && isDragging) {
            dragEnd(e);
        }
    });

    // Add button to page
    document.body.appendChild(extractButton);

    // 添加重定向检测函数
    const redirectDetection = {
        lastRedirectUrl: null,
        redirectCount: 0,
        maxRedirects: 3,
        redirectPattern: /issues\.cn-northwest-1\.amazonaws\.cn/,
        
        // 检测重定向
        detect(url) {
            if (this.redirectPattern.test(url)) {
                // 检测到错误的重定向
                this.redirectCount++;
                this.lastRedirectUrl = url;
                
                console.error(`检测到第${this.redirectCount}次API重定向到非法URL: ${url}`);
                
                if (this.redirectCount >= this.maxRedirects) {
                    console.error(`已达到最大重定向次数(${this.maxRedirects})，疑似重定向循环`);
                    return {
                        isDetected: true,
                        isLoop: true,
                        message: `API重定向循环，请联系系统管理员`
                    };
                }
                
                return {
                    isDetected: true,
                    isLoop: false,
                    message: `API重定向到非法URL: ${url}`
                };
            }
            
            return { isDetected: false };
        },
        
        // 重置计数器
        reset() {
            this.redirectCount = 0;
            this.lastRedirectUrl = null;
        }
    };
})();