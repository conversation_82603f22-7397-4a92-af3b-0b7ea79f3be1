---
alwaysApply: true
---
# 模式匹配引擎指南（Pattern Matching Engine）- v2.0

## 文档范围（Scope）
- 本文档聚焦“应用层/服务层”的匹配引擎设计与实现：架构、组件职责、处理流程、性能策略与API契约
- 描述引擎如何利用数据库中外化的规则完成高性能识别与装配
- 涉及必要的数据契约（读取哪些表/字段、查询模式、索引依赖）以指导与数据库的协作

不包含（Out-of-scope）：
- 数据库表结构/枚举/约束/DDL 的完整定义、索引DDL 与迁移脚本（参见数据库开发指南）
- 数据库层的日常运维、健康检查、备份与监控（参见数据库开发指南）

参考交叉文档：
- 数据库开发指南：.kiro\steering\database-development-guide.md
- AWS 服务名称同步模块：.kiro\steering\aws-service-sync-guide.md（说明：数据消费由翻译模块直接从数据库读取，AWS服务同步模块不提供任何对外接口）

---

## 可视化 - 端到端流程

```mermaid
sequenceDiagram
    autonumber
    participant Client as Client
    participant Engine as Matching Engine
    participant Loader as PatternLoader
    participant DB as PostgreSQL (Rules)
    participant Guard as BoundaryGuard
    participant Asm as SuffixAssembler
    participant MT as MentionTracker

    Client->>Engine: process(text, job_id?)
    Engine->>Loader: ensureInitialized()
    Loader->>DB: SELECT active & valid patterns (partial index)
    DB-->>Loader: pattern rows (regex, metadata, priority)
    Loader-->>Engine: automaton + patterns_cache

    Engine->>Engine: Aho-Corasick scan => candidates (O(n))
    Engine->>Engine: segment regex on candidates (priority order)
    Engine->>Guard: validate(matches, text)
    Guard-->>Engine: validated matches
    Engine->>Engine: resolve conflicts (priority/longest)
    Engine->>Asm: assemble if compound (suffixGroup)
    Asm-->>Engine: final service name(s)
    Engine->>MT: update mention state (job_id?)
    MT-->>Engine: state updated
    Engine-->>Client: replaced_text + placeholders + state + stats
```

```mermaid
flowchart TD
    A[Start] --> B[Load rules via partial index]
    B --> C[Build automaton & patterns cache]
    C --> D[Aho-Corasick scan => candidates]
    D --> E[Segmented regex on candidate regions]
    E --> F{Context protected?}
    F -- Yes --> G[Drop match]
    F -- No --> H[Keep match]
    G --> I[Next candidate]
    H --> J[Resolve conflicts by priority/length]
    J --> K{Compound pattern?}
    K -- Yes --> L[Assemble with Suffix]
    K -- No --> M[Pick full/short via MentionTracker]
    L --> N[Generate placeholder & state]
    M --> N
    N --> O[Join replaced_text & return stats]
    O --> P[End]
```


## 架构总览
- 关键目标：O(n) 级预扫描 + 小范围分段正则，显著降低整体匹配成本；配合“逻辑外化”的规则与优先级裁剪，实现高吞吐与稳定精度
- 架构要点：
  1) PatternLoader：加载活跃/有效规则，构建关键词映射与自动机
  2) ServiceMatcher：基于 Aho-Corasick 产生候选，在小范围内应用正则并抽取后缀
  3) BoundaryGuard：根据上下文保护规则做二次校验
  4) SuffixAssembler：复合后缀装配，保留关键信息
  5) MentionTracker：首次/后续提及策略，决定全称/简称

## 组件职责与接口

### PatternLoader
- 职责：
  - 从数据库一次性拉取活跃且验证通过的规则（is_active = TRUE AND validation_status = 'valid'）
  - 构建 Aho-Corasick 自动机与“关键词→模式集合”的缓存
- 依赖数据库：
  - 部分索引 idx_regex_patterns_active_valid
  - JSONB 元数据（metadata）GIN 索引（用于复合后缀/类别选择）
- 关键查询（示例）：
  - 参见数据库开发指南“性能优化要点/组件化架构支持”中的 PatternLoader 查询

### ServiceMatcher
- 职责：
  - 使用自动机 O(n) 预扫描获取候选（end_index, keyword）
  - 仅对候选局部文本应用该关键词的有序模式列表（按 priority）进行正则验证
  - 提取复合模式的后缀捕获组
- 冲突策略：
  - 优先级优先，其次最长匹配，最后按模式ID稳定选择（可选）

### BoundaryGuard
- 职责：
  - 应用上下文保护（CONTEXT_PROTECTED）模式，剔除 ARN/URL/代码标识符等受保护范围内的误匹配
- 数据契约：
  - 使用 pattern_type = 'CONTEXT_PROTECTED' 的规则集合

注意：模式本体禁止使用“可变宽度负向后行”（例如 `(?<!https?://[^\s]*)`）。具体边界（如 ARN/URL）由 BoundaryGuard 通过如下基线规则识别并统一过滤：
- `CONTEXT_ARN_GENERIC`：`arn:(?:aws|aws-cn):\S+`
- `CONTEXT_URL_GENERIC`：`https?://\S+`

### SuffixAssembler
- 职责：
  - 对 metadata.isCompoundWithSuffix == true 的模式，按 suffixGroup 提取后缀，与服务名合成
- 规则：
  - 首次提及使用 full_name_en，后续使用 short_name_en（可配置）

复合后缀的结构化标记统一存放于 `regex_patterns.metadata`（JSONB），例如：
```json
{
  "isCompoundWithSuffix": true,
  "suffixGroup": 1
}
```

### MentionTracker
- 职责：
  - 基于 translation_jobs.service_mention_state 跟踪并决定全称/简称使用
- 数据契约：
  - translation_jobs 表 JSONB 字段及 GIN 索引

## 处理流程（高层）
1. 初始化
   - PatternLoader 从 regex_patterns 拉取活跃/有效规则，利用部分索引与排序（priority DESC, id ASC）
   - 构建自动机与缓存（关键词→模式列表）
2. 处理文本
   - Aho-Corasick 预扫描产出候选
   - 对候选局部（±N字符）按优先级应用正则，抽取 suffix（如有）
   - BoundaryGuard 过滤上下文误匹配
   - 冲突消解：优先级/最长匹配/稳定选择
   - MentionTracker 确定名称形式；SuffixAssembler 合成复合表达
   - 生成占位符映射与 service_mention_state，并输出 replaced_text

伪代码（简化）：
```python
candidates = automaton.iter(text)
matches = service_matcher.match_candidates(text, candidates, patterns_cache)
validated = boundary_guard.validate_matches(matches, text)
final = resolve_conflicts(validated)
output = assemble_and_replace(final, text, mention_tracker, suffix_assembler)
```

## 与数据库的契约（Contracts）
- 表与字段：
  - regex_patterns(pattern_name, pattern_type, regex_string, metadata JSONB, priority, service_code, related_service_id, is_active, validation_status)
  - service_names(authoritative_full_name, base_name, full_name_en, short_name_en, service_code, is_active, last_synced_at)
  - translation_jobs(placeholder_map JSONB, service_mention_state JSONB)
- 必要索引：
  - idx_regex_patterns_active_valid（部分索引）
  - idx_regex_patterns_metadata（GIN）
  - idx_service_names_active_code（部分索引）
  - idx_translation_jobs_placeholder_map / idx_translation_jobs_service_mention_state（GIN）
- 查询与排序：
  - 规则加载按 priority DESC, id ASC 稳定排序
  - JSONB 过滤 metadata->>'isCompoundWithSuffix' = 'true'
- 批量写入：
  - 插入/更新模式时支持 metadata JSONB，与冲突处理（ON CONFLICT/或批处理函数）

详细DDL/索引/存储过程：请参见 .kiro\steering\database-development-guide.md 对应章节。说明：规则数据消费由引擎直接从数据库读取，AWS服务同步模块不提供任何对外接口。

## 公共API（示例）
- initialize()：加载规则，构建自动机与缓存
- process(text: str, job_id?: str) -> { replaced_text, placeholders, service_mention_state, stats }
- refresh()：订阅事件或定时刷新规则缓存

## 升级与兼容
- v2.0 关键变更：
  - 组件化解耦；引入 Aho-Corasick + 分段正则；强化 JSONB 元数据；部分索引
- 兼容性：
  - 模式枚举与 metadata 字段稳定；新增字段需保持向后兼容（默认值/可空）

## 交叉链接
- 数据库开发指南（DB契约、DDL/索引/查询）：.augment/rules/imported/database-development-guide.md
- 服务同步模块（模式生成与入库）：.augment/rules/imported/aws-service-sync-guide.md

