// ==UserScript==
// @name         Mass Email Feedback Collector
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  Adds a feedback button to the AWS Issues page to collect user satisfaction and comments.
// <AUTHOR>
// @match        https://issues.cn-northwest-1.amazonaws.cn/issues/*
// @icon         data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==
// @grant        GM_xmlhttpRequest
// @connect      qek0wlrzwb.execute-api.cn-north-1.amazonaws.com.cn
// @connect      lianhe-mass-email-feedback-test.s3.cn-north-1.amazonaws.com.cn
// ==/UserScript==

(function() {
    'use strict';

    // --- 配置 ---
    const CONFIG = {
        // 这是您部署 API Gateway 后得到的 URL
        API_ENDPOINT_GENERATE_URL: 'https://qek0wlrzwb.execute-api.cn-north-1.amazonaws.com.cn/generate-upload-url'
    };

    // --- UI 创建 ---

    function createMainFeedbackButton() {
        if (document.getElementById('main-feedback-btn')) return;

        const button = document.createElement('button');
        button.id = 'main-feedback-btn';
        button.textContent = 'Feedback';
        button.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #ff9900;
            color: #232f3e;
            border: none;
            border-radius: 5px;
            padding: 10px 15px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            z-index: 9999;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        `;
        button.onclick = showFeedbackModal;
        document.body.appendChild(button);
    }

    function showFeedbackModal() {
        if (document.getElementById('feedback-modal-overlay')) return;

        const overlay = document.createElement('div');
        overlay.id = 'feedback-modal-overlay';
        overlay.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0, 0, 0, 0.5); z-index: 10000;
            display: flex; align-items: center; justify-content: center;
        `;

        const modal = document.createElement('div');
        modal.id = 'feedback-modal-content';
        modal.style.cssText = `
            background: white; padding: 25px; border-radius: 8px;
            width: 400px; box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            font-family: Arial, sans-serif;
        `;

        modal.innerHTML = `
            <h3 style="margin-top: 0; color: #232f3e;">对本次 Mass Email 处理满意吗？</h3>
            <div style="margin-bottom: 20px;">
                <label style="margin-right: 20px; cursor: pointer;">
                    <input type="radio" name="satisfaction" value="satisfied" checked> 😊 满意
                </label>
                <label style="cursor: pointer;">
                    <input type="radio" name="satisfaction" value="unsatisfied"> 😞 不满意
                </label>
            </div>
            <textarea id="feedback-comments-input"
                placeholder="请在此输入您的具体意见或建议..."
                style="width: 100%; height: 100px; padding: 10px; border: 1px solid #ccc; border-radius: 4px; margin-bottom: 20px; box-sizing: border-box;"
            ></textarea>
            <div style="display: flex; justify-content: flex-end; gap: 10px;">
                <button id="feedback-cancel-btn" style="padding: 10px 20px; border: 1px solid #ccc; background: #f0f0f0; border-radius: 4px; cursor: pointer;">取消</button>
                <button id="feedback-submit-btn" style="padding: 10px 20px; border: none; background: #007dbc; color: white; border-radius: 4px; cursor: pointer;">提交</button>
            </div>
            <div id="feedback-status-msg" style="margin-top: 15px; text-align: center; color: #007dbc;"></div>
            <div id="feedback-debug-log" style="margin-top: 10px; background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 8px; font-size: 12px; max-height: 120px; overflow-y: auto; text-align: left; white-space: pre-wrap; word-wrap: break-word; color: #495057; border-radius: 4px;"></div>
        `;

        overlay.appendChild(modal);
        document.body.appendChild(overlay);

        document.getElementById('feedback-submit-btn').onclick = handleFeedbackSubmit;
        document.getElementById('feedback-cancel-btn').onclick = () => overlay.remove();
        overlay.onclick = (e) => { if (e.target === overlay) overlay.remove(); };
    }

    // --- 数据处理与上传 ---

    function logDebugToModal(message) {
        const debugLog = document.getElementById('feedback-debug-log');
        if (debugLog) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight; // Auto-scroll to bottom
        }
        console.log(`[Feedback Script Debug] ${message}`); // Also log to console
    }

    function handleFeedbackSubmit() {
        const submitBtn = document.getElementById('feedback-submit-btn');
        const statusMsg = document.getElementById('feedback-status-msg');

        // Clear previous logs
        const debugLog = document.getElementById('feedback-debug-log');
        if (debugLog) debugLog.innerHTML = '';

        logDebugToModal("提交按钮点击。");

        submitBtn.disabled = true;
        submitBtn.textContent = '正在提交...';
        statusMsg.textContent = '';

        // 1. 获取预签名 URL
        logDebugToModal("步骤 1: 开始获取预签名 URL...");
        GM_xmlhttpRequest({
            method: 'GET',
            url: CONFIG.API_ENDPOINT_GENERATE_URL,
            onload: function(response) {
                logDebugToModal(`收到响应。状态码: ${response.status}`);
                logDebugToModal(`原始响应内容: ${response.responseText}`);

                if (response.status === 200) {
                    try {
                        logDebugToModal("尝试解析 JSON 响应...");
                        let responseData = JSON.parse(response.responseText);
                        logDebugToModal("第一层 JSON 解析成功。");

                        // 检查是否为双重编码的JSON（API Gateway负载格式不匹配的常见情况）
                        if (typeof responseData === 'string') {
                            logDebugToModal("检测到双重编码的 JSON，尝试再次解析...");
                            responseData = JSON.parse(responseData);
                            logDebugToModal("第二层 JSON 解析成功。");
                        }

                        const data = responseData; // 使用最终解析后的数据
                        logDebugToModal("JSON 解析成功。");
                        let uploadURL = null;

                        // 检查顶层是否有 uploadURL
                        if (data.uploadURL) {
                            uploadURL = data.uploadURL;
                            logDebugToModal("在顶层找到 uploadURL。");
                        }
                        // 检查 'body' 属性中是否有字符串化的 JSON
                        else if (data.body && typeof data.body === 'string') {
                             logDebugToModal("未在顶层找到 uploadURL，尝试解析 data.body...");
                            try {
                                const bodyData = JSON.parse(data.body);
                                logDebugToModal("data.body 解析成功。");
                                if (bodyData.uploadURL) {
                                    uploadURL = bodyData.uploadURL;
                                    logDebugToModal("在 data.body 中找到 uploadURL。");
                                } else {
                                     logDebugToModal("在 data.body 中未找到 uploadURL。");
                                }
                            } catch (e) {
                                logDebugToModal(`解析 data.body 失败: ${e.message}`);
                                console.error("Could not parse data.body:", e);
                            }
                        } else {
                             logDebugToModal("响应中既没有顶层 uploadURL，也没有 data.body 字符串。");
                        }

                        if (uploadURL) {
                            logDebugToModal(`获取 URL 成功: ${uploadURL.substring(0, 50)}...`);
                            uploadFileToS3(uploadURL);
                        } else {
                            logDebugToModal("错误: 最终未能从响应中找到 uploadURL。");
                            throw new Error("uploadURL not found in response");
                        }
                    } catch (e) {
                         logDebugToModal(`处理响应时发生致命错误: ${e.message}`);
                         statusMsg.textContent = '❌ 错误：响应格式不正确。';
                         statusMsg.style.color = 'red';
                         submitBtn.disabled = false;
                         submitBtn.textContent = '提交';
                         console.error("Error parsing presigned URL response:", e);
                         console.error("Received response text:", response.responseText);
                    }
                } else {
                    logDebugToModal(`错误: 获取授权请求失败，状态码 ${response.status}`);
                    statusMsg.textContent = '❌ 错误：无法获取上传授权。';
                    statusMsg.style.color = 'red';
                    submitBtn.disabled = false;
                    submitBtn.textContent = '提交';
                }
            },
            onerror: function(error) {
                logDebugToModal(`网络错误: ${JSON.stringify(error)}`);
                statusMsg.textContent = '❌ 网络错误，请检查连接。';
                statusMsg.style.color = 'red';
                submitBtn.disabled = false;
                submitBtn.textContent = '提交';
                console.error("Error getting presigned URL:", error);
            }
        });
    }

    function uploadFileToS3(uploadURL) {
        const submitBtn = document.getElementById('feedback-submit-btn');
        const statusMsg = document.getElementById('feedback-status-msg');

        logDebugToModal("步骤 2: 开始上传文件到 S3...");

        // 2. 准备要上传的数据
        const satisfaction = document.querySelector('input[name="satisfaction"]:checked').value;
        const comments = document.getElementById('feedback-comments-input').value;

        const feedbackPayload = {
            mass_email_page_url: window.location.href, // 或者更具体的页面 ID
            satisfaction: satisfaction,
            comments: comments,
            submitted_at_iso: new Date().toISOString(),
            user_agent: navigator.userAgent
        };

        const fileContent = JSON.stringify(feedbackPayload, null, 2);

        // 3. 使用 PUT 方法将文件上传到 S3
        GM_xmlhttpRequest({
            method: 'PUT',
            url: uploadURL,
            headers: {
                'Content-Type': 'application/json'
            },
            data: fileContent,
            onload: function(response) {
                logDebugToModal(`S3 上传响应状态码: ${response.status}`);
                if (response.status === 200) {
                    logDebugToModal("文件上传成功！");
                    statusMsg.textContent = '✅ 感谢您的反馈！';
                    statusMsg.style.color = 'green';
                    setTimeout(() => {
                        const overlay = document.getElementById('feedback-modal-overlay');
                        if (overlay) overlay.remove();
                    }, 2000);
                } else {
                    logDebugToModal(`S3 上传失败。响应内容: ${response.responseText}`);
                    statusMsg.textContent = '❌ 上传失败，请稍后重试。';
                    statusMsg.style.color = 'red';
                    submitBtn.disabled = false;
                    submitBtn.textContent = '提交';
                }
            },
            onerror: function(error) {
                logDebugToModal(`S3 上传网络错误: ${JSON.stringify(error)}`);
                statusMsg.textContent = '❌ 上传时发生网络错误。';
                statusMsg.style.color = 'red';
                submitBtn.disabled = false;
                submitBtn.textContent = '提交';
                console.error("Error uploading to S3:", error);
            }
        });
    }

    // --- 初始化 ---
    // 延迟 2 秒执行，确保页面元素加载完毕
    setTimeout(createMainFeedbackButton, 2000);

})(); 