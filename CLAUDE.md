# AWS中国区Mass Email系统项目规则

## 项目概述

AWS中国区Mass Email批量邮件翻译和用户反馈收集系统，采用混合云原生架构：
- **后端**: AWS Lambda + API Gateway + S3 + RDS PostgreSQL
- **前端**: Tampermonkey用户脚本注入AWS Issues页面
- **目标**: 自动化AWS中国区邮件翻译规范和用户反馈收集
- **核心模块**: 
  - 翻译自动化模块（4阶段处理流水线）
  - 反馈收集模块（Lambda + S3 + Tampermonkey）
  - 服务名称同步模块（权威数据源管理）
  - 数据库系统（"逻辑外化"设计哲学）
  - Tampermonkey通用框架（标准化脚本开发）

## 核心设计原则

### 1. 页面内容检测优先（重要更新）

**所有Tampermonkey脚本必须遵循"CN Mass Email"字符串检测规范：**

- **强制要求**: 脚本仅在包含"CN Mass Email"文本的页面上激活
- **检测策略**: 使用多种方法（textContent、innerText、visibleText）
- **动态检测**: 支持异步加载内容，定时重试机制（最多10次）
- **性能优化**: 防抖处理和资源清理，避免性能影响
- **条件激活**: 只有匹配'@match'并且页面包含指定字串后才会加载生效

#### 实现示例
```javascript
function checkPageContainsMassEmail() {
    const pageText = document.body.innerText || document.body.textContent || '';
    return pageText.includes('CN Mass Email');
}

function waitForPageContentAndInitialize() {
    let attempts = 0;
    const maxAttempts = 10;
    const checkInterval = 1000;
    
    const checkContent = () => {
        attempts++;
        
        if (checkPageContainsMassEmail()) {
            console.log('[Script] 检测到 CN Mass Email 内容，初始化功能');
            activateScriptFeatures();
            return;
        }
        
        if (attempts < maxAttempts) {
            setTimeout(checkContent, checkInterval);
        } else {
            console.log('[Script] 未检测到 CN Mass Email 内容，脚本不会激活');
        }
    };
    
    checkContent();
}
```

### 2. 多阶段翻译流水线

#### 阶段0: 文本行类型分析
- **元数据行**: `Service:`, `Region:`, `Failure mode X:`, `TypeCode:`
- **Wording行**: `Wording:`, `First Post Wording:`
- **普通内容行**: 其他可翻译内容

#### 阶段1: JavaScript预处理和占位符生成
- **CLI命令**: `aws ...` → `__CLICMD_X__`
- **ARN字符串**: → `__ARNSTR_X__`
- **JSON代码块**: → `__JSONBLOCK_X__`
- **服务名称**: → `__SRVCNM_X__`
- **时间戳**: → `__TIMESTAMP_X__`
- **URL链接**: → `__URL_X__`

#### 阶段2: LLM翻译
- **严格保留**: 所有`__XXX_YYY__`占位符原封不动
- **翻译内容**: 占位符周围的英文自然语言
- **保持结构**: 维护段落和行结构

#### 阶段3: 后处理和格式化
- **占位符恢复**: 替换为标准化中文内容
- **品牌术语**: 最终中文品牌术语替换
- **格式优化**: 时间格式、标点符号中文化

### 3. 服务名称同步系统

#### 权威数据源管理
- **数据源**: Web抓取 + PDF解析 + PostgreSQL数据库存储
- **目标URL**: `https://www.amazonaws.cn/en/about-aws/regional-product-services/`
- **追踪作用域**: 全局作用域（普通内容）vs 行内作用域（Wording内容）
- **命名规则**: 首次使用全称，后续使用简称
- **复合服务**: `RDS for PostgreSQL` → `Amazon RDS for PostgreSQL`
- **数据验证**: 去重、标准化、完整性检查

#### 同步机制
- **调度**: EventBridge定期触发Lambda函数
- **多源合并**: 网页抓取 + S3 PDF解析
- **错误处理**: 重试机制（最多3次，指数退避）
- **存储结构**: PostgreSQL数据库 + 历史记录
- **监控**: CloudWatch日志和SNS通知

### 4. 安全和合规要求

#### CORS安全配置
- **严格限制**: 仅允许`https://issues.cn-northwest-1.amazonaws.cn`
- **预签名URL**: 5分钟有效期，临时上传权限
- **IAM权限**: 最小权限原则，仅S3 PutObject权限

#### 数据安全
- **传输加密**: HTTPS强制加密
- **存储加密**: S3服务端加密
- **敏感信息**: 环境变量管理，避免硬编码

## 技术架构规范

### 1. 翻译系统架构

#### 占位符管理器
```typescript
interface PlaceholderManager {
  generatePlaceholder(content: string, type: PlaceholderType): string;
  restorePlaceholder(placeholder: string): string;
  validateIntegrity(text: string): boolean;
}
```

#### 服务名称追踪器
```typescript
interface ServiceNameTracker {
  trackMention(serviceName: string, scope: TrackingScope): ServiceMentionState;
  getStandardName(serviceName: string, isFirstMention: boolean): string;
  resetScope(scope: TrackingScope): void;
}
```

#### 时区转换器
```typescript
interface TimeZoneConverter {
  convertToBeijingTime(timeString: string, sourceTimezone: string): string;
  formatChineseTime(datetime: Date, includeTime: boolean): string;
  validateTimeFormat(timeString: string): boolean;
}
```

### 2. 反馈收集系统架构

#### Lambda函数接口
```python
class FeedbackHandler:
    def generate_presigned_url(self) -> Dict[str, str]:
        """生成S3预签名URL用于反馈上传"""
        
    def create_object_key(self) -> str:
        """创建基于时间戳的对象键"""
        
    def handle_cors(self, response: Dict) -> Dict:
        """添加CORS头到响应"""
```

#### Tampermonkey脚本架构
```javascript
class FeedbackCollector {
    constructor(config) {
        this.apiEndpoint = config.API_ENDPOINT_GENERATE_URL;
        this.debugLogger = new DebugLogger();
    }
    
    // 页面内容检测后才注入UI
    injectFeedbackButton() {
        if (!checkPageContainsMassEmail()) return;
        // 创建反馈按钮逻辑
    }
}
```

### 3. 服务名称同步系统架构

#### Web Scraper Lambda
```python
class ServiceNameScraper:
    def scrape_web_services(self) -> List[ServiceInfo]:
        """抓取AWS中国区服务页面"""
        
    def parse_pdf_services(self, s3_bucket: str, pdf_key: str) -> List[ServiceInfo]:
        """解析S3存储的PDF文档"""
        
    def update_database(self, service_mappings: List[ServiceMapping]) -> bool:
        """更新PostgreSQL数据库"""
```

#### 数据库架构（"逻辑外化"设计）

**核心6张表**：
```sql
-- 服务名称表
CREATE TABLE service_names (
    id BIGSERIAL PRIMARY KEY,
    base_name VARCHAR(255) NOT NULL UNIQUE,
    full_name_en VARCHAR(255) NOT NULL,
    short_name_en VARCHAR(100) NOT NULL,
    full_name_cn VARCHAR(255),
    short_name_cn VARCHAR(100),
    service_code VARCHAR(50),
    source rule_source NOT NULL DEFAULT 'manual',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 品牌术语映射表
CREATE TABLE brand_term_mappings (
    id BIGSERIAL PRIMARY KEY,
    term_en VARCHAR(255) NOT NULL UNIQUE,
    term_cn VARCHAR(255) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    notes TEXT
);

-- URL映射规则表
CREATE TABLE url_mappings (
    id BIGSERIAL PRIMARY KEY,
    source_pattern TEXT NOT NULL,
    target_pattern TEXT NOT NULL,
    is_regex BOOLEAN NOT NULL DEFAULT FALSE,
    priority INT NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT TRUE
);

-- 正则表达式模式表
CREATE TABLE regex_patterns (
    id BIGSERIAL PRIMARY KEY,
    pattern_name VARCHAR(255) NOT NULL UNIQUE,
    pattern_type regex_pattern_type NOT NULL,
    regex_string TEXT NOT NULL,
    related_service_id BIGINT,
    priority INT NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    FOREIGN KEY(related_service_id) REFERENCES service_names(id)
);

-- 翻译任务表（完整生命周期追踪）
CREATE TABLE translation_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    status translation_job_status NOT NULL DEFAULT 'pending',
    original_text TEXT NOT NULL,
    stage1_standardized_en TEXT,
    stage2_llm_input TEXT,
    stage2_llm_output TEXT,
    stage3_final_cn TEXT,
    placeholder_map JSONB,
    service_mention_state JSONB,
    submitted_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 用户反馈表
CREATE TABLE feedback_submissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    translation_job_id UUID,
    page_url TEXT NOT NULL,
    satisfaction feedback_satisfaction NOT NULL,
    comments TEXT,
    s3_object_key VARCHAR(1024) NOT NULL UNIQUE,
    submitted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    FOREIGN KEY(translation_job_id) REFERENCES translation_jobs(id)
);
```

## AWS中国区翻译规范

### 1. 英文术语标准化（阶段1）

#### 基础替换
- `AWS` → `Amazon`（当不是服务名称部分时）
- `AWS Support` → `Amazon Web Services Support`
- `AWS Account` → `Amazon Web Services Account`
- `AWS Management Console` → `Amazon Web Services Console`

#### 服务名称处理
- **首次提及**: `Amazon Elastic Compute Cloud (EC2)`
- **后续提及**: `Amazon EC2`
- **复合服务**: `RDS for PostgreSQL` → `Amazon RDS for PostgreSQL`

#### 时区转换（精确计算）
- **PDT (UTC-7)**: 原时间 + 15小时 → UTC+8
- **PST (UTC-8)**: 原时间 + 16小时 → UTC+8
- **EDT (UTC-4)**: 原时间 + 12小时 → UTC+8
- **EST (UTC-5)**: 原时间 + 13小时 → UTC+8
- **UTC/GMT**: 原时间 + 8小时 → UTC+8

#### CLI命令处理
- **宁夏内容**: `--region cn-northwest-1`
- **北京内容**: `--region cn-north-1`
- **默认**: `--region cn-north-1`

#### URL本地化
- `docs.aws.amazon.com` → `docs.amazonaws.cn`
- `console.aws.amazon.com` → `console.amazonaws.com.cn`
- `aws.amazon.com/support` → `console.amazonaws.cn/support/`

### 2. 中文翻译标准（阶段2）

#### 品牌术语翻译
- `Amazon` → `亚马逊云科技`（当独立出现时）
- `Amazon Web Services Support` → `亚马逊云科技中国支持团队`
- `Amazon Web Services Console` → `亚马逊云科技控制台`
- `Amazon Web Services Account` → `亚马逊云科技账户`
- `Amazon Web Services` → `亚马逊云科技`

#### 区域表达规范
- **避免使用**: "all the AWS regions", "all commercial regions"
- **推荐使用**: `亚马逊云科技中国区域`
- **具体区域**: `北京区域 (BJS)` 或 `宁夏区域 (ZHY)`
- **运营商区分**: 光环新网 vs 西云数据

#### 技术术语翻译
- `Affected resources` → `受影响的资源`
- `Fast Launch` → `快速启动`
- `Service Quotas` → `服务配额`

### 3. 最终格式化（阶段3）

#### 时间格式输出
- **完整日期时间**: `YYYY年M月D日 上午/下午 h:mm 北京时间`
- **仅日期**: `YYYY年M月D日`（无时间组件时）
- **时间指示器**: 使用中文`上午/下午`

#### 标点符号规范
- **中文标点**: `，。？！：；`
- **括号处理**: `[...]` → `（...）`（避免使用`【】`）
- **技术内容**: 保持占位符恢复后的原始格式

## 开发工作流程

### 1. 核心工作流：思考、检索、整合

#### 理解与拆解 (Analyze & Decompose)
- **首要任务**: 使用 `server-sequential-thinking` MCP 对复杂请求进行任务拆解和分析
- **明确意图**: 识别用户的核心意图和成功标准
- **制定计划**: 根据拆解结果，制定清晰的分步骤执行计划

#### 信息收集 (Information Gathering)
- **本地优先 (Local First)**: **必须**首先检索并分析项目文件夹内的所有相关文件
- **网络增强 (Web Augmentation)**: 使用 `tavily-mcp` 进行网络搜索，获取最新信息
- **交叉验证**: 对信息来源进行交叉验证确保准确性

#### 深度研究模式 (Deep Research Protocol)
- **触发条件**: 复杂请求或明确使用"深度研究"关键词时启动
- **迭代研究循环**:
  1. **分析 (Analyze)**: 使用 `server-sequential-thinking` 设定研究目标
  2. **搜索 (Search)**: 使用 `tavily-mcp` 根据问题搜集资料
  3. **综合 (Synthesize)**: 整合并理解搜集到的信息
- **重复循环**: 至少两轮以确保研究深度和广度

#### 交互与反馈 (Interaction & Feedback)
- **关键节点确认**: 在任务执行关键节点调用 `interactive-feedback-mcp`
- **需求澄清**: 需要澄清需求时主动向用户提问
- **完成确认**: 即将完成整个请求时请求用户确认

### 2. MCP工具使用策略

#### 基本原则
- **任务驱动**: 根据当前子任务性质选择最精准的 MCP 工具
- **组合优先**: 优先考虑使用多个 MCP 工具组合

#### MCP选择指南
| 任务类型 | 首选MCP工具 | 适用场景 |
|:---------|:-------------|:---------|
| **逻辑与规划** | `server-sequential-thinking` | 任务拆解、架构决策、复杂问题分析 |
| **文件系统操作** | `filesystem` | 查找文件、读取代码、分析项目结构 |
| **网络信息检索** | `tavily-mcp` | 获取最新API、技术文章、外部知识 |
| **Markdown处理** | `markitdown` | 解析、生成或修改Markdown文档 |
| **网页自动化** | `playwright-mcp` / `Puppeteer` | UI测试、抓取动态内容、模拟用户操作 |
| **上下文理解** | `context7-mcp` | 深度语义理解和复杂文档分析 |
| **交互反馈** | `interactive-feedback-mcp` | 用户确认、需求澄清、进度反馈 |

#### 协同策略模式
1. **分析-执行模式**: 先用 `server-sequential-thinking` 分析，再用专用工具执行
2. **信息汇总模式**: 多渠道收集信息，然后整合分析和总结

### 3. 任务执行流程 (Workflow)

1. **问题分析**: 审查用户请求、代码和上下文，识别根本原因
2. **方案设计**: 设计**最小化、低风险**的修改方案
3. **代码实现**: 实施修改，添加清晰注释，遵循项目代码风格
4. **测试验证**: 编写或更新单元测试，进行功能验证
5. **文档更新**: 更新相关文档，清晰记录变更

#### 核心编码准则 (Code Principles)
- **安全 (Security)**: 遵循最小权限原则，避免引入安全漏洞
- **稳定 (Stability)**: 保持现有功能稳定，确保向后兼容
- **质量 (Quality)**: 编写高性能、可读性强、易于维护的代码
- **验证 (Validation)**: 所有代码变更都必须经过充分测试和文档记录

## 文件结构和开发环境

### 项目目录结构

```
├── docs/
│   ├── dev/                                  # 开发阶段文档
│   ├── backups/                              # 文档备份
│   ├── kirorules/                            # 项目规则和指导
│   │   ├── steering/                         # 指导文档（6个文件）
│   │   │   ├── database-development-guide.md
│   │   │   ├── development-workflow-guide.md
│   │   │   ├── feedback-system-guide.md
│   │   │   ├── mass-email-system-context.md
│   │   │   ├── tampermonkey-development-guide.md
│   │   │   └── translation-rules.md
│   │   └── specs/                            # 技术规范（4个模块）
│   │       ├── aws-service-sync/             # 服务同步系统规范
│   │       ├── database-design/              # 数据库设计规范
│   │       ├── mass-email-system/            # Mass Email系统规范
│   │       └── tampermonkey-framework/       # Tampermonkey框架规范
│   └── lambda_feedback_test/                 # Lambda测试文档
├── lambda/
│   ├── dev/                                  # 开发Lambda函数
│   ├── feedback_test/                        # 反馈系统Lambda
│   └── service_sync/                         # 服务同步Lambda
├── tampermonkey/
│   ├── dev/                                  # 开发脚本
│   ├── backups/                              # 脚本备份
│   └── feedback_test/                        # 反馈收集脚本
├── database/                                 # 数据库脚本和schema
├── mass_email_outline/                       # 业务需求和翻译指南
├── poc/                                      # 概念验证和临时开发
```

### 开发环境使用指导

#### 开发环境使用指导

**Development目录（`dev/`）**:
- **`docs/dev/`**: 存放开发阶段的文档、设计草稿、技术笔记
- **`lambda/dev/`**: 存放开发中的 Lambda 函数代码，用于新功能测试
- **`tampermonkey/dev/`**: 存放开发中的用户脚本，用于功能迭代和测试
- **开发流程**: 在 dev 目录中进行开发和测试，稳定后移至相应的测试目录

**版本控制策略**:
- **脚本备份**: Tampermonkey脚本自动备份到`backups/`
- **文档版本**: 重要文档变更前创建备份
- **规范管理**: kirorules目录包含完整的项目规范体系


## 质量保证和测试

### 质量保证优先级

#### 翻译系统QA优先级
1. **占位符完整性**: 绝不修改`__XXX_YYY__`占位符
2. **时间准确性**: 验证时区转换计算
3. **链接可访问性**: 确保所有链接在中国区可用
4. **品牌一致性**: 正确应用AWS中国品牌术语
5. **服务名称**: 验证首次/后续使用规则

#### 反馈系统QA优先级
1. **页面内容检测**: 严格执行"CN Mass Email"字符串检测
2. **CORS安全**: 验证CORS、IAM权限和输入清理
3. **API集成**: 确保Lambda和S3预签名URL正常工作
4. **用户体验**: 验证UI注入和调试信息显示
5. **错误处理**: 测试各种异常场景的处理

#### 服务同步系统QA优先级
1. **数据源验证**: 始终验证来自网页抓取和PDF解析的数据源
2. **错误处理**: 实现网络故障和解析错误的健壮错误处理
3. **数据库事务**: 使用数据库事务确保原子更新
4. **数据完整性**: 通过规范化和去重维护数据完整性
5. **监控日志**: 记录所有同步活动用于监控和调试


### 环境配置

#### Lambda环境变量
```bash
S3_BUCKET_NAME=your-feedback-bucket-name
ALLOWED_ORIGIN=https://issues.cn-northwest-1.amazonaws.cn
```

#### Tampermonkey配置
```javascript
// @match        https://issues.cn-northwest-1.amazonaws.cn/issues/*
// @grant        GM_xmlhttpRequest
// @connect      qek0wlrzwb.execute-api.cn-north-1.amazonaws.com.cn
// @connect      lianhe-mass-email-feedback-test.s3.cn-north-1.amazonaws.com.cn
```

## 安全和权限控制

### CORS安全配置
```json
{
    "allowedOrigins": ["https://issues.cn-northwest-1.amazonaws.cn"],
    "allowedMethods": ["POST", "OPTIONS"],
    "allowedHeaders": ["Content-Type", "Authorization"]
}
```


## 错误处理和恢复

### 翻译流水线错误处理

#### 阶段级错误恢复
- **预LLM验证**: 确保技术内容正确转换为占位符
- **后LLM验证**: 验证占位符完整性
- **恢复验证**: 确保占位符正确恢复

### 反馈系统错误处理

#### 前端错误处理
```javascript
class ErrorHandler {
    static handleNetworkError(error) {
        this.logDebugToModal(`网络错误: ${JSON.stringify(error)}`);
        this.showUserMessage('❌ 网络错误，请检查连接。', 'error');
    }
    
    static handleAPIError(response) {
        this.logDebugToModal(`API错误: 状态码 ${response.status}`);
        this.showUserMessage('❌ 错误：无法获取上传授权。', 'error');
    }
}
```

#### Lambda错误处理
```python
@staticmethod
def handle_s3_error(error: Exception) -> Dict:
    return {
        'statusCode': 500,
        'headers': {'Access-Control-Allow-Origin': ALLOWED_ORIGIN},
        'body': json.dumps({'error': 'S3 operation failed'})
    }
```

### 服务同步错误处理

#### 重试机制
```python
@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10),
    retry=retry_if_exception_type((ConnectionError, TimeoutError))
)
def fetch_data_with_retry(url: str):
    """带重试机制的数据获取"""
    pass
```

## 性能优化

### 翻译流水线优化
- **缓存策略**: 缓存常用服务名称和翻译结果
- **并行处理**: 并行处理不同类型的文本行
- **内存管理**: 优化大文本处理的内存使用

### 反馈系统优化
- **Lambda冷启动**: 使用连接池和层管理依赖
- **前端性能**: 最小化UI注入对页面性能的影响

### Tampermonkey脚本优化
- **文本搜索**: 使用indexOf而非正则表达式（除非必要）
- **DOM操作**: 最小化DOM查询，批量处理更新
- **资源管理**: 及时清理不再需要的资源
- **防抖节流**: 实施防抖和节流机制
- **内容检测**: 使用高效的检测算法和MutationObserver优化
- **性能限制**: 实现资源限制和性能阈值控制

## 监控和调试

### CloudWatch日志管理
- **详细日志**: 记录所有关键操作和错误
- **结构化日志**: 使用JSON格式便于查询
- **日志聚合**: 实现跨服务的日志关联

### 前端调试支持
- **实时调试**: 在模态框中显示调试信息
- **网络监控**: 记录API调用和响应
- **错误收集**: 收集和分析前端错误
- **性能监控**: 收集检测时间、内存使用和DOM操作指标
- **内容检测状态**: 显示检测尝试次数、成功率和失败原因


---

## 总结

## 特殊开发指导

### Mass Email系统特定指导

#### 翻译功能开发指导
- **多阶段流程**: 始终考虑 4 阶段处理流水线 (Stage 0-3)
- **占位符保护**: 实现占位符保护机制，防止技术内容被错误翻译
- **服务名追踪**: 确保服务名称首次/后续追踪的准确性
- **时区转换**: 验证时区转换计算的精确性
- **测试覆盖**: 测试各种输入格式（元数据、wording、普通内容）

#### 反馈系统开发指导
- **CORS 安全**: 维护 CORS 安全（仅允许 issues.cn-northwest-1.amazonaws.cn）
- **文件路径**: 使用结构化 S3 文件路径（时间戳和 UUID）
- **调试信息**: 在 UI 中提供详细的调试信息
- **错误处理**: 优雅处理成功和错误场景
- **跨域测试**: 彻底测试跨域请求

#### 服务名称同步系统开发指导
- **数据源验证**: 始终验证来自网页抓取和PDF解析的数据源
- **错误处理**: 实现网络故障和解析错误的健壮错误处理
- **数据库事务**: 使用数据库事务确保原子更新
- **数据完整性**: 通过规范化和去重维护数据完整性
- **监控日志**: 记录所有同步活动用于监控和调试

#### Tampermonkey框架开发指导
- **通用框架**: 提供标准化的页面内容检测、脚本生命周期管理
- **模块化设计**: 支持多种脚本类型（反馈收集、翻译辅助、UI增强等）
- **性能优化**: 防抖、节流、资源管理和多脚本协调
- **错误处理**: 全面的错误处理机制和调试工具

---

## 总结

本项目规则文档整合了来自 `@docs\kirorules\` 目录的**完整规范体系**，包括：

- **6个steering指导文档**: 核心开发指导和工作流程
- **12个specs技术规范**: 详细的系统设计和实施计划
- **5大核心模块**: 翻译自动化、反馈收集、服务同步、数据库、Tampermonkey框架
- **1000+个实施任务**: 完整的开发路线图

**关键原则**:
1. **页面内容检测优先**: 所有Tampermonkey脚本必须严格遵循"CN Mass Email"字符串检测规范
2. **"逻辑外化"数据库设计**: 将硬编码业务规则从代码分离到数据库
3. **4阶段翻译流水线**: Stage 0-3的严格处理流程和占位符保护
4. **混合云原生架构**: Lambda + S3 + RDS + Tampermonkey的完整技术栈
5. **企业级质量保证**: 全面的安全配置、错误处理和监控体系

请在开发过程中严格遵循这些规则和规范，确保系统的稳定性、安全性和一致性。这是一个**企业级**的完整系统架构，需要按照规范进行严谨的开发和实施。