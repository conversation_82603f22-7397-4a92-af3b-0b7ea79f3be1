# 数据库设计实施任务列表

## 文档范围（Scope）与定位

本任务列表面向数据库工程/后端工程，聚焦：
- 数据模型与约束（表/枚举/外键/检查约束）的实施任务
- 性能与索引（部分索引、JSONB GIN）的配置任务
- 查询模式与存储过程/函数的实现任务
- 面向组件的数据库契约（PatternLoader/SuffixAssembler/MentionTracker/BoundaryGuard 所需的查询与索引）的支持任务

不包含（Out-of-scope）：
- 应用层/服务层的算法实现与类/方法细节（例如 Aho-Corasick 自动机构建、分段正则的具体代码、占位符替换流程）
- 相关内容已迁移至：.kiro/steering/pattern-matching-engine-guide.md

## 任务概述

本任务列表基于AWS中国区Mass Email批量邮件翻译和用户反馈收集系统的数据库设计需求，采用"逻辑外化"设计哲学，实现PostgreSQL 13+数据库的完整架构实施。任务按照依赖关系和优先级进行组织，确保系统的4大核心功能模块和v2.0高性能优化特性能够有序实施。

**v2.0版本核心特性**：
- 8种核心正则表达式模式类型（优先级90-130）
- ENUM类型安全和metadata JSONB字段支持
- 部分索引策略（60-80%索引大小减少，10-50x查询速度提升）
- 组件化架构支持（PatternLoader、SuffixAssembler、MentionTracker、BoundaryGuard）
- 表分区支持和边界保护机制
- 按外键依赖顺序的安全数据清理策略

## 实施任务

### 1. 数据库基础架构搭建

- [x] 1.1 PostgreSQL环境配置和版本验证
  - 验证PostgreSQL 13.0+版本要求
  - 配置UTF-8字符编码支持中英文混合内容
  - 启用uuid-ossp扩展用于UUID主键生成
  - 配置数据库连接池（推荐minconn=5, maxconn=20）
  - _需求: 需求1.1, 需求7.1_

- [x] 1.2 自定义枚举类型创建（v2.0 ENUM类型安全）
  - 实现translation_job_status枚举（pending, processing_stage1-3, completed, failed）
  - 实现rule_source枚举（web_scrape, pdf_parse, manual）
  - 实现feedback_satisfaction枚举（satisfied, unsatisfied, neutral）
  - 实现regex_pattern_type枚举（SERVICE_NAME, TIMEZONE, CLI_COMMAND, URL, GENERAL, CONTEXT_PROTECTED）
  - 实现validation_status_type枚举（pending, valid, invalid）用于正则表达式验证状态管理
  - 配置ENUM类型的在线扩展支持（ALTER TYPE语句）
  - _需求: 需求1.2, 需求2.2, 需求4.4, 需求3.6, 需求3.7, 需求14.1_

- [x] 1.3 触发器函数实现
  - 创建trigger_set_timestamp()函数用于自动更新updated_at字段
  - 配置触发器自动维护审计时间戳
  - _需求: 需求6.7_

### 2. 核心数据表结构实现

- [x] 2.1 service_names表（v2架构）实现
  - 创建service_names表支持多数据源同步
  - 实现authoritative_full_name作为业务主键的唯一约束
  - 配置base_name字段用于翻译逻辑分组
  - 实现internal_name字段存储PDF数据源信息
  - 配置full_name_en和short_name_en字段支持首次/后续提及逻辑
  - 实现service_code、source、last_synced_at等关键字段
  - _需求: 需求2.1, 需求2.3, 需求2.4, 需求2.5_

- [x] 2.2 regex_patterns表（v2.0优化版8种模式类型系统）实现
  - 创建regex_patterns表支持8种核心模式类型（优先级90-130）
  - 实现pattern_name唯一约束和regex_pattern_type ENUM分类
  - 配置priority字段支持90-130优先级分层匹配机制，数字越大优先级越高
  - 实现metadata JSONB字段存储结构化元数据，包含isCompoundWithSuffix、suffixGroup、patternCategory、hasBoundaryProtection等
  - 保留notes字段用于向后兼容，但主要元数据存储在metadata JSONB字段中
  - 配置validation_status字段使用validation_status_type枚举（pending, valid, invalid）和长度约束（<10000字符）
  - 建立related_service_id外键关联到service_names表（ON DELETE SET NULL）
  - 实现service_code字段支持按服务分组管理
  - _需求: 需求3.1, 需求3.2, 需求3.3, 需求3.4, 需求3.5, 需求3.6_

- [x] 2.3 translation_jobs表（4阶段流水线追踪）实现
  - 创建translation_jobs表使用UUID主键
  - 实现4阶段字段：original_text, stage1_standardized_en, stage2_llm_input, stage2_llm_output, stage3_final_cn
  - 配置JSONB字段：placeholder_map存储占位符映射关系
  - 配置JSONB字段：service_mention_state存储服务提及状态追踪
  - 实现error_message和error_stage字段支持精确故障定位
  - 配置processing_time_ms、submitted_by、submitted_at、completed_at审计字段
  - _需求: 需求1.1, 需求1.2, 需求1.3, 需求1.4, 需求1.5, 需求6.1, 需求6.2_

- [x] 2.4 feedback_submissions表（用户反馈与S3集成）实现
  - 创建feedback_submissions表使用UUID主键
  - 实现translation_job_id外键关联到translation_jobs表
  - 配置page_url、satisfaction、comments、user_agent字段
  - 实现s3_object_key唯一约束确保数据完整性
  - _需求: 需求4.1, 需求4.2, 需求4.3, 需求4.5_

- [x] 2.5 brand_term_mappings表（品牌术语管理）实现
  - 创建brand_term_mappings表存储term_en到term_cn的强制替换规则
  - 实现term_en唯一约束
  - 配置is_active启用状态管理和notes字段
  - _需求: 需求5.1, 需求5.4, 需求5.5_

- [x] 2.6 url_mappings表（URL本地化规则）实现
  - 创建url_mappings表支持source_pattern到target_pattern映射
  - 实现is_regex字段区分普通字符串和正则表达式模式
  - 配置priority优先级控制和is_active启用状态管理
  - 实现source_pattern和is_regex的复合唯一约束
  - _需求: 需求5.2, 需求5.3, 需求5.4, 需求5.5_

### 3. 外键约束和数据完整性实现

- [x] 3.1 外键约束定义和实现
  - 实现regex_patterns.related_service_id外键约束到service_names.id（ON DELETE SET NULL）
  - 实现feedback_submissions.translation_job_id外键约束到translation_jobs.id（ON DELETE SET NULL）
  - 验证外键约束的引用完整性
  - _需求: 需求3.5_

- [x] 3.2 外键约束验证和修复系统
  - 实现外键约束状态检查SQL脚本
  - 创建自动修复缺失外键约束的功能
  - 实现外键约束功能测试（有效/无效/NULL值测试）
  - 配置外键约束诊断和报告系统
  - _基于: database/foreign_key_verification_and_repair.sql_

- [x] 3.3 数据完整性约束验证
  - 验证所有唯一约束（authoritative_full_name, pattern_name, term_en, s3_object_key）
  - 测试枚举约束的有效性
  - 验证NOT NULL约束的正确性
  - 实现约束违反的错误处理测试
  - _需求: 需求9.1, 需求9.4_

### 4. 索引策略和性能优化实现

- [x] 4.1 核心业务索引创建
  - 创建service_names表索引：base_name, service_code, is_active, source, last_synced_at
  - 创建regex_patterns表核心索引：priority DESC + id ASC复合索引
  - 创建regex_patterns表业务索引：pattern_type + is_active, service_code, validation_status
  - 创建brand_term_mappings和url_mappings表的is_active索引
  - 创建feedback_submissions表索引：satisfaction, submitted_at, translation_job_id
  - _需求: 需求7.1, 需求7.3_

- [x] 4.2 部分索引策略实现（v2.0核心性能优化）
  - 创建idx_service_names_active_code部分索引（WHERE is_active = TRUE）
  - 创建idx_regex_patterns_active_valid部分索引（WHERE is_active = TRUE AND validation_status = 'valid'）
  - 验证部分索引的性能提升（60-80%索引大小减少，10-50x查询速度提升）
  - 配置PatternLoader组件的高性能模式加载查询优化
  - _需求: 需求11.1, 需求11.2, 需求12.1_

- [x] 4.3 JSONB字段GIN索引优化
  - 创建translation_jobs.placeholder_map的GIN索引
  - 创建translation_jobs.service_mention_state的GIN索引
  - 创建regex_patterns.metadata的GIN索引（支持SuffixAssembler组件）
  - 验证JSONB查询性能（如placeholder_map ? '__SRVCNM_0__'、metadata->>'isCompoundWithSuffix' = 'true'）
  - _需求: 需求7.2, 需求6.7, 需求11.3, 需求12.4_

- [x] 4.4 表分区支持实现（v2.0可扩展性优化）
  - 实现translation_jobs表按时间分区（PARTITION BY RANGE submitted_at）
  - 创建按月分区表（translation_jobs_2025_01、translation_jobs_2025_02等）
  - 创建默认分区处理未来数据（translation_jobs_default）
  - 验证分区表的索引自动应用到所有分区
  - 配置分区表的自动数据管理策略
  - _需求: 需求11.5, 需求2.3_

- [ ] 4.5 查询性能优化和测试
  - 实现优先级查询优化（ORDER BY priority DESC, id ASC）
  - 测试复合索引效率（pattern_type + is_active）
  - 验证JSONB查询性能和GIN索引效果
  - 使用EXPLAIN ANALYZE分析查询执行计划
  - 验证部分索引的查询性能提升
  - _需求: 需求7.5, 需求9.2, 需求11.1_

### 5. 8种正则表达式模式类型系统实现

- [x] 5.1 模式类型架构设计实现
  - 实现优先级90-130的8种核心模式类型分层架构
  - 配置全称复合后缀模式（优先级120）
  - 配置全称标准模式（优先级115）
  - 配置简称复合后缀模式（优先级110）
  - 配置简称标准模式（优先级105）
  - 配置缩写复合后缀模式（优先级100）
  - 配置缩写标准模式（优先级95）
  - 配置特殊变体模式（优先级125）
  - 配置上下文保护模式（优先级90）
  - _需求: 需求3.1, 需求3.3_

- [x] 5.2 复合后缀处理机制实现
  - 实现metadata JSONB字段中isCompoundWithSuffix标识的存储和查询
  - 配置suffixGroup捕获组编号在metadata字段中的结构化存储
  - 实现patternCategory分类标识（full_complex_suffix、short_complex_suffix等）
  - 支持instance/instances, volume/volumes, policy/policies, for MySQL/PostgreSQL等后缀类型
  - 实现复合后缀模式的智能识别和保留机制
  - 配置GIN索引支持metadata JSONB字段的高效查询
  - _需求: 需求3.2, 需求10.1, 需求11.3_

- [x] 5.3 边界保护机制实现（v2.0统一边界保护原则）
  - 实现统一边界保护原则（BoundaryGuard + CONTEXT_PROTECTED）
  - 配置ARN保护模式：INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, priority) VALUES ('CONTEXT_ARN_GENERIC', 'CONTEXT_PROTECTED', 'arn:(?:aws|aws-cn):\S+', 90)
  - 配置URL保护模式：INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, priority) VALUES ('CONTEXT_URL_GENERIC', 'CONTEXT_PROTECTED', 'https?://\S+', 90)
  - 实现代码标识符保护：业务模式本体仅保留固定宽度的轻量边界保护（如 (?<![:_-]) 与 (?![:_-])）
  - 确保模式本体禁止使用"可变宽度负向后行"，保证Python正则表达式兼容性
  - 配置CONTEXT_PROTECTED类型模式支持BoundaryGuard组件
  - 实现特殊服务处理（Aurora PostgreSQL/MySQL变体、Health Dashboard vs通用Health服务、RDS引擎特定模式）
  - 配置边界保护模式的metadata标识（hasBoundaryProtection: true）
  - _需求: 需求13.1, 需求13.2, 需求13.3, 需求13.4, 需求13.5_

### 5.4 组件化架构数据库层支持实现（v2.0模块化设计）

> **注意**：本章节聚焦数据库层面的组件支持任务。应用层算法实现细节（类/方法/流程）已迁移至匹配引擎文档：.kiro/steering/pattern-matching-engine-guide.md。数据库任务专注于数据模型、查询、索引与过程的实现。

- [x] 5.4.1 PatternLoader组件数据库支持
  - 配置高性能模式加载查询：SELECT pattern_name, regex_string, metadata, priority, service_code FROM regex_patterns WHERE is_active = TRUE AND validation_status = 'valid' ORDER BY priority DESC, id ASC
  - 实现部分索引idx_regex_patterns_active_valid的查询优化支持
  - 配置稳定排序机制（ORDER BY priority DESC, id ASC）
  - 验证10-50x性能提升的查询优化效果
  - _需求: 需求12.1, 需求11.1_

- [x] 5.4.2 SuffixAssembler组件数据库支持
  - 配置复合后缀模式查询：SELECT pattern_name, regex_string, metadata->>'suffixGroup' AS suffix_group FROM regex_patterns WHERE metadata->>'isCompoundWithSuffix' = 'true' AND is_active = TRUE
  - 实现metadata JSONB字段的结构化元数据存储（isCompoundWithSuffix、suffixGroup、patternCategory、hasBoundaryProtection）
  - 配置GIN索引idx_regex_patterns_metadata支持metadata字段的高效查询
  - 实现复合后缀模式的数据库层识别和处理
  - _需求: 需求12.4, 需求11.3_

- [x] 5.4.3 MentionTracker组件数据库支持
  - 配置service_mention_state JSONB字段的原子性更新操作
  - 实现服务首次/后续提及状态的数据库存储和查询
  - 配置GIN索引支持service_mention_state字段的高效查询
  - 实现提及历史记录的数据库层追踪机制
  - _需求: 需求12.5, 需求11.4_

- [x] 5.4.4 BoundaryGuard组件数据库支持
  - 配置CONTEXT_PROTECTED类型模式查询：SELECT regex_string, metadata FROM regex_patterns WHERE pattern_type = 'CONTEXT_PROTECTED' AND is_active = TRUE
  - 实现边界保护模式的数据库存储和管理
  - 配置边界保护验证的数据库查询支持
  - 实现误匹配剔除的数据库层支持
  - _需求: 需求12.3, 需求13.1-13.5_

- [x] 5.4.5 ServiceMatcher组件数据库支持
  - 配置按service_code字段的服务分组模式查询
  - 实现分段正则匹配的数据库查询优化
  - 配置候选区域正则表达式应用的数据库支持
  - 实现优先级裁剪算法的数据库层支持
  - _需求: 需求12.2_

### 6. 数据初始化和种子数据实现

- [x] 6.1 安全的数据清理机制实现（v2.0优化版）
  - 实现按外键依赖顺序的DELETE FROM语句替代TRUNCATE CASCADE
  - 配置序列重置（ALTER SEQUENCE ... RESTART WITH 1）确保ID从1开始
  - 确保外键约束在数据清理过程中保持活跃，避免TRUNCATE CASCADE的约束删除问题
  - 实现可重复执行的数据清理脚本，支持开发和测试环境的数据重置
  - 验证数据清理策略的优势：保留外键约束、安全性、一致性、可测试性、维护友好
  - _基于设计文档中的数据库架构脚本修复方案和DELETE FROM + ALTER SEQUENCE RESTART策略_

- [x] 6.2 品牌术语映射初始数据
  - 插入标准品牌术语替换规则（Amazon Web Services Support等）
  - 配置品牌标准化规则（AWS SDK → Amazon SDK等）
  - 实现常见拼写错误纠正规则
  - _基于: database/mass_email_database_schema_v2.sql中的初始化数据_

- [x] 6.3 URL映射规则初始数据
  - 插入中国区URL本地化规则
  - 配置域名本地化（aws.amazon.com → amazonaws.cn）
  - 实现控制台和文档链接的区域化
  - 配置优先级和正则表达式模式
  - _基于: database/mass_email_database_schema_v2.sql中的URL映射数据_

- [x] 6.4 AWS服务权威名称初始数据
  - 插入v2架构的服务名称数据
  - 配置authoritative_full_name、base_name、service_code映射
  - 实现full_name_en和short_name_en的首次/后续提及逻辑
  - _基于: database/mass_email_database_schema_v2.sql中的服务数据_

### 7. 监控和审计系统实现

- [ ] 7.1 翻译任务健康监控实现
  - 实现任务失败率监控（failed_jobs / total_jobs * 100）
  - 配置平均处理时间统计（AVG(processing_time_ms)）
  - 实现各阶段失败分布分析（GROUP BY error_stage）
  - 配置积压任务数量监控（COUNT(*) WHERE status = 'pending'）
  - _需求: 需求6.1, 需求6.2, 需求8.1_

- [ ] 7.2 数据同步健康监控实现
  - 实现数据新鲜度监控（NOW() - MAX(last_synced_at)）
  - 配置多数据源覆盖度分析
  - 实现同步成功率统计
  - _需求: 需求6.4, 需求8.5_

- [ ] 7.3 正则表达式质量监控实现
  - 实现无效模式比例监控（invalid_patterns / total_patterns * 100）
  - 配置验证积压告警（COUNT(*) WHERE validation_status = 'pending'）
  - 实现优先级冲突检测
  - _需求: 需求6.3, 需求8.4_

- [ ] 7.4 自动化告警规则配置
  - 配置翻译失败率告警（阈值5%）
  - 配置数据同步过期告警（阈值24小时）
  - 配置无效模式比例告警（阈值10%）
  - 配置验证积压告警（阈值50个待验证模式）
  - _需求: 需求6.6, 需求10.5_

### 8. 测试策略和质量保证实现

- [ ] 8.1 数据完整性测试套件
  - 实现唯一约束验证测试
  - 实现外键约束功能测试（有效/无效/NULL值）
  - 实现枚举约束验证测试
  - 配置JSONB数据结构验证测试
  - _基于设计文档中的测试策略_

- [ ] 8.2 性能基准测试实现
  - 实现索引效率测试（EXPLAIN ANALYZE）
  - 配置并发操作性能测试
  - 实现JSONB查询性能验证
  - 配置复合索引效率测试
  - _需求: 需求7.5, 需求9.2_

- [ ] 8.3 业务逻辑测试实现
  - 实现4阶段翻译流水线测试
  - 配置8种正则表达式模式类型匹配测试
  - 实现复合后缀处理机制测试
  - 配置边界保护机制验证测试
  - _需求: 需求1.1-1.5, 需求3.1-3.5, 需求10.1_

- [ ] 8.4 端到端集成测试实现
  - 实现数据同步到模式生成的完整流程测试
  - 配置翻译流水线到反馈收集的集成测试
  - 实现数据一致性验证测试
  - 配置性能基准达标测试
  - _需求: 需求8.7_

### 9. 维护和管理工具实现

- [ ] 9.1 数据库健康检查工具
  - 实现必需表存在性验证
  - 配置模式验证状态检查
  - 实现优先级冲突检测
  - 配置外键约束完整性检查
  - 验证部分索引的存在和效果
  - _需求: 需求9.5_

- [ ] 9.2 性能分析和优化工具
  - 实现EXPLAIN ANALYZE查询执行计划分析
  - 配置索引使用率监控（包括部分索引）
  - 实现JSONB字段查询性能分析
  - 配置数据库连接数和查询执行时间监控
  - 验证v2.0性能优化效果（10-50x查询速度提升）
  - _需求: 需求9.2, 需求11.1_

- [x] 9.3 数据迁移和备份工具
  - 实现安全的数据迁移脚本
  - 配置数据完整性验证机制
  - 实现备份和恢复流程
  - 配置在线DDL操作支持
  - 实现分区表的备份和恢复策略
  - _需求: 需求9.1, 需求9.4_

- [x] 9.4 未来扩展和维护支持实现（需求14）
  - 配置ENUM类型的在线扩展支持（ALTER TYPE语句）
  - 实现metadata JSONB字段的向后兼容结构扩展机制
  - 配置新AWS服务的无冲突添加流程（基于authoritative_full_name业务主键）
  - 实现新翻译阶段的支持（如processing_stage4的枚举扩展）
  - 预留多语言字段的国际化支持（full_name_cn、short_name_cn）
  - 配置系统持续演进和优化的维护策略
  - _需求: 需求14.1, 需求14.2, 需求14.3, 需求14.4, 需求14.5_

### 10. 文档和部署准备

- [ ] 10.1 数据库部署文档编写
  - 编写PostgreSQL环境配置指南
  - 创建数据库架构部署步骤文档
  - 编写外键约束验证和修复指南
  - 创建性能调优和监控配置文档
  - _基于所有实施的组件和测试结果_

- [ ] 10.2 运维手册和故障排查指南
  - 编写常见问题诊断和解决方案
  - 创建监控指标解读指南
  - 编写数据库维护和优化建议
  - 创建安全配置和权限管理指南
  - _需求: 需求9.3, 需求9.5_

## 任务依赖关系

```
1. 数据库基础架构搭建 (1.1-1.3)
   ↓
2. 核心数据表结构实现 (2.1-2.6)
   ↓
3. 外键约束和数据完整性实现 (3.1-3.3)
   ↓
4. 索引策略和性能优化实现 (4.1-4.3)
   ↓
5. 8种正则表达式模式类型系统实现 (5.1-5.3)
   ↓
6. 数据初始化和种子数据实现 (6.1-6.4)
   ↓
7. 监控和审计系统实现 (7.1-7.4)
   ↓
8. 测试策略和质量保证实现 (8.1-8.4)
   ↓
9. 维护和管理工具实现 (9.1-9.3)
   ↓
10. 文档和部署准备 (10.1-10.2)
```

## 关键里程碑

- **里程碑1**: 完成数据库基础架构和核心表结构（任务1-2）
- **里程碑2**: 完成数据完整性和性能优化（任务3-4）
- **里程碑3**: 完成8种模式类型系统和数据初始化（任务5-6）
- **里程碑4**: 完成监控系统和测试套件（任务7-8）
- **里程碑5**: 完成维护工具和部署文档（任务9-10）

## 验收标准

每个任务完成后需要满足以下验收标准：
- 所有相关的需求验收标准得到满足
- 通过相应的测试用例验证
- 代码和配置符合最佳实践
- 完成相关文档更新
- 通过代码审查和质量检查