---
type: "manual"
---

## 文档范围（Scope）与定位

本指南面向数据库工程/后端工程，聚焦：
- 数据模型与约束（表/枚举/外键/检查约束）
- 性能与索引（部分索引、JSONB GIN）
- 查询模式与存储过程/函数
- 面向组件的数据库契约（PatternLoader/SuffixAssembler/MentionTracker/BoundaryGuard 所需的查询与索引）

不包含（Out-of-scope）：
- 应用层/服务层的算法实现与类/方法细节（例如 Aho-Corasick 自动机构建、分段正则的具体代码、占位符替换流程）。
  相关内容已迁移至：.kiro/steering/pattern-matching-engine-guide.md

# 数据库开发指导文档 - v2.0 优化版

## 核心设计原则

### 逻辑外化 (Externalization of Logic)
将业务规则（服务名、品牌词、URL映射、正则表达式）存储在数据库中，支持动态更新而无需重新部署代码。采用模块化组件设计和高性能算法优化。

### 过程可追溯 (Traceability)
`translation_jobs` 表记录翻译任务的4阶段完整生命周期，支持调试和质量审计。

### 原子化与规范化
不同类型规则分离到独立表：`brand_term_mappings`, `url_mappings`, `regex_patterns`。采用ENUM类型提供类型安全和数据一致性。

### JSONB优化与组件化支持
半结构化数据使用JSONB字段，配合GIN索引实现高效查询。支持SuffixAssembler、MentionTracker等组件化架构的元数据存储需求。

### 高性能策略 (v2.0 核心优化)
- **部分索引 (Partial Index)**: 只索引活跃数据，减少索引大小60-80%，查询速度提升10-50x
- **Aho-Corasick预扫描**: O(n)复杂度的候选词预扫描，配合分段正则表达式
- **组件化架构**: PatternLoader、ServiceMatcher、SuffixAssembler、BoundaryGuard等模块化设计

## 核心数据表

### service_names (v2架构)
AWS服务名称表，支持多数据源同步：
- `authoritative_full_name`: 业务主键，来自官网网页的权威服务全称
- `base_name`: 翻译逻辑用，规范化基础名称（不含括号）
- `internal_name`: 来自PDF的 internal name
- `full_name_en`: 英文全称，与 authoritative_full_name 相同
- `short_name_en`: 英文缩写，来自PDF或等于全称
- `full_name_cn`: 中文全称（预留字段）
- `short_name_cn`: 中文缩写（预留字段）
- `service_code`: AWS官方服务代码 (ec2, s3等)
- `source`: 数据来源 (web_scrape, pdf_parse, manual)
- `is_active`: 是否启用
- `notes`: 备注
- `last_synced_at`: 上次同步时间

### regex_patterns (8种模式类型) - v2.0 优化版
正则表达式模式表，优先级90-130，已应用Amazon_service_name_matching_solutio.md优化建议：

**核心字段结构**：
- `pattern_name`: 模式的易读名称 (VARCHAR(100) UNIQUE)
- `pattern_type`: **优化：改回 ENUM 类型** (regex_pattern_type) - 提供类型安全和数据一致性
- `regex_string`: 正则表达式本身 (TEXT) - **新增长度约束 < 10000 字符**
- `related_service_id`: 关联的服务ID (外键到service_names表)
- `service_code`: 服务代码，如 "ec2", "s3"
- `priority`: 匹配优先级，数字越大优先级越高 (默认100)
- `notes`: 传统备注字段，保持向后兼容
- **`metadata`: 新增 JSONB 字段** - 存储结构化元数据，支持SuffixAssembler组件
- `is_active`: 是否启用
- `validation_status`: 验证状态 (pending, valid, invalid)

**ENUM 类型定义**：
```sql
CREATE TYPE regex_pattern_type AS ENUM (
    'SERVICE_NAME',         -- 服务名称
    'TIMEZONE',             -- 时区
    'CLI_COMMAND',          -- CLI命令
    'URL',                  -- URL
    'GENERAL',              -- 通用
    'CONTEXT_PROTECTED'     -- 上下文保护模式
);
```

**metadata JSONB 字段结构**：
```json
{
    "isCompoundWithSuffix": true,
    "suffixGroup": 1,
    "patternCategory": "full_complex_suffix",
    "hasBoundaryProtection": true
}
```

**8种核心模式类型**：
1. **全称复合后缀** (120) - 完整名称+后缀
2. **全称标准** (115) - 精确匹配+边界保护
3. **简称复合后缀** (110) - 简称+后缀
4. **简称标准** (105) - 简称+边界保护
5. **缩写复合后缀** (100) - 缩写+后缀
6. **缩写标准** (95) - 纯缩写+边界保护
7. **特殊变体** (125) - Aurora PostgreSQL等
8. **上下文保护** (90) - 避免误匹配

#### 8种模式类型详细解读

**核心概念**：在AWS批量邮件系统中，**"后缀"（suffix）**是指**跟在AWS服务名称后面的相关技术术语或限定词**，用于更精确地识别和处理复合服务名称表达。

##### 带后缀的模式类型（复合后缀模式）

**1. 全称复合后缀模式** (优先级: 120)
- **匹配对象**: `Amazon Elastic Compute Cloud (EC2) instance`
- **后缀部分**: `instance`, `instances`, `instance family`, `instance type`
- **正则示例**: `Amazon\\s+Elastic\\s+Compute\\s+Cloud\\s*\\(EC2\\)\\s+((?:[A-Za-z0-9][a-zA-Z0-9.-]*\\s+)?(?:instance|instances|instance\\s+family|instance\\s+type))`

**3. 简称复合后缀模式** (优先级: 110)
- **匹配对象**: `Amazon EC2 instance`
- **后缀部分**: `instance`, `instances` 等
- **正则示例**: `Amazon\\s+EC2\\s+((?:[A-Za-z0-9][a-zA-Z0-9.-]*\\s+)?(?:instance|instances|instance\\s+family|instance\\s+type))`

**5. 缩写复合后缀模式** (优先级: 100)
- **匹配对象**: `EC2 instance`
- **后缀部分**: `instance`, `instances` 等
- **正则示例**: `(?<![:_-])\\bEC2\\s+((?:[A-Za-z0-9][a-zA-Z0-9.-]*\\s+)?(?:instance|instances|instance\\s+family|instance\\s+type))(?![:_-])`

##### 标准模式（无后缀）

**2. 全称标准模式** (优先级: 115) - 精确匹配+边界保护
**4. 简称标准模式** (优先级: 105) - 简称+边界保护
**6. 缩写标准模式** (优先级: 95) - 纯缩写+边界保护

##### 特殊模式

**7. 特殊变体模式** (优先级: 125) - 处理Aurora PostgreSQL等特殊情况
**8. 上下文保护模式** (优先级: 90) - 避免在ARN、URL等上下文中误匹配

##### 具体后缀类型示例

**EC2服务的后缀**:
- `instance` / `instances`
- `instance family`
- `instance type`
- `P3 instances` (带规格的实例)

**EBS服务的后缀**:
- `volume` / `volumes`
- `volume(s)`

**IAM服务的后缀**:
- `policy` / `policies`
- `role` / `roles`
- `user` / `users`

**RDS服务的后缀**:
- `for MySQL`
- `for PostgreSQL`
- `for Oracle`

##### 技术实现机制

**数据库标记方式**：
在`regex_patterns`表的`notes`字段中标记：
```sql
'isCompoundWithSuffix: true, suffixGroup: 1'
```

其中：
- `isCompoundWithSuffix: true`: 标识这是复合后缀模式
- `suffixGroup: 1`: 指示正则表达式中第1个捕获组包含后缀内容

##### 复合后缀模式标识详细分析

**1. `isCompoundWithSuffix: true` 的作用**

这个标识告诉处理程序："这个正则表达式不是简单的服务名匹配，而是要处理'服务名+后缀'的复合表达"。

**没有这个标识的情况**：
```
原文: "EC2 instance"
简单处理: 替换为 "Amazon Elastic Compute Cloud (EC2)"
结果: 丢失了 "instance" 信息 ❌
```

**有这个标识的情况**：
```
原文: "EC2 instance"
智能处理: 识别为复合表达，保留后缀
结果: "Amazon Elastic Compute Cloud (EC2) instance" ✅
```

**2. `suffixGroup: 1` 的作用**

这个标识指定了正则表达式中哪个捕获组包含需要保留的后缀内容。

**正则表达式示例**：
```regex
(?<![:_-])\bEC2\s+((?:[A-Za-z0-9][a-zA-Z0-9.-]*\s+)?(?:instance|instances|instance\s+family|instance\s+type))(?![:_-])
```

**分解分析**：
- `(?<![:_-])\bEC2\s+`: 匹配独立的"EC2"和后面的空格
- `((?:[A-Za-z0-9][a-zA-Z0-9.-]*\s+)?(?:instance|instances|instance\s+family|instance\s+type))`: **第1个捕获组**，匹配后缀部分

##### 具体实例分析

**实例1：EC2 Instance 处理**

| 原始文本 | 匹配结果 | 捕获组1内容 | 最终结果 |
|---------|---------|------------|----------|
| `"EC2 instance"` | 整个匹配项 | `"instance"` | `"Amazon Elastic Compute Cloud (EC2) instance"` |
| `"EC2 P3 instances"` | 整个匹配项 | `"P3 instances"` | `"Amazon Elastic Compute Cloud (EC2) P3 instances"` |
| `"EC2 instance family"` | 整个匹配项 | `"instance family"` | `"Amazon Elastic Compute Cloud (EC2) instance family"` |

**实例2：RDS 数据库引擎处理**

正则表达式：`Amazon\s+RDS\s*(for\s+[\w\s().-]+)`

| 原始文本 | 捕获组1内容 | 最终结果 |
|---------|------------|----------|
| `"Amazon RDS for MySQL"` | `"for MySQL"` | `"Amazon Relational Database Service (RDS) for MySQL"` |
| `"Amazon RDS for PostgreSQL"` | `"for PostgreSQL"` | `"Amazon Relational Database Service (RDS) for PostgreSQL"` |

**实例3：IAM 资源类型处理**

正则表达式：`\bIAM\s+(policy|policies|role|roles|user|users)`

| 原始文本 | 捕获组1内容 | 最终结果 |
|---------|------------|----------|
| `"IAM policy"` | `"policy"` | `"Amazon Identity and Access Management (IAM) policy"` |
| `"IAM roles"` | `"roles"` | `"Amazon Identity and Access Management (IAM) roles"` |

##### 程序处理逻辑

```python
def process_compound_pattern(match, pattern_info, service_info):
    """处理复合后缀模式"""

    # 检查是否为复合模式
    if pattern_info.get('isCompoundWithSuffix'):
        suffix_group = pattern_info.get('suffixGroup', 1)

        # 提取后缀内容
        suffix_content = match.group(suffix_group)

        # 获取标准化服务名称
        if is_first_mention(service_info['base_name']):
            service_name = service_info['full_name_en']
        else:
            service_name = service_info['short_name_en']

        # 重新组装：服务名 + 空格 + 后缀
        return f"{service_name} {suffix_content}"

    else:
        # 简单模式，直接替换
        return get_service_name(service_info)
```

##### 实际应用场景

**场景1：技术文档翻译**
```
原文: "You can launch EC2 P3 instances for machine learning workloads."
处理: 识别"EC2 P3 instances"为复合表达
结果: "您可以启动Amazon EC2 P3实例用于机器学习工作负载。"
```

**场景2：配置说明翻译**
```
原文: "Configure your IAM policies to allow access."
处理: 识别"IAM policies"为复合表达
结果: "配置您的Amazon Identity and Access Management (IAM)策略以允许访问。"
```

**处理逻辑流程**：
1. **识别阶段**: 正则表达式匹配"服务名称+后缀"的完整表达
2. **占位符阶段**: 将整个复合表达替换为占位符（如`__SRVCNM_0__`）
3. **翻译阶段**: LLM翻译时保持占位符不变
4. **恢复阶段**: 将占位符恢复为"标准化中文服务名称+适当中文后缀"

##### 实际应用示例

**原始文本**:
```
"We detected an issue with some EC2 P3 instances."
```

**匹配过程**:
1. **匹配规则**: `EC2_ACRONYM_COMPLEX_SUFFIX`
2. **正则表达式**: `(?<![:_-])\\bEC2\\s+((?:[A-Za-z0-9][a-zA-Z0-9.-]*\\s+)?(?:instance|instances|instance\\s+family|instance\\s+type))(?![:_-])`
3. **匹配结果**: 整个匹配项是`"EC2 P3 instances"`
4. **后缀捕获**: 第1个捕获组捕获到`"P3 instances"`

**标准化结果**:
- **首次提及**: `Amazon Elastic Compute Cloud (EC2) P3 instances`
- **后续提及**: `Amazon EC2 P3 instances`

##### 设计优势

1. **信息保真**: 避免丢失重要的技术细节（如"P3"规格信息）
2. **减少冗余**: 一条智能规则覆盖多种后缀组合，避免为每种组合创建独立规则
3. **高度灵活**: 可轻松处理各种复合表达，如`RDS for PostgreSQL`、`IAM role`等
4. **上下文感知**: 确保翻译质量和技术准确性

这种"后缀"机制是AWS批量邮件系统实现高质量、上下文感知翻译的关键技术，完美体现了"逻辑外化"设计理念的精髓。

### translation_jobs (4阶段追踪)
翻译任务生命周期：
- Stage 0: 原始文本
- Stage 1: 文本预处理标准化 (原JS标准化，现Python实现)
- Stage 2: LLM翻译
- Stage 3: 最终中文
- JSONB字段: `placeholder_map`, `service_mention_state`

**Stage 1 详细说明**：
- **术语标准化**：服务名称、品牌术语、技术缩写的规范化处理
- **占位符生成**：将标准化内容替换为 `__XXX_YYY__` 格式占位符
- **上下文保护**：ARN、URL、CLI命令、JSON块的边界保护
- **时区转换**：多时区时间戳统一转换为UTC+8格式
- **状态追踪**：全局和行级作用域的服务名称首次/后续提及管理

### brand_term_mappings & url_mappings
品牌术语和URL本地化规则，支持优先级和正则表达式。

## AWS服务名称同步模块集成 ⭐

AWS服务名称同步模块是批量邮件翻译系统的重要组成部分，负责定期从官方网页和PDF文档中获取、整理和同步AWS中国区的服务名称信息。本章节说明该模块与系统数据库的集成规范。

### 模块定位与职责
- **模块定位**: 作为批量邮件翻译系统的数据源管理模块
- **核心职责**: 为翻译流水线中的阶段1（文本预处理标准化）提供服务名称识别和占位符替换的基础数据
- **设计原则**: 遵循功能精简原则，只实现核心业务功能和基本错误处理

### 数据库集成接口规范

#### 1. 服务数据同步接口
```python
def sync_service_data(service_data):
    """
    服务数据同步的标准接口
    使用 authoritative_full_name 作为业务主键进行UPSERT操作
    """
    sql = """
    INSERT INTO service_names
        (authoritative_full_name, base_name, internal_name, full_name_en,
         short_name_en, service_code, source, last_synced_at)
    VALUES
        (%(auth_name)s, %(base_name)s, %(internal_name)s, %(full_name)s,
         %(short_name)s, %(service_code)s, 'web_scrape', NOW())
    ON CONFLICT (authoritative_full_name)
    DO UPDATE SET
        base_name = EXCLUDED.base_name,
        internal_name = EXCLUDED.internal_name,
        full_name_en = EXCLUDED.full_name_en,
        short_name_en = EXCLUDED.short_name_en,
        service_code = EXCLUDED.service_code,
        last_synced_at = NOW(),
        updated_at = NOW()
    """
```

#### 2. 正则表达式模式生成接口
模块自动生成8种核心模式类型，优先级90-130：
- 全称复合后缀模式 (120) - 完整名称+后缀
- 全称标准模式 (115) - 精确匹配+边界保护
- 简称复合后缀模式 (110) - 简称+后缀
- 简称标准模式 (105) - 简称+边界保护
- 缩写复合后缀模式 (100) - 缩写+后缀
- 缩写标准模式 (95) - 纯缩写+边界保护
- 特殊变体模式 (125) - Aurora PostgreSQL等
- 上下文保护模式 (90) - 避免误匹配

#### 3. 翻译系统数据提供接口
```python
def provide_patterns_for_translation_system():
    """为翻译系统提供优化的模式数据"""
    query = """
    SELECT pattern_name, regex_string, metadata, priority, service_code,
           related_service_id
    FROM regex_patterns
    WHERE is_active = TRUE AND validation_status = 'valid'
    ORDER BY priority DESC, id ASC;
    """
    # 此查询利用 idx_regex_patterns_active_valid 部分索引
    # 为翻译系统的高性能匹配提供优化支持
    return execute_query(query)
```

### 模块特定优化建议

#### 1. 批量操作优化
- 使用 `psycopg2.extras.execute_values` 进行批量插入
- 实施分页处理（每批100条记录）避免内存溢出
- 使用数据库事务确保操作原子性

#### 2. 性能监控要点
- 监控同步操作的执行时间
- 跟踪数据库连接池使用情况
- 记录模式生成的成功率

#### 3. 错误处理策略
- 网络请求重试3次，数据库操作重试5次
- 使用指数退避算法，基础延迟1-2秒
- 详细记录错误上下文便于故障排查

### 集成最佳实践
1. **数据一致性**: 确保 `authoritative_full_name` 字段的唯一性和准确性
2. **性能优化**: 利用部分索引 `idx_service_names_active_code` 和 `idx_regex_patterns_active_valid`
3. **边界保护**: 实现ARN、URL等上下文中的误匹配避免机制
4. **特殊服务**: 为Aurora、Health、RDS等服务提供专门处理策略

> 📋 **详细实现指导**: AWS服务同步模块的具体实现细节请参考
> **[AWS服务同步模块开发指南](aws-service-sync-guide.md)**

#### Lambda函数集成示例
```python
def lambda_handler(event, context):
    """AWS服务同步Lambda函数入口 - 保持简洁"""
    try:
        logger.info("开始执行服务同步")

        # 1. 初始化配置
        config_manager = ConfigManager()
        config = config_manager.get_config()

        # 2. 抓取和处理数据
        web_scraper = WebScraper()
        pdf_parser = PDFParser(config)
        data_processor = DataProcessor()

        web_services = web_scraper.scrape_services()
        pdf_services = pdf_parser.parse_pdf()
        merged_services = data_processor.process_services(web_services, pdf_services)

        # 3. 存储到数据库
        rds_client = RDSClient(config)
        stored_count = rds_client.store_services(merged_services)

        # 4. 生成和存储正则表达式模式
        all_patterns = batch_generate_all_patterns(merged_services)
        validated_count, conflicts = batch_insert_patterns_optimized(all_patterns)

        logger.info(f"同步完成: {stored_count}个服务, {validated_count}个模式")

        return {
            'statusCode': 200,
            'body': json.dumps({
                'success': True,
                'processed_count': stored_count,
                'pattern_count': validated_count
            })
        }

    except Exception as e:
        logger.error(f"同步失败: {e}")
        return {
            'statusCode': 500,
            'body': json.dumps({'success': False, 'error': str(e)})
        }
```

## 开发最佳实践

### 数据库操作模式
- 使用上下文管理器进行事务管理
- JSONB字段查询：`placeholder_map ? '__SRVCNM_0__'`
- 优先级查询：`ORDER BY priority DESC, id ASC` (数值越大优先级越高)
- 使用`authoritative_full_name`作为业务主键进行UPSERT操作

### 批量操作模式
```python
# 批量UPSERT - 使用authoritative_full_name作为业务主键
def batch_upsert_services(services_data):
    insert_sql = """
    INSERT INTO service_names (authoritative_full_name, base_name, service_code, source)
    VALUES %s ON CONFLICT (authoritative_full_name)
    DO UPDATE SET base_name = EXCLUDED.base_name, last_synced_at = NOW()
    """
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            psycopg2.extras.execute_values(cur, insert_sql, services_data, page_size=100)

# 8种模式类型生成 - 为每个服务生成完整模式集合
def generate_service_patterns(service_data):
    patterns = []
    # 1. 全称复合后缀 (120) - 完整名称+后缀
    # 2. 全称标准 (115) - 精确匹配+边界保护
    # 3. 简称复合后缀 (110) - 简称+后缀
    # 4. 简称标准 (105) - 简称+边界保护
    # 5. 缩写复合后缀 (100) - 缩写+后缀
    # 6. 缩写标准 (95) - 纯缩写+边界保护
    # 7. 特殊变体 (125) - Aurora PostgreSQL等
    # 8. 上下文保护 (90) - 避免误匹配
    return patterns
```

## 性能优化要点 - v2.0 高性能策略

### 索引策略 (关键性能提升点)
基于Amazon_service_name_matching_solutio.md的优化建议：

#### 部分索引 (Partial Index) - 核心优化
**关键性能提升**：只索引活跃数据，减少索引大小60-80%，查询速度提升10-50x

```sql
-- 只索引活跃的服务 - 服务于PatternLoader组件
CREATE INDEX idx_service_names_active_code
ON service_names(service_code) WHERE is_active = TRUE;

-- 只索引活跃且有效的模式 - 直接服务于PatternLoader
CREATE INDEX idx_regex_patterns_active_valid
ON regex_patterns(priority DESC, id ASC)
WHERE is_active = TRUE AND validation_status = 'valid';
```

#### JSONB 高性能索引
```sql
-- 为metadata JSONB字段创建GIN索引 - 支持SuffixAssembler组件
CREATE INDEX idx_regex_patterns_metadata
ON regex_patterns USING GIN (metadata);

-- 翻译任务JSONB字段索引
CREATE INDEX idx_translation_jobs_placeholder_map
ON translation_jobs USING GIN (placeholder_map);

CREATE INDEX idx_translation_jobs_service_mention_state
ON translation_jobs USING GIN (service_mention_state);
```


### 查询优化 (组件化架构支持)

#### PatternLoader 优化查询
```sql
-- 高性能模式加载 - 利用部分索引
SELECT pattern_name, regex_string, metadata, priority
FROM regex_patterns
WHERE is_active = TRUE AND validation_status = 'valid'
ORDER BY priority DESC, id ASC;
```

#### SuffixAssembler 元数据查询
```sql
-- 查询复合后缀模式 - 利用JSONB索引
SELECT pattern_name, regex_string, metadata->>'suffixGroup' as suffix_group
FROM regex_patterns
WHERE metadata->>'isCompoundWithSuffix' = 'true'
  AND is_active = TRUE;
```

#### MentionTracker 状态查询
```sql
-- 服务提及状态查询 - 利用GIN索引
SELECT id, service_mention_state
FROM translation_jobs
WHERE service_mention_state ? 'Amazon Elastic Compute Cloud';
```

### 连接池配置 (高并发优化)
```python
# 推荐配置 - 支持高并发UPSERT操作
DATABASE_CONFIG = {
    'minconn': 5,
    'maxconn': 20,
    'maxconnections': 50,
    'blocking': True,
    'ping': 1,
    # 避免死锁的锁定策略
    'options': '-c lock_timeout=30s -c statement_timeout=60s'
}
```

### 并发处理优化
```sql
-- 高并发UPSERT - 避免死锁
SELECT ... FROM service_names
WHERE authoritative_full_name = %s
FOR UPDATE SKIP LOCKED;
```

## 边界保护机制

### ARN保护
避免在ARN字符串中匹配服务名：`(?<!arn:aws[^:]*:[^:]*:[^:]*:[^:]*:)`

### URL保护
避免在URL路径中匹配服务名：`(?<!https?://[^\s]*)`

### 代码标识符保护
避免在代码标识符中匹配：`(?<![:_-])` 和 `(?![:_-])`

### 特殊服务处理
- **Aurora**: 区分通用Aurora和特定引擎变体(PostgreSQL/MySQL)
- **RDS**: 支持"RDS for Engine"模式和通用RDS模式
- **Health**: 区分Health Dashboard和通用Health服务

## 关键查询模式

### 服务模式查询
```sql
-- 按优先级获取活跃的服务匹配规则
SELECT rp.regex_string, rp.priority, sn.base_name
FROM regex_patterns rp JOIN service_names sn ON rp.related_service_id = sn.id
WHERE rp.pattern_type = 'SERVICE_NAME' AND rp.is_active = true
ORDER BY rp.priority DESC, rp.id ASC;
```

### 复合模式识别
```sql
-- 识别支持后缀的复合模式
SELECT pattern_name, regex_string FROM regex_patterns
WHERE notes LIKE '%isCompoundWithSuffix: true%' AND is_active = true
ORDER BY priority DESC;
```

## 组件化架构支持 - v2.0 模块化设计

### PatternLoader 组件支持

> 应用层实现详见：.kiro/steering/pattern-matching-engine-guide.md 的“PatternLoader”小节

**职责**：从数据库一次性拉取活跃且有效的正则规则，构建高效内存查找结构

```python
class PatternLoader:
    """高性能模式加载器 - 利用部分索引优化"""

    async def load_patterns(self):
        """利用优化索引加载模式"""
        query = """
        SELECT pattern_name, regex_string, metadata, priority, service_code
        FROM regex_patterns
        WHERE is_active = TRUE AND validation_status = 'valid'
        ORDER BY priority DESC, id ASC;
        """
        # 此查询将利用 idx_regex_patterns_active_valid 部分索引
        # 性能提升：10-50x
        return await self.db_pool.fetch(query)

    def build_aho_corasick_automaton(self, patterns):
        """构建Aho-Corasick自动机用于预扫描"""
        # 提取非正则文本关键词
        keywords = self.extract_keywords(patterns)
        # 构建Trie树映射
        return self.build_automaton(keywords)

> 应用层实现详见：.kiro/steering/pattern-matching-engine-guide.md 的“SuffixAssembler”小节

```

### SuffixAssembler 组件支持
**职责**：处理复合后缀模式，利用metadata JSONB字段

```python
class SuffixAssembler:
    """后缀组装器 - 利用JSONB元数据"""

    def get_compound_patterns(self):
        """获取复合后缀模式"""
        query = """
        SELECT pattern_name, regex_string,
               metadata->>'suffixGroup' as suffix_group,
               metadata->>'isCompoundWithSuffix' as is_compound
        FROM regex_patterns
        WHERE metadata->>'isCompoundWithSuffix' = 'true'
          AND is_active = TRUE;
        """
        # 利用 idx_regex_patterns_metadata GIN索引
        return self.db_pool.fetch(query)

    def assemble_service_with_suffix(self, service_name, suffix, is_first_mention):
        """组装服务名称和后缀"""
        if is_first_mention:
            return f"{service_name} {suffix}"

> 应用层实现详见：.kiro/steering/pattern-matching-engine-guide.md 的“MentionTracker”小节

        else:
            # 使用简称
            return f"{self.get_short_name(service_name)} {suffix}"
```

### MentionTracker 组件支持
**职责**：跟踪服务首次/后续提及状态

```python
class MentionTracker:
    """提及状态追踪器 - 利用JSONB状态存储"""

    def update_mention_state(self, job_id, service_base_name, used_full_name):
        """更新服务提及状态"""
        query = """
        UPDATE translation_jobs
        SET service_mention_state =
            COALESCE(service_mention_state, '{}'::jsonb) ||
            jsonb_build_object(%s, jsonb_build_object(
                'mentioned', true,
                'used_full_name', %s,
                'timestamp', NOW()::text
            ))
        WHERE id = %s;
        """
        return self.db_pool.execute(query, service_base_name, used_full_name, job_id)

    def get_mention_history(self, job_id):
        """获取提及历史 - 利用GIN索引"""

> 应用层实现详见：.kiro/steering/pattern-matching-engine-guide.md 的“BoundaryGuard”小节

        query = """
        SELECT service_mention_state
        FROM translation_jobs
        WHERE id = %s AND service_mention_state IS NOT NULL;
        """
        return self.db_pool.fetchrow(query, job_id)
```

### BoundaryGuard 组件支持
**职责**：边界保护，利用CONTEXT_PROTECTED类型模式

```python
class BoundaryGuard:
    """边界保护器 - 利用上下文保护模式"""

    def load_protection_patterns(self):
        """加载上下文保护模式"""
        query = """
        SELECT regex_string, metadata
        FROM regex_patterns
        WHERE pattern_type = 'CONTEXT_PROTECTED'
          AND is_active = TRUE;
        """
        return self.db_pool.fetch(query)

    def validate_matches(self, matches, text):
        """验证匹配结果，剔除边界内误匹配"""
        protected_ranges = self.find_protected_ranges(text)
        return [m for m in matches if not self.in_protected_range(m, protected_ranges)]
```

## 核心开发模式

### 数据库健康检查要点
- 验证必需表存在：`service_names`, `regex_patterns`, `brand_term_mappings`, `url_mappings`
- 检查模式验证状态：无效模式数量
- 检查优先级冲突：相同优先级的活跃模式

## 模式生成实现代码 - v2.0 完整实现

### 后缀模式定义

```python
# 通用后缀模式定义
COMMON_SUFFIX_PATTERNS = {
    'instance': r'((?:[A-Za-z0-9][a-zA-Z0-9.-]*\s+)?(?:instance|instances|instance\s+family|instance\s+type))',
    'volume': r'(volume\(s\)?|volumes|volume)',
    'database': r'(for\s+(?:PostgreSQL|MySQL|MariaDB|Oracle|SQL\s+Server))',
    'cluster': r'(cluster|clusters)',
    'service': r'(service|services)',
    'resource': r'(resource|resources)'
}

def get_service_suffix_patterns(service_code):
    """根据服务代码获取适用的后缀模式"""
    suffix_mapping = {
        'ec2': ['instance', 'volume'],
        'rds': ['database', 'cluster'],
        'ecs': ['cluster', 'service'],
        'eks': ['cluster'],
        's3': ['resource'],
        'lambda': ['resource']
    }

    applicable_suffixes = suffix_mapping.get(service_code, ['resource'])
    return '|'.join([COMMON_SUFFIX_PATTERNS[suffix] for suffix in applicable_suffixes])
```

### 边界保护实现

```python
def generate_boundary_protected_pattern(service_name):
    """生成带完整边界保护的模式"""
    # ARN保护：避免在ARN中匹配
    arn_protection = r'(?<!arn:aws[^:]*:[^:]*:[^:]*:[^:]*:)'

    # URL保护：避免在URL中匹配
    url_protection = r'(?<!https?://[^\s]*)'

    # 代码保护：避免在代码标识符中匹配
    code_protection = r'(?<![:_-])'

    # 后续保护：避免作为更大标识符的一部分
    trailing_protection = r'(?![:_-])'
    url_trailing_protection = r'(?![^\s]*\.[a-z]{2,4})'

    return f"{arn_protection}{url_protection}{code_protection}\\b{re.escape(service_name)}\\b{trailing_protection}{url_trailing_protection}"

def generate_context_aware_patterns(service_data):
    """生成上下文感知的模式"""
    patterns = []

    # CLI命令上下文
    cli_pattern = {
        'pattern_name': f"{get_acronym(service_data['full_name_en'])}_CLI_CONTEXT",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': f"(?<=aws\\s){service_data['service_code']}(?=\\s)",
        'priority': 130,
        'notes': 'CLI command context pattern'
    }
    patterns.append(cli_pattern)

    # 配置文件上下文
    config_pattern = {
        'pattern_name': f"{get_acronym(service_data['full_name_en'])}_CONFIG_CONTEXT",
        'pattern_type': 'SERVICE_NAME',
        'regex_string': f"(?<=\"){service_data['service_code']}(?=\")",
        'priority': 128,
        'notes': 'Configuration file context pattern'
    }
    patterns.append(config_pattern)

    return patterns
```

### 特殊服务处理实现

```python
def generate_aurora_patterns():
    """生成Aurora服务的特殊模式"""
    base_patterns = [
        {
            'service_name': 'Amazon Aurora',
            'variants': ['PostgreSQL', 'MySQL'],
            'priority_base': 125
        }
    ]

    patterns = []
    for base in base_patterns:
        # 通用Aurora模式
        patterns.append({
            'pattern_name': 'AURORA_GENERAL',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': r'(?<![:_-])\bAurora\b(?![\s-](?:PostgreSQL|MySQL)\b)',
            'priority': base['priority_base'],
            'notes': 'General Aurora pattern excluding specific variants'
        })

        # 特定引擎变体
        for variant in base['variants']:
            patterns.append({
                'pattern_name': f'AURORA_{variant.upper()}_VARIANT',
                'pattern_type': 'SERVICE_NAME',
                'regex_string': f"(?:Amazon\\s+Aurora|AWS\\s+Aurora|\\bAurora)\\s+{variant}\\b",
                'priority': base['priority_base'] + 5,
                'notes': f'Aurora {variant} specific variant'
            })

    return patterns

def generate_health_patterns():
    """生成Health服务的特殊模式"""
    return [
        {
            'pattern_name': 'HEALTH_DASHBOARD_FULL',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': r'(?:Amazon|AWS)\s+Health\s+Dashboard',
            'priority': 125,
            'notes': 'Full Health Dashboard pattern'
        },
        {
            'pattern_name': 'HEALTH_GENERAL',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': r'(?:Amazon|AWS)\s+Health\b(?!Lake|Imaging|\s+Dashboard)',
            'priority': 120,
            'notes': 'General Health pattern excluding other Health services'
        }
    ]

def generate_rds_patterns():
    """生成RDS服务的特殊模式"""
    engines = ['PostgreSQL', 'MySQL', 'MariaDB', 'Oracle', 'SQL Server']
    patterns = []

    for engine in engines:
        # RDS for Engine模式
        patterns.append({
            'pattern_name': f'RDS_FOR_{engine.replace(" ", "_").upper()}',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': f"(?:Amazon\\s+RDS|AWS\\s+RDS|\\bRDS)\\s+for\\s+{re.escape(engine)}",
            'priority': 125,
            'notes': f'RDS for {engine} specific pattern'
        })

    # 通用RDS模式（排除特定引擎）
    engine_exclusion = '|'.join([f'for\\s+{re.escape(engine)}' for engine in engines])
    patterns.append({
        'pattern_name': 'RDS_GENERAL',
        'pattern_type': 'SERVICE_NAME',
        'regex_string': f"(?:Amazon\\s+Relational\\s+Database\\s+Service|Amazon\\s+RDS|AWS\\s+RDS|\\bRDS)(?!\\s+(?:{engine_exclusion}))",
        'priority': 115,
        'notes': 'General RDS pattern excluding specific engine variants'
    })

    return patterns
```

### 完整模式生成器

```python
def generate_comprehensive_service_patterns(service_data):
    """为单个服务生成完整的模式集合"""
    patterns = []

    # 1. 基础模式（8种核心类型）
    if has_complex_suffix_support(service_data):
        patterns.append(generate_full_complex_suffix_pattern(service_data))
        patterns.append(generate_short_complex_suffix_pattern(service_data))
        patterns.append(generate_acronym_complex_suffix_pattern(service_data))

    patterns.append(generate_full_standard_pattern(service_data))
    patterns.append(generate_short_standard_pattern(service_data))
    patterns.append(generate_acronym_standard_pattern(service_data))

    # 2. 特殊变体模式
    if has_special_variants(service_data):
        patterns.extend(generate_special_variant_patterns(service_data))

    # 3. 上下文保护模式
    patterns.extend(generate_context_aware_patterns(service_data))

    # 4. 服务特定模式
    service_code = service_data['service_code']
    if service_code == 'aurora':
        patterns.extend(generate_aurora_patterns())
    elif service_code == 'health':
        patterns.extend(generate_health_patterns())
    elif service_code == 'rds':
        patterns.extend(generate_rds_patterns())

    return patterns

def batch_generate_all_patterns(services_list):
    """批量生成所有服务的模式"""
    all_patterns = []

    for service_data in services_list:
        service_patterns = generate_comprehensive_service_patterns(service_data)

        # 添加服务关联信息
        for pattern in service_patterns:
            pattern['related_service_id'] = service_data['id']
            pattern['service_code'] = service_data['service_code']
            pattern['created_at'] = datetime.now()
            pattern['is_active'] = True

        all_patterns.extend(service_patterns)

    # 按优先级排序
    all_patterns.sort(key=lambda x: x['priority'], reverse=True)

    return all_patterns
```

### 工具函数集合

```python
def get_acronym(service_name):
    """从服务名称中提取缩写"""
    match = re.search(r'\(([^)]+)\)', service_name)
    if match:
        return match.group(1)

    # 如果没有括号，尝试从首字母生成
    words = service_name.replace('Amazon ', '').replace('AWS ', '').split()
    return ''.join([word[0].upper() for word in words if word[0].isupper()])

def has_complex_suffix_support(service_data):
    """判断服务是否支持复杂后缀"""
    suffix_supported_services = ['ec2', 'rds', 'ecs', 'eks', 'ebs']
    return service_data['service_code'] in suffix_supported_services

def has_special_variants(service_data):
    """判断服务是否有特殊变体"""
    variant_services = ['aurora', 'rds', 'health', 'iam']
    return service_data['service_code'] in variant_services

def validate_pattern_syntax(pattern):
    """验证正则表达式模式的语法正确性"""
    try:
        re.compile(pattern['regex_string'])
        return True, None
    except re.error as e:
        return False, str(e)

def detect_pattern_conflicts(patterns):
    """检测模式之间的潜在冲突"""
    conflicts = []

    for i, pattern1 in enumerate(patterns):
        for j, pattern2 in enumerate(patterns[i+1:], i+1):
            if pattern1['priority'] == pattern2['priority']:
                conflicts.append({
                    'type': 'priority_conflict',
                    'patterns': [pattern1['pattern_name'], pattern2['pattern_name']],
                    'priority': pattern1['priority']
                })

    return conflicts
```



## 高性能匹配算法实现 - v2.0 核心创新

> 注意：本章节包含的应用层算法实现细节（类/方法/流程示例）已迁移至匹配引擎文档：.kiro/steering/pattern-matching-engine-guide.md。本指南仅保留与数据库相关的接口契约、关键查询与索引配置。

### DB侧要点

以下为“高性能匹配算法实现”在数据库层面的关键支撑点。应用层实现细节（Aho‑Corasick 自动机构建、分段正则、冲突消解与占位符装配等）请参见：.kiro/steering/pattern-matching-engine-guide.md

1. 模式数据装载与排序
   - 仅加载活跃且验证通过的模式：is_active = TRUE AND validation_status = 'valid'
   - 稳定排序：ORDER BY priority DESC, id ASC
   - 依赖部分索引：idx_regex_patterns_active_valid

   示例查询：
   ```sql
   SELECT pattern_name, regex_string, metadata, priority, service_code, related_service_id
   FROM regex_patterns
   WHERE is_active = TRUE AND validation_status = 'valid'
   ORDER BY priority DESC, id ASC;
   ```

2. 复合后缀元数据（JSONB）
   - 使用 metadata JSONB 字段标注：isCompoundWithSuffix、suffixGroup、patternCategory、hasBoundaryProtection
   - 依赖 GIN 索引：idx_regex_patterns_metadata

   示例查询：
   ```sql
   SELECT pattern_name, regex_string, metadata->>'suffixGroup' AS suffix_group
   FROM regex_patterns
   WHERE metadata->>'isCompoundWithSuffix' = 'true' AND is_active = TRUE;
   ```

3. 上下文保护（CONTEXT_PROTECTED）
   - 通过 pattern_type = 'CONTEXT_PROTECTED' 提供边界保护模式集合

   示例查询：
   ```sql
   SELECT regex_string, metadata
   FROM regex_patterns
   WHERE pattern_type = 'CONTEXT_PROTECTED' AND is_active = TRUE;
   ```

4. 服务名称数据契约
   - service_names: authoritative_full_name（业务主键）、base_name、full_name_en、short_name_en、service_code
   - 部分索引：idx_service_names_active_code

5. 翻译任务状态与占位符映射（JSONB）
   - translation_jobs: placeholder_map、service_mention_state（均为 JSONB）
   - GIN 索引：idx_translation_jobs_placeholder_map、idx_translation_jobs_service_mention_state

6. 批量写入与并发优化
   - 批量插入/更新 regex_patterns 时支持 metadata JSONB
   - 提供批量过程：batch_insert_patterns_optimized(patterns JSONB)
   - 并发 UPSERT：示例 upsert_service_name_safe() 使用 SKIP LOCKED 避免死锁

> 注：应用层匹配流程与类/方法实现已迁移，DB 指南专注于数据模型、查询、索引与过程。

### 数据库优化配置 (v2.0 完整版)

```sql
-- ====================================================================
-- 服务名称表索引 (v2.0 优化版)
-- ====================================================================
CREATE INDEX idx_service_names_base_name ON service_names(base_name);
CREATE INDEX idx_service_names_service_code ON service_names(service_code);
CREATE INDEX idx_service_names_is_active ON service_names(is_active);
CREATE INDEX idx_service_names_source ON service_names(source);
CREATE INDEX idx_service_names_last_synced ON service_names(last_synced_at);

-- 优化: 部分索引 - 只索引活跃的服务
CREATE INDEX idx_service_names_active_code
ON service_names(service_code) WHERE is_active = TRUE;

-- ====================================================================
-- 正则表达式模式表索引 (v2.0 高性能版)
-- ====================================================================
CREATE INDEX idx_regex_patterns_priority ON regex_patterns(priority DESC, id ASC);
CREATE INDEX idx_regex_patterns_service ON regex_patterns(related_service_id);
CREATE INDEX idx_regex_patterns_type_active ON regex_patterns(pattern_type, is_active);
CREATE INDEX idx_regex_patterns_service_code ON regex_patterns(service_code);
CREATE INDEX idx_regex_patterns_validation_status ON regex_patterns(validation_status);

-- 优化: 关键性能提升 - 部分索引，只索引活跃且有效的模式
CREATE INDEX idx_regex_patterns_active_valid
ON regex_patterns(priority DESC, id ASC)
WHERE is_active = TRUE AND validation_status = 'valid';

-- 优化: 为JSONB metadata字段创建GIN索引
CREATE INDEX idx_regex_patterns_metadata ON regex_patterns USING GIN (metadata);

-- ====================================================================
-- 品牌术语映射表索引
-- ====================================================================
CREATE INDEX idx_brand_term_mappings_is_active ON brand_term_mappings(is_active);

-- ====================================================================
-- URL映射表索引
-- ====================================================================
CREATE INDEX idx_url_mappings_priority ON url_mappings(priority DESC);
CREATE INDEX idx_url_mappings_is_active ON url_mappings(is_active);
CREATE INDEX idx_url_mappings_is_regex ON url_mappings(is_regex);

-- ====================================================================
-- 翻译任务表索引
-- ====================================================================
CREATE INDEX idx_translation_jobs_status ON translation_jobs(status);
CREATE INDEX idx_translation_jobs_submitted_by ON translation_jobs(submitted_by);
CREATE INDEX idx_translation_jobs_submitted_at ON translation_jobs(submitted_at);
CREATE INDEX idx_translation_jobs_completed_at ON translation_jobs(completed_at);

-- GIN索引用于高效查询JSONB数据
CREATE INDEX idx_translation_jobs_placeholder_map
ON translation_jobs USING GIN (placeholder_map);

CREATE INDEX idx_translation_jobs_service_mention_state
ON translation_jobs USING GIN (service_mention_state);

-- ====================================================================
-- 用户反馈表索引
-- ====================================================================
CREATE INDEX idx_feedback_submissions_satisfaction ON feedback_submissions(satisfaction);
CREATE INDEX idx_feedback_submissions_submitted_at ON feedback_submissions(submitted_at);
CREATE INDEX idx_feedback_submissions_translation_job ON feedback_submissions(translation_job_id);

-- ====================================================================
-- 数据库优化配置 - v2.0 高性能版
-- ====================================================================

-- 1. 连接池优化配置
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET work_mem = '4MB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';

-- 2. 查询优化配置
ALTER SYSTEM SET random_page_cost = 1.1;  -- SSD优化
ALTER SYSTEM SET effective_io_concurrency = 200;  -- SSD并发优化

-- 3. JSONB优化配置
ALTER SYSTEM SET gin_fuzzy_search_limit = 0;  -- 禁用GIN模糊搜索限制

-- 4. 并发控制优化
ALTER SYSTEM SET lock_timeout = '30s';
ALTER SYSTEM SET statement_timeout = '60s';
ALTER SYSTEM SET deadlock_timeout = '1s';

-- 重新加载配置
SELECT pg_reload_conf();

-- ====================================================================
-- 高并发UPSERT优化 - 避免死锁的最佳实践
-- ====================================================================

-- 服务名称UPSERT - 使用SKIP LOCKED避免死锁
CREATE OR REPLACE FUNCTION upsert_service_name_safe(
    p_authoritative_full_name VARCHAR(255),
    p_base_name VARCHAR(255),
    p_service_code VARCHAR(50),
    p_source rule_source
) RETURNS BIGINT AS $$
DECLARE
    service_id BIGINT;
BEGIN
    -- 尝试获取现有记录（带锁跳过）
    SELECT id INTO service_id
    FROM service_names
    WHERE authoritative_full_name = p_authoritative_full_name
    FOR UPDATE SKIP LOCKED;

    IF service_id IS NOT NULL THEN
        -- 更新现有记录
        UPDATE service_names
        SET base_name = p_base_name,
            service_code = p_service_code,
            source = p_source,
            last_synced_at = NOW(),
            updated_at = NOW()
        WHERE id = service_id;

        RETURN service_id;
    ELSE
        -- 插入新记录（处理并发插入冲突）
        INSERT INTO service_names (
            authoritative_full_name, base_name, service_code, source
        ) VALUES (
            p_authoritative_full_name, p_base_name, p_service_code, p_source
        ) ON CONFLICT (authoritative_full_name) DO UPDATE SET
            base_name = EXCLUDED.base_name,
            last_synced_at = NOW(),
            updated_at = NOW()
        RETURNING id INTO service_id;

        RETURN service_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- ====================================================================
-- 批量模式插入优化 - 支持metadata JSONB字段
-- ====================================================================

CREATE OR REPLACE FUNCTION batch_insert_patterns_optimized(
    patterns JSONB
) RETURNS TABLE(inserted_count INT, conflict_count INT) AS $$
DECLARE
    pattern_record JSONB;
    inserted_count INT := 0;
    conflict_count INT := 0;
BEGIN
    -- 遍历模式数组
    FOR pattern_record IN SELECT * FROM jsonb_array_elements(patterns)
    LOOP
        BEGIN
            INSERT INTO regex_patterns (
                pattern_name, pattern_type, regex_string,
                related_service_id, service_code, priority,
                notes, metadata, validation_status
            ) VALUES (
                (pattern_record->>'pattern_name')::VARCHAR(100),
                (pattern_record->>'pattern_type')::regex_pattern_type,
                pattern_record->>'regex_string',
                (pattern_record->>'related_service_id')::BIGINT,
                pattern_record->>'service_code',
                (pattern_record->>'priority')::INTEGER,
                pattern_record->>'notes',
                pattern_record->'metadata',
                COALESCE(pattern_record->>'validation_status', 'pending')
            );

            inserted_count := inserted_count + 1;

        EXCEPTION WHEN unique_violation THEN
            -- 处理名称冲突
            UPDATE regex_patterns
            SET metadata = pattern_record->'metadata',
                updated_at = NOW()
            WHERE pattern_name = pattern_record->>'pattern_name';

            conflict_count := conflict_count + 1;
        END;
    END LOOP;

    RETURN QUERY SELECT inserted_count, conflict_count;
END;
$$ LANGUAGE plpgsql;
```



## 总结

### 核心优化成果
1. **性能提升10-50x**: 通过部分索引和Aho-Corasick算法
2. **ENUM类型安全**: 提供类型安全和数据一致性
3. **JSONB元数据支持**: 支持组件化架构的灵活元数据存储
4. **组件化设计**: PatternLoader、ServiceMatcher、SuffixAssembler等模块
5. **完整实现代码**: 提供8种模式类型的完整代码实现

### 技术创新点
- **部分索引策略**: 只索引活跃数据，减少索引大小60-80%
- **高并发UPSERT**: 使用SKIP LOCKED避免死锁
- **智能冲突消解**: 优先级裁剪算法
- **边界保护机制**: ARN、URL、代码标识符保护
- **模式生成工具**: 完整的工具函数集合和批量处理能力

## 变更记录（Changelog）

- 2025-08-11
  - 新增“文档范围（Scope）与定位”，明确DB指南仅覆盖数据模型/索引/查询/过程/契约
  - “高性能匹配算法实现”章节瘦身，仅保留DB侧要点；应用层实现迁移至 .kiro/steering/pattern-matching-engine-guide.md
  - 在“组件化架构支持 - v2.0 模块化设计”各组件处加入指向新文档的小节链接，形成双向导航
