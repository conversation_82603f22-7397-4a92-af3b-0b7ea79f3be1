-- ====================================================================
-- 外键约束验证和修复SQL脚本
-- 用于验证和修复 feedback_submissions 和 regex_patterns 表的外键约束
-- ====================================================================

-- ====================================================================
-- 1. 外键约束状态检查
-- ====================================================================

-- 检查当前数据库中所有外键约束的状态
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints AS rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = current_schema()
    AND tc.table_name IN ('regex_patterns', 'feedback_submissions')
ORDER BY tc.table_name, tc.constraint_name;

-- ====================================================================
-- 2. 具体表的外键约束检查
-- ====================================================================

-- 检查 regex_patterns 表的外键约束
DO $$
DECLARE
    constraint_exists BOOLEAN;
BEGIN
    -- 检查 fk_related_service 约束是否存在
    SELECT EXISTS(
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_type = 'FOREIGN KEY' 
        AND table_name = 'regex_patterns' 
        AND constraint_name = 'fk_related_service'
    ) INTO constraint_exists;
    
    IF constraint_exists THEN
        RAISE NOTICE 'SUCCESS: regex_patterns.fk_related_service constraint exists';
    ELSE
        RAISE WARNING 'MISSING: regex_patterns.fk_related_service constraint is missing!';
    END IF;
END $$;

-- 检查 feedback_submissions 表的外键约束
DO $$
DECLARE
    constraint_exists BOOLEAN;
BEGIN
    -- 检查 fk_translation_job 约束是否存在
    SELECT EXISTS(
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_type = 'FOREIGN KEY' 
        AND table_name = 'feedback_submissions' 
        AND constraint_name = 'fk_translation_job'
    ) INTO constraint_exists;
    
    IF constraint_exists THEN
        RAISE NOTICE 'SUCCESS: feedback_submissions.fk_translation_job constraint exists';
    ELSE
        RAISE WARNING 'MISSING: feedback_submissions.fk_translation_job constraint is missing!';
    END IF;
END $$;

-- ====================================================================
-- 3. 外键约束修复SQL（如果约束缺失）
-- ====================================================================

-- 修复 regex_patterns 表的外键约束
DO $$
DECLARE
    constraint_exists BOOLEAN;
BEGIN
    -- 检查约束是否存在
    SELECT EXISTS(
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_type = 'FOREIGN KEY' 
        AND table_name = 'regex_patterns' 
        AND constraint_name = 'fk_related_service'
    ) INTO constraint_exists;
    
    IF NOT constraint_exists THEN
        RAISE NOTICE 'REPAIRING: Adding missing fk_related_service constraint to regex_patterns table';
        
        -- 添加外键约束
        ALTER TABLE regex_patterns 
        ADD CONSTRAINT fk_related_service 
        FOREIGN KEY(related_service_id) 
        REFERENCES service_names(id) 
        ON DELETE SET NULL;
        
        RAISE NOTICE 'SUCCESS: fk_related_service constraint added to regex_patterns table';
    ELSE
        RAISE NOTICE 'INFO: regex_patterns.fk_related_service constraint already exists';
    END IF;
END $$;

-- 修复 feedback_submissions 表的外键约束
DO $$
DECLARE
    constraint_exists BOOLEAN;
BEGIN
    -- 检查约束是否存在
    SELECT EXISTS(
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_type = 'FOREIGN KEY' 
        AND table_name = 'feedback_submissions' 
        AND constraint_name = 'fk_translation_job'
    ) INTO constraint_exists;
    
    IF NOT constraint_exists THEN
        RAISE NOTICE 'REPAIRING: Adding missing fk_translation_job constraint to feedback_submissions table';
        
        -- 添加外键约束
        ALTER TABLE feedback_submissions 
        ADD CONSTRAINT fk_translation_job 
        FOREIGN KEY(translation_job_id) 
        REFERENCES translation_jobs(id) 
        ON DELETE SET NULL;
        
        RAISE NOTICE 'SUCCESS: fk_translation_job constraint added to feedback_submissions table';
    ELSE
        RAISE NOTICE 'INFO: feedback_submissions.fk_translation_job constraint already exists';
    END IF;
END $$;

-- ====================================================================
-- 4. 外键约束功能测试
-- ====================================================================

-- 测试 regex_patterns 表的外键约束
DO $$
DECLARE
    test_service_id BIGINT;
BEGIN
    -- 确保有测试数据
    INSERT INTO service_names (authoritative_full_name, base_name, full_name_en, short_name_en) 
    VALUES ('Test Service for FK', 'Test Service for FK', 'Test Service for FK', 'TSFK') 
    ON CONFLICT (authoritative_full_name) DO NOTHING;
    
    -- 获取有效的service_id
    SELECT id INTO test_service_id FROM service_names WHERE authoritative_full_name = 'Test Service for FK';
    
    -- 测试有效外键（应该成功）
    INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id) 
    VALUES ('TEST_FK_VALID', 'SERVICE_NAME', 'test', test_service_id)
    ON CONFLICT (pattern_name) DO NOTHING;
    
    RAISE NOTICE 'SUCCESS: Valid foreign key insert succeeded for regex_patterns';
    
    -- 测试无效外键（应该失败）
    BEGIN
        INSERT INTO regex_patterns (pattern_name, pattern_type, regex_string, related_service_id) 
        VALUES ('TEST_FK_INVALID', 'SERVICE_NAME', 'test', 99999);
        
        RAISE EXCEPTION 'ERROR: Invalid foreign key was accepted in regex_patterns!';
    EXCEPTION
        WHEN foreign_key_violation THEN
            RAISE NOTICE 'SUCCESS: Invalid foreign key correctly rejected for regex_patterns';
        WHEN unique_violation THEN
            RAISE NOTICE 'NOTE: Pattern name conflict, but foreign key constraint is working';
    END;
    
END $$;

-- 测试 feedback_submissions 表的外键约束
DO $$
DECLARE
    test_job_id UUID;
BEGIN
    -- 确保有测试数据
    INSERT INTO translation_jobs (original_text, submitted_by) 
    VALUES ('Test translation for FK', 'test_user')
    RETURNING id INTO test_job_id;
    
    -- 测试有效外键（应该成功）
    INSERT INTO feedback_submissions (translation_job_id, page_url, satisfaction, s3_object_key)
    VALUES (test_job_id, 'https://test.example.com', 'satisfied', 'test/feedback/fk_test.json');
    
    RAISE NOTICE 'SUCCESS: Valid foreign key insert succeeded for feedback_submissions';
    
    -- 测试无效外键（应该失败）
    BEGIN
        INSERT INTO feedback_submissions (translation_job_id, page_url, satisfaction, s3_object_key)
        VALUES ('00000000-0000-0000-0000-000000000000', 'https://test.example.com', 'unsatisfied', 'test/feedback/fk_invalid.json');
        
        RAISE EXCEPTION 'ERROR: Invalid foreign key was accepted in feedback_submissions!';
    EXCEPTION
        WHEN foreign_key_violation THEN
            RAISE NOTICE 'SUCCESS: Invalid foreign key correctly rejected for feedback_submissions';
        WHEN unique_violation THEN
            RAISE NOTICE 'NOTE: S3 object key conflict, but foreign key constraint is working';
    END;
    
END $$;

-- ====================================================================
-- 5. 清理测试数据
-- ====================================================================

-- 清理测试数据
DELETE FROM feedback_submissions WHERE s3_object_key LIKE 'test/feedback/%';
DELETE FROM translation_jobs WHERE original_text = 'Test translation for FK';
DELETE FROM regex_patterns WHERE pattern_name LIKE 'TEST_FK_%';
DELETE FROM service_names WHERE authoritative_full_name = 'Test Service for FK';

-- ====================================================================
-- 6. 最终验证报告
-- ====================================================================

-- 生成最终的外键约束状态报告
SELECT 
    'FINAL REPORT: Foreign Key Constraints Status' AS report_title;

SELECT 
    tc.table_name,
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS references_table,
    ccu.column_name AS references_column,
    CASE 
        WHEN tc.constraint_name IS NOT NULL THEN 'EXISTS'
        ELSE 'MISSING'
    END AS status
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = current_schema()
    AND tc.table_name IN ('regex_patterns', 'feedback_submissions')
ORDER BY tc.table_name, tc.constraint_name;

RAISE NOTICE 'Foreign key verification and repair script completed successfully!';