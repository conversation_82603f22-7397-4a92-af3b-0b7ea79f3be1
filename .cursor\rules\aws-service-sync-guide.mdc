---
alwaysApply: true
---
# AWS服务名称同步模块开发指南 - v2.0 优化版

## 📋 文档导航

### 快速定位指南
- **🏗️ 数据库架构和优化**: 请参考 **[数据库开发指导文档](database-development-guide.md)**
- **📊 8种核心模式类型详解**: 请参考 **[数据库开发指导文档 - regex_patterns表](database-development-guide.md#regex_patterns-8种模式类型)**
- **⚡ 高性能索引和查询优化**: 请参考 **[数据库开发指导文档 - 性能优化要点](database-development-guide.md#性能优化要点)**

---

## 核心原则

**🎯 功能精简原则**: AWS服务名称同步模块的所有子功能开发都应该**只实现功能本身**，不要过度开发增加其他功能，如：
- ❌ 不要添加复杂的监控和性能统计功能
- ❌ 不要添加详细的报告生成和分析功能
- ❌ 不要添加全面的自动化测试框架
- ❌ 不要添加过度的日志记录和指标收集
- ✅ 只实现核心业务功能和基本错误处理
- ✅ 使用简单的日志记录（基本的成功/失败状态）
- ✅ 保持代码简洁和可维护性

## 模块概述

AWS服务名称同步模块是一个独立的数据同步服务，专门负责定期从官方网页和PDF文档中抓取、解析和同步AWS中国区服务名称数据。

**模块定位**: 作为独立的数据同步服务，专注于从官方数据源获取、处理和存储AWS服务名称信息，通过数据库为其他系统提供标准化的数据支持。

**核心功能模块**:
1. **网页爬虫模块** - 从AWS中国区官网抓取服务名称
2. **PDF解析模块** - 解析S3存储的PDF文档
3. **数据处理模块** - 合并和标准化数据
4. **数据库存储模块** - 存储到PostgreSQL数据库
5. **正则表达式模式生成** - 为服务名称生成匹配模式

## 核心架构

### 模块结构
```
aws_service_sync/
├── handler.py              # Lambda入口函数
├── config.py              # 配置管理
├── scrapers/
│   ├── web_scraper.py     # 网页抓取器
│   └── pdf_parser.py      # PDF解析器
├── processors/
│   ├── data_processor.py  # 数据处理器
│   ├── regex_pattern_generator.py    # 正则模式生成器
│   └── service_pattern_sync.py       # 服务模式同步处理器
├── storage/
│   ├── rds_client.py      # RDS数据库客户端
│   └── s3_client.py       # S3操作客户端
└── utils/
    ├── logger.py          # 基础日志工具
    └── retry.py           # 重试机制
```

## 数据获取模块

### Web Scraper模块
- **目标URL**: https://www.amazonaws.cn/en/about-aws/regional-product-services/
- **提取内容**: "提供的服务"字段的值作为权威服务全称
- **基本验证**: 检查数据完整性
- **简单错误处理**: 网络访问失败时记录错误

### PDF Parser模块
- **数据源**: S3存储的AWS官方PDF文档
- **解析内容**: 三列数据（AWS offering, Long name, Short name）
- **数据清理**: 标准化服务名称格式
- **基本验证**: 检查解析结果

## 数据处理模块

### 数据合并和标准化

#### 数据源优先级和冲突解决策略 (v2.0 增强版)

当网页数据和PDF数据存在冲突时，采用以下优先级策略：

```python
def resolve_data_conflicts(web_data, pdf_data):
    """
    解决网页数据和PDF数据之间的冲突

    优先级规则：
    1. authoritative_full_name: 网页数据优先（权威性更高）
    2. short_name_en: PDF数据优先（更准确的缩写）
    3. service_code: PDF数据优先（更标准化）
    4. internal_name: 仅使用PDF数据
    """
    resolved_data = {}

    # 权威全称：网页数据优先
    resolved_data['authoritative_full_name'] = web_data.get('full_name') or pdf_data.get('long_name')

    # 基础名称：基于权威全称生成
    resolved_data['base_name'] = generate_base_name(resolved_data['authoritative_full_name'])

    # 英文简称：PDF数据优先，回退到网页数据
    resolved_data['short_name_en'] = (
        pdf_data.get('short_name') or
        web_data.get('short_name') or
        resolved_data['authoritative_full_name']
    )

    # 服务代码：PDF数据优先，回退到生成
    resolved_data['service_code'] = (
        pdf_data.get('service_code') or
        generate_service_code(pdf_data.get('aws_offering', '')) or
        generate_service_code(resolved_data['authoritative_full_name'])
    )

    # 内部名称：仅来自PDF
    resolved_data['internal_name'] = pdf_data.get('aws_offering')

    # 数据来源标记
    resolved_data['source'] = determine_data_source(web_data, pdf_data)

    # 数据质量评分
    resolved_data['quality_score'] = calculate_data_quality_score(resolved_data, web_data, pdf_data)

    return resolved_data

def determine_data_source(web_data, pdf_data):
    """确定数据来源标记"""
    if web_data and pdf_data:
        return 'merged'
    elif web_data:
        return 'web_scrape'
    elif pdf_data:
        return 'pdf_parse'
    else:
        return 'unknown'

def calculate_data_quality_score(resolved_data, web_data, pdf_data):
    """计算数据质量评分（0-100）"""
    score = 0

    # 基础字段完整性 (40分)
    if resolved_data.get('authoritative_full_name'):
        score += 20
    if resolved_data.get('service_code'):
        score += 20

    # 数据源多样性 (30分)
    if web_data and pdf_data:
        score += 30  # 双重验证
    elif web_data or pdf_data:
        score += 15  # 单一来源

    # 数据一致性 (30分)
    if web_data and pdf_data:
        # 检查名称一致性
        if names_are_consistent(web_data, pdf_data):
            score += 30
        else:
            score += 15  # 部分一致
    else:
        score += 20  # 单一来源默认一致

    return min(score, 100)

def names_are_consistent(web_data, pdf_data):
    """检查网页和PDF数据的名称一致性"""
    web_name = normalize_service_name(web_data.get('full_name', ''))
    pdf_name = normalize_service_name(pdf_data.get('long_name', ''))

    # 使用模糊匹配检查一致性
    similarity = calculate_name_similarity(web_name, pdf_name)
    return similarity > 0.8  # 80%以上相似度认为一致

def generate_base_name(authoritative_full_name):
    """从权威全称生成基础名称（移除括号内容）"""
    if not authoritative_full_name:
        return ''

    # 移除括号及其内容
    import re
    base_name = re.sub(r'\s*\([^)]*\)\s*', ' ', authoritative_full_name)

    # 清理多余空格
    base_name = ' '.join(base_name.split())

    return base_name.strip()
```

#### 增量同步策略 (v2.0 新增功能)

实现智能的增量同步机制，避免不必要的全量同步：

```python
def perform_incremental_sync():
    """执行增量同步"""
    try:
        # 1. 获取上次同步时间戳
        last_sync_time = get_last_sync_timestamp()

        # 2. 检测数据源变更
        web_changes = detect_web_data_changes(last_sync_time)
        pdf_changes = detect_pdf_data_changes(last_sync_time)

        if not web_changes and not pdf_changes:
            logger.info("No changes detected, skipping sync")
            return {'status': 'no_changes', 'processed': 0}

        # 3. 处理变更数据
        changes_processed = 0

        if web_changes:
            changes_processed += process_web_data_changes(web_changes)

        if pdf_changes:
            changes_processed += process_pdf_data_changes(pdf_changes)

        # 4. 更新同步时间戳
        update_last_sync_timestamp()

        logger.info(f"Incremental sync completed: {changes_processed} changes processed")
        return {'status': 'success', 'processed': changes_processed}

    except Exception as e:
        logger.error(f"Incremental sync failed: {e}")
        # 回退到全量同步
        return perform_full_sync()

def detect_web_data_changes(last_sync_time):
    """检测网页数据变更"""
    # 实现网页内容变更检测逻辑
    # 可以使用内容哈希、最后修改时间等方法
    current_web_hash = calculate_web_content_hash()
    stored_web_hash = get_stored_web_content_hash()

    if current_web_hash != stored_web_hash:
        return fetch_current_web_data()

    return None

def detect_pdf_data_changes(last_sync_time):
    """检测PDF数据变更"""
    # 检查S3中PDF文件的最后修改时间
    pdf_last_modified = get_pdf_last_modified_time()

    if pdf_last_modified > last_sync_time:
        return parse_pdf_data()

    return None
```

### 边界保护机制同步支持 (v2.0 统一边界保护)

实现CONTEXT_PROTECTED类型模式的完整同步和维护：

```python
def sync_boundary_protection_system():
    """同步完整的边界保护系统"""

    # 1. 同步基础边界保护模式
    base_protection_patterns = generate_base_protection_patterns()
    sync_protection_patterns(base_protection_patterns)

    # 2. 同步服务特定的边界保护模式
    service_protection_patterns = generate_service_specific_protection_patterns()
    sync_protection_patterns(service_protection_patterns)

    # 3. 验证边界保护模式的有效性
    validate_protection_patterns()

    logger.info("Boundary protection system sync completed")

def generate_base_protection_patterns():
    """生成基础边界保护模式"""
    return [
        {
            'pattern_name': 'CONTEXT_ARN_GENERIC',
            'pattern_type': 'CONTEXT_PROTECTED',
            'regex_string': r'arn:(?:aws|aws-cn):[^\s]+',
            'priority': 90,
            'metadata': {
                'category': 'ARN',
                'scope': 'global',
                'protectionType': 'generic_arn',
                'conflictResolution': 'exclude_matches'
            }
        },
        {
            'pattern_name': 'CONTEXT_URL_GENERIC',
            'pattern_type': 'CONTEXT_PROTECTED',
            'regex_string': r'https?://[^\s]+',
            'priority': 90,
            'metadata': {
                'category': 'URL',
                'scope': 'global',
                'protectionType': 'generic_url',
                'conflictResolution': 'exclude_matches'
            }
        },
        {
            'pattern_name': 'CONTEXT_CODE_IDENTIFIER',
            'pattern_type': 'CONTEXT_PROTECTED',
            'regex_string': r'[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*',
            'priority': 85,
            'metadata': {
                'category': 'CODE',
                'scope': 'programming',
                'protectionType': 'code_identifier',
                'conflictResolution': 'exclude_matches'
            }
        }
    ]

def generate_service_specific_protection_patterns():
    """生成服务特定的边界保护模式"""
    return [
        {
            'pattern_name': 'CONTEXT_ARN_EC2_SPECIFIC',
            'pattern_type': 'CONTEXT_PROTECTED',
            'regex_string': r'arn:(?:aws|aws-cn):ec2:[^:\s]*:[^:\s]*:(?:instance|volume|snapshot)/[^\s]+',
            'priority': 95,
            'metadata': {
                'category': 'ARN',
                'scope': 'ec2_specific',
                'protectionType': 'service_arn',
                'serviceCode': 'ec2'
            }
        },
        {
            'pattern_name': 'CONTEXT_URL_AWS_CONSOLE',
            'pattern_type': 'CONTEXT_PROTECTED',
            'regex_string': r'https?://[^.\s]+\.(?:aws\.amazon\.com|amazonaws\.com\.cn)[^\s]*',
            'priority': 95,
            'metadata': {
                'category': 'URL',
                'scope': 'aws_console',
                'protectionType': 'console_url'
            }
        }
    ]

def validate_protection_patterns():
    """验证边界保护模式的有效性"""
    query = """
    SELECT pattern_name, regex_string
    FROM regex_patterns
    WHERE pattern_type = 'CONTEXT_PROTECTED'
      AND is_active = TRUE;
    """

    patterns = execute_query(query)
    invalid_patterns = []

    for pattern in patterns:
        if not validate_regex_pattern(pattern['regex_string']):
            invalid_patterns.append(pattern['pattern_name'])

    if invalid_patterns:
        logger.warning(f"Invalid protection patterns detected: {invalid_patterns}")
        # 标记无效模式为非活跃状态
        deactivate_invalid_patterns(invalid_patterns)
```

### 标准化字段生成
- **匹配逻辑**: 网页数据与PDF数据的智能匹配和冲突解决
- **字段生成**:
  - `base_name`: 从`authoritative_full_name`移除括号内容
  - `service_code`: 从`internal_name`生成小写代码，支持冲突解决
- **去重处理**: 基于`authoritative_full_name`去重，包含数据质量评分

## 数据库集成 - v2.0 优化版

> 📋 **完整数据库架构**: 表结构、索引优化和性能策略请参考 
> **[数据库开发指导文档 - 核心数据表章节](database-development-guide.md#核心数据表)**

### 模块特定的数据库操作

AWS服务名称同步模块基于系统统一的v2数据库架构，实现以下特定操作：

### UPSERT操作

```python
def upsert_service(service_data):
    """
    使用authoritative_full_name作为业务主键进行UPSERT操作
    
    Args:
        service_data (dict): 服务数据字典，必须包含以下字段：
            - authoritative_full_name: 权威服务全称（业务主键）
            - base_name: 规范化基础名称（用于翻译逻辑）
            - internal_name: PDF内部名称（可选）
            - short_name_en: 英文简称（可选，默认使用authoritative_full_name）
            - service_code: 服务代码（可选）
            - source: 数据来源（可选，默认'web_scrape'）
    
    Returns:
        int: 服务记录的ID
        
    Raises:
        ValueError: 当必需字段缺失时
        psycopg2.IntegrityError: 当数据完整性约束违反时
    """
    
    # 数据验证
    required_fields = ['authoritative_full_name', 'base_name']
    for field in required_fields:
        if not service_data.get(field):
            raise ValueError(f"Required field '{field}' is missing or empty")
    
    # 数据预处理和标准化
    processed_data = {
        'authoritative_full_name': service_data['authoritative_full_name'].strip(),
        'base_name': service_data['base_name'].strip(),
        'internal_name': service_data.get('internal_name', '').strip() or None,
        'short_name_en': service_data.get('short_name_en', service_data['authoritative_full_name']).strip(),
        'service_code': service_data.get('service_code', '').lower().strip() or None,
        'source': service_data.get('source', 'web_scrape')
    }
    
    sql = """
    INSERT INTO service_names 
        (authoritative_full_name, base_name, internal_name, full_name_en, 
         short_name_en, service_code, source, last_synced_at)
    VALUES 
        (%(authoritative_full_name)s, %(base_name)s, %(internal_name)s, 
         %(authoritative_full_name)s, %(short_name_en)s, %(service_code)s, 
         %(source)s, NOW())
    ON CONFLICT (authoritative_full_name) 
    DO UPDATE SET
        base_name = EXCLUDED.base_name,
        internal_name = EXCLUDED.internal_name,
        full_name_en = EXCLUDED.authoritative_full_name,  -- 保持与业务主键一致
        short_name_en = EXCLUDED.short_name_en,
        service_code = EXCLUDED.service_code,
        source = EXCLUDED.source,
        last_synced_at = NOW(),
        updated_at = NOW()
    RETURNING id, authoritative_full_name;
    """
    
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute(sql, processed_data)
                result = cur.fetchone()
                conn.commit()
                
                logger.info(f"Successfully upserted service: {result[1]} (ID: {result[0]})")
                return result[0]  # 返回服务ID
                
    except psycopg2.IntegrityError as e:
        logger.error(f"Integrity error upserting service '{processed_data['authoritative_full_name']}': {e}")
        raise
    except psycopg2.Error as e:
        logger.error(f"Database error upserting service '{processed_data['authoritative_full_name']}': {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error upserting service '{processed_data['authoritative_full_name']}': {e}")
        raise

def batch_upsert_services(services_data_list):
    """
    批量UPSERT服务数据，提高性能 - v2.0 优化版

    Args:
        services_data_list (list): 服务数据列表

    Returns:
        tuple: (成功处理数量, 失败列表)
    """
    if not services_data_list:
        return 0, []

    # 性能优化：使用批量操作和事务管理
    success_count = 0
    failed_services = []
    batch_size = 50  # 优化的批量大小

    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                # 利用部分索引 idx_service_names_active_code 进行优化查询
                for i in range(0, len(services_data_list), batch_size):
                    batch = services_data_list[i:i + batch_size]
                    batch_success, batch_failed = process_service_batch(cur, batch)
                    success_count += batch_success
                    failed_services.extend(batch_failed)

                conn.commit()

    except Exception as e:
        logger.error(f"Batch upsert transaction failed: {e}")
        # 回退到单个处理模式
        return fallback_individual_upsert(services_data_list)

    logger.info(f"Batch upsert completed: {success_count} successful, {len(failed_services)} failed")
    return success_count, failed_services

def process_service_batch(cursor, batch_data):
    """处理单个批次的服务数据"""
    success_count = 0
    failed_services = []

    # 构建批量UPSERT SQL
    sql = """
    INSERT INTO service_names
        (authoritative_full_name, base_name, internal_name, full_name_en,
         short_name_en, service_code, source, last_synced_at)
    VALUES %s
    ON CONFLICT (authoritative_full_name)
    DO UPDATE SET
        base_name = EXCLUDED.base_name,
        internal_name = EXCLUDED.internal_name,
        full_name_en = EXCLUDED.authoritative_full_name,
        short_name_en = EXCLUDED.short_name_en,
        service_code = EXCLUDED.service_code,
        source = EXCLUDED.source,
        last_synced_at = NOW(),
        updated_at = NOW()
    RETURNING id, authoritative_full_name;
    """

    # 准备批量数据
    batch_values = []
    for service_data in batch_data:
        try:
            processed_data = validate_and_process_service_data(service_data)
            batch_values.append((
                processed_data['authoritative_full_name'],
                processed_data['base_name'],
                processed_data['internal_name'],
                processed_data['authoritative_full_name'],  # full_name_en
                processed_data['short_name_en'],
                processed_data['service_code'],
                processed_data['source']
            ))
        except Exception as e:
            failed_services.append({
                'service': service_data.get('authoritative_full_name', 'Unknown'),
                'error': str(e)
            })

    if batch_values:
        try:
            # 使用psycopg2的execute_values进行高性能批量插入
            from psycopg2.extras import execute_values
            execute_values(cursor, sql, batch_values, template=None, page_size=100)
            success_count = len(batch_values)

        except Exception as e:
            logger.error(f"Batch insert failed: {e}")
            # 回退到单个处理
            for i, service_data in enumerate(batch_data[:len(batch_values)]):
                try:
                    upsert_service(service_data)
                    success_count += 1
                except Exception as individual_error:
                    failed_services.append({
                        'service': service_data.get('authoritative_full_name', 'Unknown'),
                        'error': str(individual_error)
                    })

    return success_count, failed_services

def batch_insert_patterns_with_metadata(patterns_data_list):
    """
    批量插入正则表达式模式，优化JSONB metadata处理

    Args:
        patterns_data_list (list): 模式数据列表，包含完整metadata

    Returns:
        tuple: (验证通过数量, 冲突列表)
    """
    if not patterns_data_list:
        return 0, []

    validated_count = 0
    conflicts = []
    batch_size = 100  # JSONB处理的优化批量大小

    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                for i in range(0, len(patterns_data_list), batch_size):
                    batch = patterns_data_list[i:i + batch_size]
                    batch_validated, batch_conflicts = process_patterns_batch(cur, batch)
                    validated_count += batch_validated
                    conflicts.extend(batch_conflicts)

                conn.commit()

                # 更新JSONB GIN索引统计信息
                cur.execute("ANALYZE regex_patterns;")

    except Exception as e:
        logger.error(f"Batch pattern insert failed: {e}")
        raise

    logger.info(f"Batch pattern insert completed: {validated_count} validated, {len(conflicts)} conflicts")
    return validated_count, conflicts

def process_patterns_batch(cursor, batch_data):
    """处理单个批次的模式数据，利用GIN索引优化"""
    validated_count = 0
    conflicts = []

    # 利用GIN索引进行冲突检测
    conflict_check_sql = """
    SELECT pattern_name, regex_string
    FROM regex_patterns
    WHERE pattern_name = ANY(%s)
       OR (metadata @> %s AND is_active = TRUE);
    """

    # 批量UPSERT SQL，优化JSONB处理
    upsert_sql = """
    INSERT INTO regex_patterns
        (pattern_name, pattern_type, regex_string, related_service_id,
         service_code, priority, notes, metadata, validation_status, is_active)
    VALUES %s
    ON CONFLICT (pattern_name)
    DO UPDATE SET
        pattern_type = EXCLUDED.pattern_type,
        regex_string = EXCLUDED.regex_string,
        related_service_id = EXCLUDED.related_service_id,
        service_code = EXCLUDED.service_code,
        priority = EXCLUDED.priority,
        notes = EXCLUDED.notes,
        metadata = EXCLUDED.metadata,
        validation_status = EXCLUDED.validation_status,
        updated_at = NOW()
    RETURNING pattern_name;
    """

    # 准备批量数据和冲突检测
    pattern_names = [p['pattern_name'] for p in batch_data]

    # 检测现有冲突（利用GIN索引）
    cursor.execute(conflict_check_sql, (pattern_names, '{}'))
    existing_patterns = cursor.fetchall()

    if existing_patterns:
        for existing in existing_patterns:
            conflicts.append({
                'pattern_name': existing[0],
                'existing_regex': existing[1],
                'action': 'updated'
            })

    # 准备批量插入数据
    batch_values = []
    for pattern_data in batch_data:
        try:
            # 验证正则表达式
            if not validate_regex_pattern(pattern_data['regex_string']):
                conflicts.append({
                    'pattern_name': pattern_data['pattern_name'],
                    'error': 'Invalid regex pattern',
                    'action': 'skipped'
                })
                continue

            # 确保metadata是有效的JSON
            metadata_json = json.dumps(pattern_data.get('metadata', {}))

            batch_values.append((
                pattern_data['pattern_name'],
                pattern_data['pattern_type'],
                pattern_data['regex_string'],
                pattern_data.get('related_service_id'),
                pattern_data.get('service_code'),
                pattern_data.get('priority', 100),
                pattern_data.get('notes', ''),
                metadata_json,
                pattern_data.get('validation_status', 'valid'),
                pattern_data.get('is_active', True)
            ))

        except Exception as e:
            conflicts.append({
                'pattern_name': pattern_data.get('pattern_name', 'Unknown'),
                'error': str(e),
                'action': 'failed'
            })

    if batch_values:
        try:
            from psycopg2.extras import execute_values
            execute_values(cursor, upsert_sql, batch_values, template=None, page_size=50)
            validated_count = len(batch_values)

        except Exception as e:
            logger.error(f"Pattern batch insert failed: {e}")
            raise

    return validated_count, conflicts

def validate_regex_pattern(pattern_string):
    """验证正则表达式的有效性和Python兼容性"""
    try:
        import re
        re.compile(pattern_string)

        # 检查Python不支持的可变宽度负向后行断言
        if '(?<=' in pattern_string:
            # 简单检测可变宽度模式
            if any(char in pattern_string for char in ['*', '+', '?', '{']):
                # 更详细的检查需要解析正则表达式结构
                return is_fixed_width_lookbehind(pattern_string)

        return True

    except re.error:
        return False

def is_fixed_width_lookbehind(pattern_string):
    """检查负向后行断言是否为固定宽度（简化版检查）"""
    # 这里实现简化的固定宽度检查
    # 实际实现需要更复杂的正则表达式解析
    lookbehind_patterns = re.findall(r'\(\?<=([^)]+)\)', pattern_string)

    for lb_pattern in lookbehind_patterns:
        # 检查是否包含可变宽度量词
        if any(char in lb_pattern for char in ['*', '+', '?', '{']):
            return False

    return True
```

#### 字段职责说明

**核心字段职责分离**：
- **`authoritative_full_name`**: 业务主键，来自AWS官网的权威服务全称
- **`base_name`**: 翻译逻辑用，规范化基础名称（移除括号内容）
- **`full_name_en`**: 首次提及替换值，与 `authoritative_full_name` 保持一致
- **`short_name_en`**: 后续提及替换值，来自PDF或默认使用全称
- **`internal_name`**: PDF原始数据保留，用于追溯
- **`service_code`**: 系统内部标识，用于模式分组管理

#### 优化特性

1. **数据验证**: 确保必需字段完整性
2. **数据标准化**: 自动清理和格式化输入数据
3. **错误处理**: 分类处理不同类型的数据库错误
4. **事务管理**: 确保操作的原子性
5. **返回值**: 提供操作结果反馈
6. **批量处理**: 支持高效的批量操作
7. **日志记录**: 详细的操作日志用于调试


### 正则表达式模式生成

> **⚠️ 重要**: 模式生成策略已完全重构。请参考 **[数据库开发指导文档 - 8种核心模式类型](database-development-guide.md#regex_patterns-8种模式类型)** 获取完整的实现指导。

> 🔧 **完整实现代码**: 详细的模式生成代码实现请参考 **[数据库开发指导文档 - 模式生成实现代码](database-development-guide.md#模式生成实现代码-v20-完整实现)**

#### 模块实现要点

AWS服务名称同步模块负责自动生成8种核心正则表达式模式类型（注意：服务名模式本体不内嵌“可变宽度负向后行”；上下文边界由 BoundaryGuard 结合 `CONTEXT_PROTECTED` 模式统一处理）：

#### 完整的metadata生成算法 (v2.0 增强版)

AWS服务同步模块需要为每个正则表达式模式生成完整的metadata结构，以支持组件化架构的各种需求：

```python
def generate_pattern_metadata(service_data, pattern_type, pattern_info):
    """
    生成完整的pattern metadata结构

    Args:
        service_data (dict): 服务数据
        pattern_type (str): 模式类型 (SERVICE_NAME, CONTEXT_PROTECTED等)
        pattern_info (dict): 模式特定信息

    Returns:
        dict: 完整的metadata结构
    """
    # 基础metadata结构
    metadata = {
        'patternCategory': pattern_info.get('category', 'standard'),
        'generatedBy': 'aws_service_sync_v2',
        'generatedAt': datetime.utcnow().isoformat(),
        'serviceCode': service_data.get('service_code'),
        'hasBoundaryProtection': pattern_info.get('has_boundary', False)
    }

    # 复合后缀模式的特殊处理
    if pattern_info.get('is_compound_suffix'):
        metadata.update({
            'isCompoundWithSuffix': True,
            'suffixGroup': pattern_info.get('suffix_group', 1),
            'suffixType': pattern_info.get('suffix_type', 'instance'),  # instance, volume, etc.
            'assemblyRule': pattern_info.get('assembly_rule', 'standard')
        })

    # 边界保护相关metadata
    if pattern_type == 'CONTEXT_PROTECTED':
        metadata.update({
            'protectionScope': pattern_info.get('scope', 'global'),
            'protectionCategory': pattern_info.get('category', 'generic'),
            'conflictResolution': pattern_info.get('conflict_resolution', 'priority_based')
        })

    # 服务名称模式的特殊metadata
    if pattern_type == 'SERVICE_NAME':
        metadata.update({
            'nameVariant': pattern_info.get('name_variant', 'full'),  # full, short, acronym
            'complexityLevel': pattern_info.get('complexity', 'standard'),  # standard, complex, simple
            'mentionContext': pattern_info.get('mention_context', 'general')  # first, subsequent, general
        })

        # 特殊服务处理标记
        if service_data.get('service_code') in ['health', 'rds', 'aurora']:
            metadata['requiresSpecialHandling'] = True
            metadata['specialHandlingType'] = get_special_handling_type(service_data)

    # 性能优化相关metadata
    metadata.update({
        'expectedFrequency': pattern_info.get('frequency', 'medium'),  # high, medium, low
        'cacheStrategy': pattern_info.get('cache_strategy', 'standard'),
        'indexOptimized': True if pattern_info.get('has_index_support') else False
    })

    return metadata

def get_special_handling_type(service_data):
    """确定特殊服务的处理类型"""
    service_code = service_data.get('service_code', '')

    special_handling_map = {
        'health': 'disambiguation_required',  # Health vs HealthLake/HealthImaging
        'rds': 'engine_specific_variants',    # RDS引擎特定模式
        'aurora': 'mysql_postgresql_variants', # Aurora MySQL/PostgreSQL变体
        'ec2': 'instance_type_suffix',        # EC2实例类型后缀
        'ebs': 'volume_type_suffix'           # EBS卷类型后缀
    }

    return special_handling_map.get(service_code, 'standard')

# 使用新的模式生成系统
from processors.regex_pattern_generator import generate_comprehensive_service_patterns

def sync_patterns_for_services(services_data):
    """同步服务的正则表达式模式 - 增强版"""
    all_patterns = []

    for service_data in services_data:
        # 生成完整的模式集合（8种核心类型 + 特殊变体）
        service_patterns = generate_comprehensive_service_patterns_with_metadata(service_data)
        all_patterns.extend(service_patterns)

    # 批量插入到数据库，包含完整metadata
    validated_count, conflicts = batch_insert_patterns_with_metadata(all_patterns)

    logger.info(f"Generated {len(all_patterns)} patterns with metadata, validated {validated_count}")
    if conflicts:
        logger.warning(f"Detected {len(conflicts)} pattern conflicts")

    return len(all_patterns)

def generate_comprehensive_service_patterns_with_metadata(service_data):
    """为单个服务生成包含完整metadata的模式集合"""
    patterns = []
    service_name = service_data['authoritative_full_name']
    base_name = service_data['base_name']
    service_code = service_data.get('service_code', '')

    # 1. 全名模式（最高优先级）
    full_pattern_info = {
        'category': 'full_name',
        'name_variant': 'full',
        'complexity': 'standard',
        'has_boundary': True,
        'frequency': 'high'
    }
    patterns.append({
        'pattern_name': f'{service_code.upper()}_FULL_STANDARD',
        'pattern_type': 'SERVICE_NAME',
        'regex_string': generate_full_name_regex(service_name),
        'priority': 115,
        'metadata': generate_pattern_metadata(service_data, 'SERVICE_NAME', full_pattern_info)
    })

    # 2. 复合后缀模式（如果适用）
    if service_code in ['ec2', 'ebs', 'rds']:
        suffix_pattern_info = {
            'category': 'full_complex_suffix',
            'name_variant': 'full',
            'complexity': 'complex',
            'is_compound_suffix': True,
            'suffix_group': 1,
            'suffix_type': get_suffix_type(service_code),
            'assembly_rule': 'capture_group_1',
            'has_boundary': True,
            'frequency': 'medium'
        }
        patterns.append({
            'pattern_name': f'{service_code.upper()}_FULL_COMPLEX_SUFFIX',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': generate_complex_suffix_regex(service_name, service_code),
            'priority': 120,
            'metadata': generate_pattern_metadata(service_data, 'SERVICE_NAME', suffix_pattern_info)
        })

    # 3. 短名称模式
    if service_data.get('short_name_en') and service_data['short_name_en'] != service_name:
        short_pattern_info = {
            'category': 'short_name',
            'name_variant': 'short',
            'complexity': 'standard',
            'has_boundary': True,
            'frequency': 'high'
        }
        patterns.append({
            'pattern_name': f'{service_code.upper()}_SHORT_STANDARD',
            'pattern_type': 'SERVICE_NAME',
            'regex_string': generate_short_name_regex(service_data['short_name_en']),
            'priority': 105,
            'metadata': generate_pattern_metadata(service_data, 'SERVICE_NAME', short_pattern_info)
        })

    # 为每个模式添加服务关联信息
    for pattern in patterns:
        pattern.update({
            'related_service_id': service_data.get('id'),
            'service_code': service_code,
            'validation_status': 'valid',
            'is_active': True
        })

    return patterns

def get_suffix_type(service_code):
    """根据服务代码确定后缀类型"""
    suffix_type_map = {
        'ec2': 'instance',
        'ebs': 'volume',
        'rds': 'engine',
        'aurora': 'engine'
    }
    return suffix_type_map.get(service_code, 'generic')
```

> 🔗 **详细的模式类型说明、边界保护机制和特殊服务处理**: 请参考 
> **[数据库开发指导文档 - regex_patterns表详解](database-development-guide.md#regex_patterns-8种模式类型)**

> ⚠️ 分类约束：CLI/配置上下文规则的 `pattern_type` 分别归类为 `CLI_COMMAND` 与 `GENERAL`（或按需 `URL`），避免与 `SERVICE_NAME` 混淆。



## 模式生命周期管理 (v2.0 新增功能)

### 过时模式清理和版本管理

实现智能的模式生命周期管理，确保数据库中的模式数据保持最新和有效：

```python
def manage_pattern_lifecycle():
    """管理正则表达式模式的完整生命周期"""

    # 1. 识别过时模式
    obsolete_patterns = identify_obsolete_patterns()

    # 2. 清理无效模式
    invalid_patterns = identify_invalid_patterns()

    # 3. 更新模式版本
    version_updates = check_pattern_version_updates()

    # 4. 执行清理操作
    cleanup_results = execute_pattern_cleanup(obsolete_patterns, invalid_patterns)

    # 5. 应用版本更新
    update_results = apply_pattern_version_updates(version_updates)

    logger.info(f"Pattern lifecycle management completed: "
                f"cleaned {cleanup_results['cleaned']}, "
                f"updated {update_results['updated']}")

    return {
        'obsolete_cleaned': cleanup_results['cleaned'],
        'invalid_removed': cleanup_results['invalid_removed'],
        'versions_updated': update_results['updated']
    }

def identify_obsolete_patterns():
    """识别过时的正则表达式模式"""
    query = """
    SELECT rp.id, rp.pattern_name, rp.service_code, rp.created_at,
           sn.is_active as service_active, sn.last_synced_at
    FROM regex_patterns rp
    LEFT JOIN service_names sn ON rp.related_service_id = sn.id
    WHERE rp.is_active = TRUE
      AND (
          -- 服务已被标记为非活跃
          sn.is_active = FALSE
          -- 或者模式超过6个月未更新且服务超过3个月未同步
          OR (rp.updated_at < NOW() - INTERVAL '6 months'
              AND sn.last_synced_at < NOW() - INTERVAL '3 months')
          -- 或者模式没有关联的服务（孤儿模式）
          OR sn.id IS NULL
      );
    """

    return execute_query(query)

def identify_invalid_patterns():
    """识别无效的正则表达式模式"""
    query = """
    SELECT id, pattern_name, regex_string, validation_status
    FROM regex_patterns
    WHERE is_active = TRUE
      AND (
          validation_status = 'invalid'
          OR validation_status = 'pending'
          OR regex_string IS NULL
          OR regex_string = ''
      );
    """

    patterns = execute_query(query)
    invalid_patterns = []

    # 进一步验证正则表达式的有效性
    for pattern in patterns:
        if not validate_regex_pattern(pattern['regex_string']):
            invalid_patterns.append(pattern)

    return invalid_patterns

def check_pattern_version_updates():
    """检查需要版本更新的模式"""
    # 获取当前活跃服务的最新信息
    current_services = get_current_active_services()

    version_updates = []

    for service in current_services:
        # 检查该服务的模式是否需要更新
        service_patterns = get_service_patterns(service['id'])

        for pattern in service_patterns:
            # 检查模式是否与最新的服务信息一致
            if pattern_needs_update(pattern, service):
                version_updates.append({
                    'pattern_id': pattern['id'],
                    'pattern_name': pattern['pattern_name'],
                    'service_id': service['id'],
                    'update_type': determine_update_type(pattern, service),
                    'new_metadata': generate_updated_metadata(pattern, service)
                })

    return version_updates

def pattern_needs_update(pattern, service):
    """判断模式是否需要更新"""
    # 检查服务信息变更
    if pattern['service_code'] != service['service_code']:
        return True

    # 检查metadata是否过时
    pattern_metadata = pattern.get('metadata', {})
    if not pattern_metadata.get('generatedAt'):
        return True

    # 检查生成时间是否超过阈值
    from datetime import datetime, timedelta
    generated_at = datetime.fromisoformat(pattern_metadata['generatedAt'].replace('Z', '+00:00'))
    if datetime.utcnow() - generated_at > timedelta(days=90):  # 90天更新周期
        return True

    return False

def execute_pattern_cleanup(obsolete_patterns, invalid_patterns):
    """执行模式清理操作"""
    cleaned_count = 0
    invalid_removed_count = 0

    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                # 清理过时模式（软删除）
                if obsolete_patterns:
                    obsolete_ids = [p['id'] for p in obsolete_patterns]
                    cur.execute("""
                        UPDATE regex_patterns
                        SET is_active = FALSE,
                            notes = COALESCE(notes, '') || ' [OBSOLETE: ' || NOW() || ']',
                            updated_at = NOW()
                        WHERE id = ANY(%s);
                    """, (obsolete_ids,))
                    cleaned_count = cur.rowcount

                # 删除无效模式
                if invalid_patterns:
                    invalid_ids = [p['id'] for p in invalid_patterns]
                    cur.execute("""
                        DELETE FROM regex_patterns
                        WHERE id = ANY(%s);
                    """, (invalid_ids,))
                    invalid_removed_count = cur.rowcount

                conn.commit()

    except Exception as e:
        logger.error(f"Pattern cleanup failed: {e}")
        raise

    return {
        'cleaned': cleaned_count,
        'invalid_removed': invalid_removed_count
    }

def apply_pattern_version_updates(version_updates):
    """应用模式版本更新"""
    updated_count = 0

    if not version_updates:
        return {'updated': 0}

    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                for update in version_updates:
                    # 更新模式的metadata和版本信息
                    cur.execute("""
                        UPDATE regex_patterns
                        SET metadata = %s,
                            notes = COALESCE(notes, '') || ' [UPDATED: ' || NOW() || ']',
                            updated_at = NOW()
                        WHERE id = %s;
                    """, (
                        json.dumps(update['new_metadata']),
                        update['pattern_id']
                    ))

                    if cur.rowcount > 0:
                        updated_count += 1

                conn.commit()

    except Exception as e:
        logger.error(f"Pattern version update failed: {e}")
        raise

    return {'updated': updated_count}

def generate_updated_metadata(pattern, service):
    """为模式生成更新的metadata"""
    current_metadata = pattern.get('metadata', {})

    # 保留现有的重要metadata
    updated_metadata = {
        'patternCategory': current_metadata.get('patternCategory', 'standard'),
        'isCompoundWithSuffix': current_metadata.get('isCompoundWithSuffix', False),
        'suffixGroup': current_metadata.get('suffixGroup'),
        'hasBoundaryProtection': current_metadata.get('hasBoundaryProtection', False)
    }

    # 更新版本和时间信息
    updated_metadata.update({
        'generatedBy': 'aws_service_sync_v2',
        'generatedAt': datetime.utcnow().isoformat(),
        'lastUpdated': datetime.utcnow().isoformat(),
        'serviceCode': service['service_code'],
        'version': current_metadata.get('version', 1) + 1
    })

    # 移除None值
    return {k: v for k, v in updated_metadata.items() if v is not None}
```

### 数据质量保证和验证机制

实现全面的数据质量保证机制：

```python
def comprehensive_data_validation():
    """执行全面的数据质量验证"""

    validation_results = {
        'service_validation': validate_service_data_quality(),
        'pattern_validation': validate_pattern_data_quality(),
        'relationship_validation': validate_data_relationships()
    }

    # 生成数据质量报告
    quality_report = generate_data_quality_report(validation_results)

    # 自动修复可修复的问题
    auto_fix_results = auto_fix_data_issues(validation_results)

    logger.info(f"Data validation completed. Quality score: {quality_report['overall_score']}")

    return {
        'validation_results': validation_results,
        'quality_report': quality_report,
        'auto_fix_results': auto_fix_results
    }

def validate_service_data_quality():
    """验证服务数据质量"""
    issues = []

    # 检查必需字段完整性
    query = """
    SELECT id, authoritative_full_name, base_name, service_code
    FROM service_names
    WHERE is_active = TRUE
      AND (
          authoritative_full_name IS NULL
          OR authoritative_full_name = ''
          OR base_name IS NULL
          OR base_name = ''
      );
    """

    incomplete_services = execute_query(query)
    if incomplete_services:
        issues.append({
            'type': 'incomplete_data',
            'count': len(incomplete_services),
            'details': incomplete_services
        })

    # 检查重复数据
    duplicate_query = """
    SELECT authoritative_full_name, COUNT(*) as count
    FROM service_names
    WHERE is_active = TRUE
    GROUP BY authoritative_full_name
    HAVING COUNT(*) > 1;
    """

    duplicates = execute_query(duplicate_query)
    if duplicates:
        issues.append({
            'type': 'duplicate_services',
            'count': len(duplicates),
            'details': duplicates
        })

    return issues

def validate_pattern_data_quality():
    """验证模式数据质量"""
    issues = []

    # 检查正则表达式有效性
    invalid_patterns = []
    query = """
    SELECT id, pattern_name, regex_string
    FROM regex_patterns
    WHERE is_active = TRUE;
    """

    patterns = execute_query(query)
    for pattern in patterns:
        if not validate_regex_pattern(pattern['regex_string']):
            invalid_patterns.append(pattern)

    if invalid_patterns:
        issues.append({
            'type': 'invalid_regex',
            'count': len(invalid_patterns),
            'details': invalid_patterns
        })

    # 检查孤儿模式（没有关联服务的模式）
    orphan_query = """
    SELECT rp.id, rp.pattern_name
    FROM regex_patterns rp
    LEFT JOIN service_names sn ON rp.related_service_id = sn.id
    WHERE rp.is_active = TRUE
      AND rp.pattern_type = 'SERVICE_NAME'
      AND sn.id IS NULL;
    """

    orphan_patterns = execute_query(orphan_query)
    if orphan_patterns:
        issues.append({
            'type': 'orphan_patterns',
            'count': len(orphan_patterns),
            'details': orphan_patterns
        })

    return issues

def auto_fix_data_issues(validation_results):
    """自动修复可修复的数据问题"""
    fix_results = {}

    # 自动修复孤儿模式
    orphan_patterns = []
    for issue in validation_results.get('pattern_validation', []):
        if issue['type'] == 'orphan_patterns':
            orphan_patterns = issue['details']
            break

    if orphan_patterns:
        fixed_count = fix_orphan_patterns(orphan_patterns)
        fix_results['orphan_patterns_fixed'] = fixed_count

    # 自动标记无效正则表达式
    invalid_patterns = []
    for issue in validation_results.get('pattern_validation', []):
        if issue['type'] == 'invalid_regex':
            invalid_patterns = issue['details']
            break

    if invalid_patterns:
        marked_count = mark_invalid_patterns(invalid_patterns)
        fix_results['invalid_patterns_marked'] = marked_count

    return fix_results
```

## 配置管理

### AWS Secrets Manager集成

```python
class ConfigManager:
    def __init__(self):
        self.secrets_client = boto3.client('secretsmanager', region_name='cn-northwest-1')
        self.secret_arn = os.environ.get('SECRETS_MANAGER_ARN')
        self._config_cache = None
    
    def get_config(self):
        """从Secrets Manager获取配置信息"""
        if self._config_cache is None:
            try:
                response = self.secrets_client.get_secret_value(SecretId=self.secret_arn)
                self._config_cache = json.loads(response['SecretString'])
            except Exception as e:
                logger.error(f"Failed to get config: {e}")
                raise
        return self._config_cache
```

### 配置结构
```json
{
  "host": "数据库主机",
  "port": "5432",
  "username": "数据库用户名",
  "password": "数据库密码",
  "database": "数据库名",
  "snpdf": "s3://bucket/path/to/pdf"
}
```

## 基本错误处理

### 重试机制

```python
@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=2, max=10),
    retry=retry_if_exception_type((ConnectionError, TimeoutError))
)
def fetch_data_with_retry(url):
    """带基本重试机制的数据获取"""
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        return response
    except requests.RequestException as e:
        logger.error(f"Failed to fetch data: {e}")
        raise
```

### 基本日志记录

```python
import logging

logger = logging.getLogger(__name__)

def log_sync_result(success, processed_count, error_message=None):
    """记录同步结果的基本信息"""
    if success:
        logger.info(f"同步成功，处理了 {processed_count} 个服务")
    else:
        logger.error(f"同步失败: {error_message}")
```

## 开发指导原则

### 1. 保持简洁
- 每个模块只实现其核心功能
- 避免添加复杂的统计和分析功能
- 使用简单直接的实现方式

### 2. 基本错误处理
- 实现必要的重试机制
- 记录关键错误信息
- 不要过度设计错误处理逻辑

### 3. 最小化依赖
- 只使用必要的第三方库
- 避免引入复杂的框架
- 保持代码的可维护性

### 4. 数据验证
- 实现基本的数据完整性检查
- 验证必要字段的存在
- 不要过度验证非关键数据

## Lambda函数入口 - v2.0 完整优化版

```python
def lambda_handler(event, context):
    """
    Lambda函数入口 - v2.0优化版
    集成核心功能：增量同步、生命周期管理、数据质量保证
    """
    try:
        logger.info("开始执行AWS服务同步 - v2.0优化版")

        # 1. 初始化配置管理
        config_manager = ConfigManager()
        config = config_manager.get_config()

        # 2. 确定同步策略（增量 vs 全量）
        sync_strategy = determine_sync_strategy(event)
        logger.info(f"使用同步策略: {sync_strategy}")

        # 3. 执行数据同步
        if sync_strategy == 'incremental':
            sync_results = perform_incremental_sync()
        else:
            sync_results = perform_full_sync_with_enhancements()

        # 4. 边界保护系统同步
        boundary_results = sync_boundary_protection_system()

        # 5. 模式生命周期管理
        lifecycle_results = manage_pattern_lifecycle()

        # 6. 数据质量验证
        validation_results = comprehensive_data_validation()

        # 7. 性能统计和结果汇总
        result_summary = {
            'sync_strategy': sync_strategy,
            'sync_results': sync_results,
            'boundary_protection': boundary_results,
            'lifecycle_management': lifecycle_results,
            'data_quality': {
                'overall_score': validation_results['quality_report']['overall_score'],
                'issues_found': sum(len(issues) for issues in validation_results['validation_results'].values()),
                'auto_fixed': validation_results['auto_fix_results']
            },
            'database_version': 'v2.0',
            'features_enabled': [
                'incremental_sync', 'lifecycle_management', 'data_quality_assurance',
                'boundary_protection', 'metadata_generation',
                'performance_optimization', 'conflict_resolution'
            ],
            'execution_time': time.time() - start_time
        }

        logger.info(f"同步完成 - {result_summary}")

        # 8. 发送通知给依赖系统
        notify_dependent_systems(result_summary)

        return {
            'statusCode': 200,
            'body': json.dumps({
                'success': True,
                'version': '2.0',
                'summary': result_summary
            })
        }

    except Exception as e:
        logger.error(f"同步失败: {e}")

        # 错误恢复机制
        try:
            error_recovery_results = perform_error_recovery(e)
            logger.info(f"错误恢复完成: {error_recovery_results}")
        except Exception as recovery_error:
            logger.error(f"错误恢复失败: {recovery_error}")

        return {
            'statusCode': 500,
            'body': json.dumps({
                'success': False,
                'error': str(e),
                'version': '2.0',
                'recovery_attempted': True
            })
        }

def determine_sync_strategy(event):
    """确定同步策略"""
    # 检查事件参数
    if event.get('force_full_sync'):
        return 'full'

    # 检查上次同步时间
    last_sync = get_last_sync_timestamp()
    if not last_sync:
        return 'full'  # 首次同步

    # 检查时间间隔
    time_since_last_sync = datetime.utcnow() - last_sync
    if time_since_last_sync > timedelta(days=7):
        return 'full'  # 超过一周执行全量同步

    return 'incremental'

def perform_full_sync_with_enhancements():
    """执行增强的全量同步"""
    start_time = time.time()

    # 1. 数据抓取模块
    web_scraper = WebScraper()
    pdf_parser = PDFParser(config)

    web_services = web_scraper.scrape_services()
    pdf_services = pdf_parser.parse_pdf()

    logger.info(f"数据抓取完成: 网页 {len(web_services)} 个服务, PDF {len(pdf_services)} 个服务")

    # 2. 数据处理和合并（使用冲突解决策略）
    data_processor = DataProcessor()
    merged_services = data_processor.process_services_with_conflict_resolution(web_services, pdf_services)

    # 3. 批量数据库存储（使用性能优化）
    stored_count, failed_services = batch_upsert_services(merged_services)

    # 4. 正则表达式模式生成（包含完整metadata）
    all_patterns = []
    for service_data in merged_services:
        service_patterns = generate_comprehensive_service_patterns_with_metadata(service_data)
        all_patterns.extend(service_patterns)

    # 5. 批量模式插入（利用索引优化）
    validated_count, conflicts = batch_insert_patterns_with_metadata(all_patterns)

    execution_time = time.time() - start_time

    return {
        'type': 'full_sync',
        'services_processed': stored_count,
        'services_failed': len(failed_services),
        'patterns_generated': len(all_patterns),
        'patterns_validated': validated_count,
        'conflicts_detected': len(conflicts),
        'execution_time': execution_time
    }



def notify_dependent_systems(result_summary):
    """通知依赖系统数据已更新"""
    try:
        # 发送SNS通知给模式匹配引擎
        notification_message = {
            'event_type': 'aws_service_sync_completed',
            'timestamp': datetime.utcnow().isoformat(),
            'summary': result_summary,
            'data_version': generate_data_version_hash()
        }

        # 这里可以集成SNS、SQS或其他通知机制
        logger.info(f"Notification sent to dependent systems: {notification_message}")

    except Exception as e:
        logger.warning(f"Failed to notify dependent systems: {e}")

def perform_error_recovery(error):
    """执行错误恢复"""
    recovery_results = {}

    try:
        # 1. 检查数据库连接
        if "database" in str(error).lower():
            recovery_results['database_check'] = check_database_connectivity()

        # 2. 检查外部数据源
        if "network" in str(error).lower() or "timeout" in str(error).lower():
            recovery_results['network_check'] = check_external_data_sources()

        # 3. 清理部分失败的数据
        recovery_results['cleanup'] = cleanup_partial_failures()

    except Exception as recovery_error:
        recovery_results['recovery_error'] = str(recovery_error)

    return recovery_results
```



def get_active_patterns_from_database():
    """从数据库获取活跃的模式数据"""
    query = """
    SELECT pattern_name, regex_string, metadata, priority, service_code
    FROM regex_patterns 
    WHERE is_active = TRUE AND validation_status = 'valid'
    ORDER BY priority DESC, id ASC;
    """
    
    with get_db_connection() as conn:
        with conn.cursor() as cur:
            cur.execute(query)
            return cur.fetchall()
```

## 📖 相关文档导航

### 系统级文档
- **[数据库开发指导文档](database-development-guide.md)** - 完整的数据库架构、优化策略和性能优化

### 快速查找索引
| 需要查找的内容 | 参考文档章节 |
|---------------|-------------|
| 完整数据库表结构 | [数据库开发指导文档 - 核心数据表](database-development-guide.md#核心数据表) |
| 8种正则模式类型详解 | [数据库开发指导文档 - regex_patterns表](database-development-guide.md#regex_patterns-8种模式类型) |
| 高性能索引策略 | [数据库开发指导文档 - 性能优化要点](database-development-guide.md#性能优化要点) |
| 边界保护机制 | [数据库开发指导文档 - 边界保护机制](database-development-guide.md#边界保护机制) |
| JSONB字段设计 | [数据库开发指导文档 - JSONB高性能索引](database-development-guide.md#jsonb-高性能索引) |

## 总结 - v2.0 完整功能版

AWS服务同步模块已发展为一个功能完整的企业级数据同步系统，实现了以下核心功能：

### 🎯 核心功能模块
1. **智能数据抓取** - 从网页和PDF获取服务名称，支持变更检测
2. **高级数据处理** - 合并、标准化、去重和冲突解决
3. **优化数据存储** - 批量操作、事务管理和性能优化
4. **完整模式生成** - 生成8种核心正则表达式模式类型，包含完整metadata
5. **边界保护系统** - 维护CONTEXT_PROTECTED类型的边界保护模式
6. **生命周期管理** - 过时模式清理、版本管理和自动更新
7. **数据质量保证** - 全面验证、自动修复和质量评分

### 🚀 v2.0 增强特性
- **增量同步策略** - 智能变更检测，避免不必要的全量同步
- **冲突解决机制** - 数据源优先级和智能冲突解决算法
- **性能优化** - 利用部分索引、JSONB GIN索引和批量操作
- **metadata生成** - 完整的JSONB metadata结构，支持灵活的模式管理
- **错误恢复** - 自动错误检测、恢复和降级策略
- **系统通知** - 依赖系统的数据更新通知机制

### 🏗️ 架构集成
- **数据库优化集成** - 充分利用v2.0数据库架构的性能特性
- **边界保护集成** - 统一的边界保护原则和CONTEXT_PROTECTED模式管理
- **数据库中介模式** - 通过数据库为其他系统提供标准化的数据访问

### 📊 质量保证
- **数据验证** - 多层次的数据完整性和有效性验证
- **自动修复** - 常见数据问题的自动检测和修复
- **版本管理** - 模式版本控制和生命周期管理

### 🔧 运维友好
- **配置管理** - AWS Secrets Manager集成和安全配置
- **日志记录** - 结构化日志和操作审计
- **错误处理** - 分层错误处理和恢复机制
- **通知系统** - 依赖系统的实时数据更新通知

> 💡 **开发提示**:
> - 本模块的所有数据库相关的详细设计和优化策略都已统一到 **[数据库开发指导文档](database-development-guide.md)** 中
> - 本模块是完全独立的数据同步服务，不提供任何对外接口，其他系统应直接从数据库获取所需数据
> - 遵循数据库中介模式，通过数据库实现模块间的解耦，确保系统的独立性和可维护性

### 🎯 下一步发展方向
1. **机器学习集成** - 智能模式优化和质量预测
2. **多区域支持** - 跨区域数据同步和一致性保证
3. **实时流处理** - 基于事件的实时数据更新
4. **高级分析** - 数据使用模式分析和优化建议

AWS服务同步模块现已成为一个功能完整、性能优化、质量保证的独立数据同步服务，专注于AWS服务名称数据的获取、处理和存储，通过数据库为其他系统提供可靠的数据支持。