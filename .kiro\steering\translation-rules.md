---
inclusion: fileMatch
fileMatchPattern: '*translation*|*mass*email*|*翻译*'
---

# AWS China Translation Rules and Standards

## Core Translation Principles

### Multi-Stage Processing Pipeline
The translation system follows a strict 4-stage pipeline:
1. **Stage 0**: Text line type analysis and routing
2. **Stage 1**: JavaScript term standardization and placeholder generation  
3. **Stage 2**: LLM translation of non-placeholder content
4. **Stage 3**: Placeholder restoration and final Chinese formatting

### Placeholder System Rules
- **NEVER** modify any `__XXX_YYY__` format placeholders
- Placeholders protect: CLI commands, ARNs, JSON blocks, service names, timestamps, URLs
- LLM must preserve placeholders exactly while translating surrounding text
- JavaScript handles placeholder generation and restoration

## English Text Standardization (Stage 1)

### Basic Replacements
- `AWS` → `Amazon` (when not part of service names)
- `AWS Support` → `Amazon Web Services Support`
- `AWS Account` → `Amazon Web Services Account`
- `AWS Management Console` → `Amazon Web Services Console`

### Service Name Handling
- First mention: Use full name `Amazon Elastic Compute Cloud (EC2)`
- Subsequent mentions: Use short name `Amazon EC2`
- Track mentions globally for general content, per-line for Wording content
- Complex patterns: `RDS for PostgreSQL` → `Amazon RDS for PostgreSQL`

### Time Zone Conversions (Precise Calculations)
- **PDT (UTC-7)**: Original time + 15 hours → UTC+8
- **PST (UTC-8)**: Original time + 16 hours → UTC+8  
- **EDT (UTC-4)**: Original time + 12 hours → UTC+8
- **EST (UTC-5)**: Original time + 13 hours → UTC+8
- **UTC/GMT**: Original time + 8 hours → UTC+8

### CLI Command Processing
- Standardize `--region` parameters for China regions:
  - Ningxia context → `cn-northwest-1`
  - Beijing context → `cn-north-1`
  - Default → `cn-north-1`
- Protect entire CLI commands with `__CLICMD_X__` placeholders

### URL Localization
- `docs.aws.amazon.com` → `docs.amazonaws.cn`
- `console.aws.amazon.com` → `console.amazonaws.com.cn`
- `aws.amazon.com/support` → `console.amazonaws.cn/support/`
- Standardize region parameters in console URLs

## Chinese Translation Standards (Stage 2)

### Brand Term Translations
- `Amazon` → `亚马逊云科技` (when standalone)
- `Amazon Web Services Support` → `亚马逊云科技中国支持团队`
- `Amazon Web Services Console` → `亚马逊云科技控制台`
- `Amazon Web Services Account` → `亚马逊云科技账户`
- `Amazon Web Services` → `亚马逊云科技`

### Regional Expressions
- Avoid: "all the AWS regions", "all commercial regions"
- Use: `亚马逊云科技中国区域`
- Specify: `北京区域 (BJS)` or `宁夏区域 (ZHY)`
- Distinguish operators: 光环新网 vs 西云数据

### Technical Term Translations
- `Affected resources` → `受影响的资源`
- `Fast Launch` → `快速启动`
- `Service Quotas` → `服务配额`
- Common acronyms preserved in `__ACRNYM_X__` placeholders

### Sentence Restructuring
Standard ending: "如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队"

## Final Formatting (Stage 3)

### Time Format Output
- Full datetime: `YYYY年M月D日 上午/下午 h:mm 北京时间`
- Date only: `YYYY年M月D日` (no time component)
- Use Chinese time indicators: 上午/下午

### Punctuation Standards
- Use Chinese punctuation: `，。？！：；`
- Bracket handling: `[...]` → `（...）` (avoid `【】`)
- Preserve technical formatting in restored placeholders

### Quality Checks
- Verify all service names follow first/subsequent rules
- Ensure region expressions use China-specific terms
- Validate link accessibility in China region
- Check time zone conversion accuracy
- Confirm brand term consistency

## Content Type Handling

### Metadata Lines
- Format: `Service:`, `Region:`, `Failure mode X:`, `TypeCode:`
- Keys become `__METAKEY_XXX__` placeholders
- Values may be standardized and placeholder-protected

### Wording Lines  
- Format: `Wording:` or `First Post Wording:`
- Use line-scope service name tracking
- Key becomes placeholder, content gets full processing

### General Content
- All other translatable content
- Use global-scope service name tracking
- Full multi-stage processing pipeline

## Error Prevention

### Critical Don'ts
- Never modify placeholder format or content
- Never skip translation of English natural language
- Never add metadata lines if input lacks them
- Never use unsupported time zones
- Never reference inaccessible links/resources

### Validation Points
- Placeholder preservation integrity
- Service name tracking accuracy  
- Time calculation precision
- Link localization completeness
- Brand term application consistency