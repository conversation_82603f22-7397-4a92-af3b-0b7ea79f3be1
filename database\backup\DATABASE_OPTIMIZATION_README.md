# 数据库优化升级指南

## 概述

本文档描述了基于 `Amazon_service_name_matching_solutio.md` 优化建议对批量邮件系统数据库架构的升级过程。

## 优化内容

### 1. 表结构增强

#### 1.1 `regex_patterns` 表优化

**变更项目：**
- ✅ **pattern_type 字段**：从 `VARCHAR(50)` 改回 `ENUM` 类型
  - 提供类型安全和数据一致性
  - 防止因拼写错误导致规则失效
  - 新增 `CONTEXT_PROTECTED` 枚举值

- ✅ **metadata 字段**：新增 `JSONB` 类型字段
  - 存储结构化元数据，如 `{"isCompoundWithSuffix": true, "suffixGroup": 1}`
  - 支持 `SuffixAssembler` 组件的需求
  - 提供灵活的扩展能力

- ✅ **regex_string 约束**：添加长度检查约束
  ```sql
  CONSTRAINT chk_regex_string_length CHECK (length(regex_string) < 10000)
  ```

### 2. 索引优化

#### 2.1 部分索引（Partial Index）

**关键性能提升：**

```sql
-- 只索引活跃的服务
CREATE INDEX idx_service_names_active_code 
ON service_names(service_code) WHERE is_active = TRUE;

-- 只索引活跃且有效的模式（核心优化）
CREATE INDEX idx_regex_patterns_active_valid 
ON regex_patterns(priority DESC, id ASC) 
WHERE is_active = TRUE AND validation_status = 'valid';
```

**性能收益：**
- 索引大小减少 60-80%
- 查询速度提升数个数量级
- 直接服务于 `PatternLoader` 组件

#### 2.2 JSONB 索引

```sql
-- 为 metadata 字段创建 GIN 索引
CREATE INDEX idx_regex_patterns_metadata 
ON regex_patterns USING GIN (metadata);
```

### 3. 可扩展性优化

#### 3.1 表分区支持

**translation_jobs 表分区：**
- 按 `submitted_at` 时间范围分区
- 解决历史数据膨胀问题
- 保持查询性能

```sql
CREATE TABLE translation_jobs (
    -- ... 字段定义
) PARTITION BY RANGE (submitted_at);
```

## 文件说明

### 核心文件

| 文件名 | 用途 | 说明 |
|--------|------|------|
| `mass_email_database_schema_v2.sql` | 完整的优化后架构 | 新建数据库使用 |
| `migration_v2_optimization_fixed.sql` | 现有数据库迁移脚本（修复版） | **推荐使用** - 升级现有数据库 |
| `migration_v2_optimization.sql` | 原迁移脚本 | 已修复，保留作参考 |
| `verify_migration.sql` | 迁移验证脚本 | 验证升级结果 |
| `test_migration_fix.sql` | 迁移修复测试脚本 | 测试迁移逻辑 |
| `mass_email_database_schema_v2_backup.sql` | 原架构备份 | 回滚参考 |

### 备份文件

- `mass_email_database_schema_v2_backup.sql` - 升级前的完整备份

## 使用指南

### 新建数据库

```bash
# 直接使用优化后的架构
psql -d your_database -f database/mass_email_database_schema_v2.sql
```

### 升级现有数据库

```bash
# 1. 备份现有数据库
pg_dump your_database > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 执行迁移脚本（推荐使用修复版）
psql -d your_database -f database/migration_v2_optimization_fixed.sql

# 3. 验证迁移结果
psql -d your_database -f database/verify_migration.sql

# 4. 可选：测试迁移逻辑
psql -d your_database -f database/test_migration_fix.sql
```

## 迁移安全措施

### 1. 事务保护
- 整个迁移过程在单个事务中执行
- 任何错误都会自动回滚
- 确保数据一致性

### 2. 数据备份
- 迁移前自动创建临时备份表
- 保留原始数据以供验证

### 3. 渐进式验证
- 每个步骤都有验证检查
- 失败时提供详细错误信息

## 性能预期

### 查询性能提升

| 查询类型 | 优化前 | 优化后 | 提升幅度 |
|----------|--------|--------|----------|
| 活跃服务查询 | 全表扫描 | 部分索引 | 5-10x |
| 模式加载查询 | 全表扫描 | 部分索引 | 10-50x |
| metadata 查询 | 不支持 | GIN 索引 | N/A |

### 存储优化

- 索引大小减少 60-80%
- 查询缓存命中率提升
- 内存使用更高效

## 兼容性说明

### 向后兼容
- ✅ 保留所有现有字段
- ✅ 保持现有数据完整性
- ✅ 应用程序无需修改

### 新功能支持
- ✅ 支持 `SuffixAssembler` 组件
- ✅ 支持高性能模式匹配
- ✅ 支持元数据查询

## 监控建议

### 1. 性能监控
```sql
-- 监控部分索引使用情况
SELECT 
    schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE indexname LIKE '%_active_%';
```

### 2. 数据质量监控
```sql
-- 监控 metadata 字段完整性
SELECT 
    COUNT(*) as total,
    COUNT(metadata) as with_metadata,
    COUNT(metadata) * 100.0 / COUNT(*) as completion_rate
FROM regex_patterns;
```

## 迁移脚本修复说明

### 修复的问题

**原问题：** 在原始的 `migration_v2_optimization.sql` 中，存在字段添加和数据迁移的顺序问题：
- 尝试在 `metadata` 字段添加之前就使用它进行数据迁移
- 导致 SQL 错误：`ERROR: column "metadata" does not exist`

**修复方案：** 在 `migration_v2_optimization_fixed.sql` 中：
1. ✅ **重新排序操作步骤**：先添加字段，再进行数据迁移
2. ✅ **增加条件检查**：使用 `DO` 块检查字段是否存在后再操作
3. ✅ **增强错误处理**：每个步骤都有详细的状态检查和错误提示
4. ✅ **改进验证逻辑**：更全面的数据完整性验证

### 修复版本的优势

- **更安全**：每个操作都有前置条件检查
- **更详细**：提供步骤级别的进度反馈
- **更健壮**：能够处理各种边界情况
- **更易调试**：详细的错误信息和状态报告

## 故障排除

### 常见问题

1. **ENUM 类型转换失败**
   - 检查现有数据中是否有无效的 pattern_type 值
   - 迁移脚本会自动将无效值转换为 'GENERAL'

2. **索引创建失败**
   - 检查磁盘空间是否充足
   - 确认没有长时间运行的事务阻塞

3. **分区表转换**
   - 现有数据的分区转换需要在维护窗口期间进行
   - 建议先在测试环境验证

4. **metadata 字段迁移问题**
   - 使用修复版迁移脚本：`migration_v2_optimization_fixed.sql`
   - 如果仍有问题，检查表结构是否完整

### 回滚方案

如果需要回滚到优化前的状态：

```bash
# 1. 恢复备份
psql -d your_database < backup_file.sql

# 2. 或者使用备份架构重建
psql -d your_database -f database/mass_email_database_schema_v2_backup.sql
```

## 后续优化建议

1. **定期维护**
   - 每月更新表统计信息：`ANALYZE`
   - 监控索引使用情况
   - 清理过期分区数据

2. **进一步优化**
   - 考虑实施连接池优化
   - 评估读写分离的可能性
   - 监控并优化慢查询

3. **应用层集成**
   - 更新应用程序以利用新的 metadata 字段
   - 实施基于部分索引的查询优化
   - 集成性能监控

## 联系信息

如有问题或需要支持，请参考：
- 技术文档：`Amazon_service_name_matching_solutio.md`
- 系统上下文：`.kiro/steering/mass-email-system-context.md`