import json
import boto3
import uuid
import os  # <--- 修正：添加缺失的模块导入
from datetime import datetime

# 从环境变量中获取 S3 存储桶名称
S3_BUCKET_NAME = os.environ['S3_BUCKET_NAME']
# 生产环境中建议从环境变量读取允许的前端域名
ALLOWED_ORIGIN = os.environ.get('ALLOWED_ORIGIN', '*') # 默认为 '*'，但建议在Lambda配置中设置特定域名

s3_client = boto3.client('s3', config=boto3.session.Config(signature_version='s3v4'))

def lambda_handler(event, context):
    """
    为客户端生成一个用于上传反馈文件的 S3 预签名 URL。
    """
    try:
        # 1. 生成一个唯一的对象键 (文件名)
        current_time = datetime.now()
        object_key = (
            f"feedback/{current_time.strftime('%Y/%m/%d')}/"
            f"{current_time.strftime('%H-%M-%S')}-{uuid.uuid4().hex[:12]}.json"
        )

        # 2. 定义预签名 URL 的参数
        presigned_params = {
            'Bucket': S3_BUCKET_NAME,
            'Key': object_key,
            'ContentType': 'application/json' 
        }

        # 3. 生成预签名 URL，有效期为 5 分钟 (300 秒)
        upload_url = s3_client.generate_presigned_url(
            'put_object',
            Params=presigned_params,
            ExpiresIn=300 
        )

        # 4. 将 URL 和对象键返回给客户端
        response_body = {
            'uploadURL': upload_url,
            'objectKey': object_key
        }

        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': ALLOWED_ORIGIN # <--- 改进：使用配置化的CORS头
            },
            'body': json.dumps(response_body)
        }

    except Exception as e:
        print(f"Error generating presigned URL: {e}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': ALLOWED_ORIGIN # <--- 改进：错误响应也应包含CORS头
            },
            'body': json.dumps({'error': 'Could not generate upload URL.'})
        } 