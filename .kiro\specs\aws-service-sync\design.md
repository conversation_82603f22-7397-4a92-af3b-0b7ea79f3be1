# AWS服务名称同步模块设计文档 - v2.0

## 文档信息

- **版本**: 2.0
- **创建日期**: 2025-01-13
- **最后更新**: 2025-01-13
- **状态**: 设计阶段

## 目录

1. [系统概述](#1-系统概述)
2. [架构设计](#2-架构设计)
3. [核心组件设计](#3-核心组件设计)
4. [数据库集成设计](#4-数据库集成设计)
5. [API接口设计](#5-api接口设计)
6. [错误处理和监控设计](#6-错误处理和监控设计)
7. [部署和配置设计](#7-部署和配置设计)
8. [性能优化策略](#8-性能优化策略)
9. [测试策略](#9-测试策略)
10. [运维和维护](#10-运维和维护)

---

## 1. 系统概述

### 1.1 模块定位

AWS服务名称同步模块是一个**完全独立的数据同步服务**，专门负责定期从官方网页和PDF文档中获取、整理和同步AWS中国区的服务名称信息。该模块实现"逻辑外化"设计理念，将硬编码的服务名称列表转换为数据库驱动的动态管理系统。

**核心特性**：
- **完全独立性**: 不依赖其他模块，支持独立部署、运行和维护
- **数据库中介模式**: 通过数据库为其他系统提供标准化的数据支持，实现模块间完全解耦
- **无对外接口**: 不提供任何API接口，确保模块的完全独立性
- **单一职责**: 专注于数据同步核心功能，遵循功能精简原则

## 概述

AWS服务同步系统是一个独立的数据同步服务，旨在定期从官方网页和PDF文档中获取、整理和同步AWS中国区的服务名称信息。系统遵循“逻辑外化”设计理念，将硬编码的服务名称列表转换为数据库驱动的动态管理系统，不提供任何对外服务接口，并通过数据库为其他系统提供标准化的数据支持，确保各系统获取的服务名称数据始终保持最新和准确。

### 核心设计原则

**🎯 功能精简原则**: 系统遵循功能精简原则，只实现核心业务功能和基本错误处理，保持代码简洁和可维护性。

**🔄 逻辑外化**: 将业务规则（服务名、品牌词、URL映射、正则表达式）存储在数据库中，支持动态更新而无需重新部署代码。

**📊 过程可追溯**: 记录完整的同步生命周期，支持调试和质量审计。

**⚡ 高性能架构**: 采用基于Aho-Corasick + 分段正则的O(n)复杂度匹配引擎，实现10-50x性能提升。

**🧩 组件化设计**: 实现模块化组件架构，支持独立测试、维护和扩展。

### v2.0 优化版特性

1. **完全独立的同步服务**: 不提供任何对外接口，专注于数据获取、处理、存储与模式生成
2. **数据库中介模式**: 其他系统直接从数据库读取活跃且有效的数据，形成松耦合架构
3. **JSONB元数据支持**: 使用metadata JSONB存储结构化元数据（isCompoundWithSuffix、suffixGroup、patternCategory、hasBoundaryProtection等）
4. **索引优化**: 利用部分索引与GIN索引（idx_regex_patterns_active_valid、idx_regex_patterns_metadata）实现10–50x查询性能提升
5. **8种核心正则表达式模式类型**: 覆盖所有使用场景的完整模式体系（90–125优先级）
6. **统一边界保护机制**: 使用CONTEXT_PROTECTED基线模式 + 轻量固定宽度保护
7. **校验与生命周期**: validation_status（pending/valid/invalid）、regex_string长度约束（<10000）、is_active与版本/清理策略
8. **特殊服务处理策略**: 为Aurora、Health、RDS等特殊服务提供专门处理

### 系统目标

1. **自动化数据同步**: 从AWS中国区官方网页和PDF文档自动抓取服务名称数据
2. **智能数据处理**: 实现多数据源的智能匹配和标准化处理
3. **动态模式生成**: 基于8种核心模式类型自动生成正则表达式匹配模式
4. **数据质量保证**: 提供完整的数据验证和质量检查机制
5. **运维友好**: 提供结构化日志和CloudWatch监控支持
6. **高性能匹配**: 实现O(n)复杂度的文本匹配算法
7. **组件化架构**: 支持模块化开发、测试和维护

## 架构

### 系统架构图

```mermaid
graph TB
    A[EventBridge Scheduler] --> B[Lambda Function]
    B --> C[ConfigManager]
    C --> D[AWS Secrets Manager]
    
    B --> E[WebScraper]
    B --> F[PDFParser]
    B --> G[DataProcessor]
    B --> H[RegexPatternGenerator]
    
    E --> I[AWS China Website<br/>amazonaws.cn]
    F --> J[S3 PDF Files]
    
    G --> K[RDS PostgreSQL<br/>v2 Architecture]
    H --> K
    
    B --> L[CloudWatch Logs]
    B --> M[SNS Notifications]
    
    N[Mass Email System] --> K
    
    subgraph "Lambda Function Components"
        E
        F
        G
        H
    end
    
    subgraph "Data Storage"
        J
        K
    end
    
    subgraph "Monitoring & Config"
        D
        L
        M
    end
```

### 核心架构组件

#### 1. 调度层 (Scheduling Layer)
- **EventBridge Scheduler**: 定期触发Lambda函数执行
- **支持灵活调度**: cron表达式配置，支持手动和定时触发
- **执行历史**: 提供执行历史和监控能力

#### 2. 计算层 (Compute Layer)
- **Lambda函数**: 无服务器计算，自动扩缩容
- **模块化设计**: 清晰的模块分离和接口定义
- **错误处理**: 完整的错误处理和重试机制

#### 3. 数据获取层 (Data Acquisition Layer)
- **Web Scraper**: 从AWS中国区官网抓取服务名称
- **PDF Parser**: 解析S3存储的官方PDF文档
- **智能匹配**: 多数据源的智能匹配算法

#### 4. 数据处理层 (Data Processing Layer)
- **数据标准化**: 服务名称的规范化处理
- **去重算法**: 基于业务主键的去重处理
- **模式生成**: 8种核心正则表达式模式自动生成

#### 5. 存储层 (Storage Layer)
- **RDS PostgreSQL**: v2架构数据库，支持字段职责分离
- **S3**: 原始PDF文件存储
- **AWS Secrets Manager**: 敏感配置信息安全存储

#### 6. 监控层 (Monitoring Layer)
- **CloudWatch Logs**: 结构化日志记录
- **CloudWatch Metrics**: 性能指标监控（仅提供部署指南，不在代码中实现）
- **SNS**: 告警通知机制（仅提供部署指南，不在代码中实现）

## 组件和接口

### Lambda函数模块结构

```
aws_service_sync/
├── handler.py              # Lambda入口函数
├── config.py              # 配置管理
├── scrapers/
│   ├── web_scraper.py     # 网页抓取器
│   └── pdf_parser.py      # PDF解析器
├── processors/
│   ├── data_processor.py  # 数据处理器
│   ├── regex_pattern_generator.py    # 正则模式生成器
│   └── service_pattern_sync.py       # 服务模式同步处理器
├── storage/
│   ├── rds_client.py      # RDS数据库客户端
│   └── s3_client.py       # S3操作客户端

└── utils/
    ├── logger.py          # 基础日志工具
    └── retry.py           # 重试机制
```

---

## 3. 核心组件设计

### 3.1 数据获取组件

#### 3.1.1 Web Scraper (网页抓取器)

**功能职责**：
- 从AWS中国区官网抓取权威服务名称
- 提取"提供的服务"字段值作为authoritative_full_name
- 实施基本的数据验证和完整性检查

**核心接口**：
```python
class WebScraper:
    """AWS中国区官网服务名称抓取器"""

    def __init__(self):
        self.target_url = "https://www.amazonaws.cn/about-aws/regional-product-services/"
        self.session = requests.Session()
        self.user_agents = self._load_user_agents()

    def scrape_services(self) -> List[str]:
        """抓取官网服务名称列表"""
        pass

    def validate_scraped_data(self, services: List[str]) -> bool:
        """验证抓取数据的完整性（±30%变化范围）"""
        pass

    def _extract_service_names(self, html_content: str) -> List[str]:
        """从HTML内容中提取服务名称"""
        pass
```

**实现要点**：
- 使用BeautifulSoup解析HTML
- 实施User-Agent轮换和速率限制
- 遵守robots.txt规则
- 基本的重试机制（最多3次）
- 数据完整性验证（服务数量变化±30%范围内）

#### 3.1.2 PDF Parser (PDF解析器)

**功能职责**：
- 从S3下载并解析官方PDF文档
- 提取三列数据：AWS offering, Long name, Short name
- 标准化处理（AWS→Amazon替换）

**核心接口**：
```python
class PDFParser:
    """S3 PDF文档解析器"""

    def __init__(self, config: ConfigManager):
        self.config = config
        self.s3_client = boto3.client('s3', region_name='cn-northwest-1')

    def parse_pdf(self) -> List[ServiceInfo]:
        """解析S3中的PDF文件，提取三列数据"""
        pass

    def download_pdf_from_s3(self, bucket: str, key: str) -> bytes:
        """从S3下载PDF文件"""
        pass

    def extract_three_columns(self, pdf_content: bytes) -> List[Dict[str, str]]:
        """提取PDF中的三列数据"""
        pass

    def standardize_pdf_data(self, raw_data: List[Dict]) -> List[ServiceInfo]:
        """标准化PDF数据（AWS→Amazon替换等）"""
        pass
```

**实现要点**：
- 使用pdfplumber库解析PDF
- 自动解析s3://bucket/key格式路径
- 标准化处理：所有'AWS'和'aws'替换为'Amazon'
- 清理internal_name字段的特殊字符
- 基本的错误处理和数据验证

### 3.2 数据处理组件

#### 3.2.1 Data Processor (数据处理器)

**功能职责**：
- 智能匹配网页数据与PDF数据
- 实施冲突解决策略
- 生成标准化的ServiceInfo对象

**核心接口**：
```python
class DataProcessor:
    """多数据源智能匹配和处理器"""

    def process_services_with_conflict_resolution(self, web_services: List[str],
                                                pdf_services: List[ServiceInfo]) -> List[ServiceInfo]:
        """处理和合并多数据源服务数据，包含冲突解决"""
        pass

    def intelligent_matching(self, web_name: str,
                           pdf_services: List[ServiceInfo]) -> Optional[ServiceInfo]:
        """智能匹配算法：精确匹配、模糊匹配、正则匹配"""
        pass

    def resolve_data_conflicts(self, web_data: Dict, pdf_data: Dict) -> Dict:
        """解决网页数据和PDF数据之间的冲突"""
        pass

    def generate_service_info(self, authoritative_name: str,
                            pdf_match: Optional[ServiceInfo]) -> ServiceInfo:
        """生成标准化的ServiceInfo对象"""
        pass
```

**冲突解决策略**：
1. **authoritative_full_name**: 网页数据优先（权威性更高）
2. **short_name_en**: PDF数据优先（更准确的缩写）
3. **service_code**: PDF数据优先（更标准化）
4. **internal_name**: 仅使用PDF数据

#### 3.2.2 Regex Pattern Generator (正则模式生成器)

**功能职责**：
- 为每个服务生成8种核心正则表达式模式
- 生成完整的JSONB元数据结构
- 实施边界保护机制

**核心接口**：
```python
class RegexPatternGenerator:
    """8种核心正则表达式模式生成器"""

    def generate_comprehensive_service_patterns_with_metadata(self,
                                                            service_data: ServiceInfo) -> List[RegexPattern]:
        """为单个服务生成包含完整metadata的模式集合"""
        pass

    def generate_pattern_metadata(self, service_data: ServiceInfo,
                                pattern_type: str, pattern_info: Dict) -> Dict:
        """生成完整的pattern metadata结构"""
        pass

    def validate_pattern_syntax(self, pattern: RegexPattern) -> Tuple[bool, Optional[str]]:
        """验证正则表达式语法正确性"""
        pass
```

**8种核心模式类型**：
1. **全称复合后缀模式** (优先级: 120) - 完整名称+复杂后缀
2. **全称标准模式** (优先级: 115) - 精确匹配+边界保护
3. **简称复合后缀模式** (优先级: 110) - 简称+复杂后缀
4. **简称标准模式** (优先级: 105) - 简称+边界保护
5. **缩写复合后缀模式** (优先级: 100) - 缩写+后缀
6. **缩写标准模式** (优先级: 95) - 纯缩写+边界保护
7. **特殊变体模式** (优先级: 125) - Aurora PostgreSQL等
8. **上下文保护模式** (优先级: 90) - 避免误匹配

### 3.3 存储管理组件

#### 3.3.1 RDS Client (数据库客户端)

**功能职责**：
- 执行高效的UPSERT操作
- 批量数据库操作优化
- 事务管理和错误处理

**核心接口**：
```python
class RDSClient:
    """RDS PostgreSQL数据库客户端"""

    def batch_upsert_services(self, services_data_list: List[ServiceInfo]) -> Tuple[int, List]:
        """批量UPSERT服务数据，提高性能"""
        pass

    def batch_insert_patterns_with_metadata(self, patterns_data_list: List[RegexPattern]) -> Tuple[int, List]:
        """批量插入正则表达式模式，优化JSONB metadata处理"""
        pass

    def get_db_connection(self) -> psycopg2.connection:
        """获取数据库连接，支持连接池"""
        pass
```

**性能优化特性**：
- 使用psycopg2.extras.execute_values进行批量操作
- 利用部分索引idx_regex_patterns_active_valid优化查询
- JSONB GIN索引优化metadata查询
- 连接池管理提高连接复用效率

### 3.4 配置管理组件

#### 3.4.1 Config Manager (配置管理器)

**功能职责**：
- 从AWS Secrets Manager安全获取配置
- 提供配置缓存机制
- 解析和验证配置格式

**核心接口**：
```python
class ConfigManager:
    """AWS Secrets Manager配置管理器"""

    def __init__(self):
        self.secrets_client = boto3.client('secretsmanager', region_name='cn-northwest-1')
        self.secret_arn = os.environ.get('SECRETS_MANAGER_ARN')
        self._config_cache = None
        self._cache_ttl = 3600  # 1小时TTL

    def get_config(self) -> Dict[str, Any]:
        """从Secrets Manager获取配置信息，支持缓存"""
        pass

    def get_database_config(self) -> Dict[str, str]:
        """获取数据库连接配置"""
        pass

    def get_s3_pdf_path(self) -> Tuple[str, str]:
        """解析S3 PDF路径，返回(bucket, key)"""
        pass
```

**配置结构示例**：
```json
{
  "database": {
    "host": "postgres-shared-dev.cvsmudg6yyle.rds.cn-northwest-1.amazonaws.com.cn",
    "port": 5432,
    "username": "mass_email_translator",
    "password": "[敏感信息]",
    "database": "mass_email_translator"
  },
  "s3": {
    "snpdf": "s3://mass-email-translator-feedback/awsofferingnames_pdf/AWSOfferingNames05032024.pdf"
  }
}
```

---

## 4. 数据库集成设计

### 4.1 v2.0数据库架构集成

#### 4.1.1 核心表结构

**service_names表 (v2架构)**：
- `authoritative_full_name`: 业务主键，来自官网网页的权威服务全称
- `base_name`: 翻译逻辑用，规范化基础名称（不含括号）
- `internal_name`: 来自PDF的 internal name
- `full_name_en`: 英文全称，与 authoritative_full_name 相同
- `short_name_en`: 英文缩写，来自PDF或等于全称
- `service_code`: AWS官方服务代码 (ec2, s3等)
- `source`: 数据来源 (web_scrape, pdf_parse, manual)
- `is_active`: 是否启用
- `last_synced_at`: 上次同步时间

**regex_patterns表 (8种模式类型)**：
- `pattern_name`: 模式的易读名称 (VARCHAR(100) UNIQUE)
- `pattern_type`: ENUM类型 (SERVICE_NAME, TIMEZONE, CLI_COMMAND, URL, GENERAL, CONTEXT_PROTECTED)
- `regex_string`: 正则表达式本身 (TEXT, <10000字符约束)
- `related_service_id`: 关联的服务ID (外键到service_names表)
- `service_code`: 服务代码，如 "ec2", "s3"
- `priority`: 匹配优先级，数字越大优先级越高 (90-130)
- `metadata`: JSONB字段存储结构化元数据
- `is_active`: 是否启用
- `validation_status`: 验证状态 (pending, valid, invalid)

#### 4.1.2 字段职责分离架构

| 字段 | 职责 | 用途 |
|------|------|------|
| `authoritative_full_name` | 数据同步的业务主键 | 作为UPSERT操作的唯一业务键 |
| `base_name` | 翻译逻辑状态跟踪 | 用于翻译系统的状态跟踪和分组 |
| `full_name_en` | 首次提及替换值 | 提供首次提及时的替换值 |
| `short_name_en` | 后续提及替换值 | 提供后续提及时的替换值 |
| `internal_name` | PDF原始数据保留 | 保留原始PDF数据用于追溯 |
| `service_code` | 系统内部标识和模式分组管理 | 用于系统内部标识和正则表达式模式分组 |

### 4.2 UPSERT操作设计

#### 4.2.1 服务数据UPSERT

```python
def upsert_service(service_data: ServiceInfo) -> int:
    """
    使用authoritative_full_name作为业务主键进行UPSERT操作

    Args:
        service_data: 服务数据对象，必须包含以下字段：
            - authoritative_full_name: 权威服务全称（业务主键）
            - base_name: 规范化基础名称（用于翻译逻辑）
            - internal_name: PDF内部名称（可选）
            - short_name_en: 英文简称（可选，默认使用authoritative_full_name）
            - service_code: 服务代码（可选）
            - source: 数据来源（可选，默认'web_scrape'）

    Returns:
        int: 服务记录的ID
    """

    sql = """
    INSERT INTO service_names
        (authoritative_full_name, base_name, internal_name, full_name_en,
         short_name_en, service_code, source, last_synced_at)
    VALUES
        (%(authoritative_full_name)s, %(base_name)s, %(internal_name)s,
         %(authoritative_full_name)s, %(short_name_en)s, %(service_code)s,
         %(source)s, NOW())
    ON CONFLICT (authoritative_full_name)
    DO UPDATE SET
        base_name = EXCLUDED.base_name,
        internal_name = EXCLUDED.internal_name,
        full_name_en = EXCLUDED.authoritative_full_name,
        short_name_en = EXCLUDED.short_name_en,
        service_code = EXCLUDED.service_code,
        source = EXCLUDED.source,
        last_synced_at = NOW(),
        updated_at = NOW()
    RETURNING id, authoritative_full_name;
    """
```

#### 4.2.2 批量UPSERT优化

```python
def batch_upsert_services(services_data_list: List[ServiceInfo]) -> Tuple[int, List]:
    """
    批量UPSERT服务数据，提高性能 - v2.0 优化版

    性能优化特性：
    - 使用psycopg2.extras.execute_values进行批量操作
    - 利用部分索引 idx_service_names_active_code 优化查询
    - 事务管理确保操作原子性
    - 批量失败时回退到单个处理模式

    Returns:
        tuple: (成功处理数量, 失败列表)
    """

    batch_size = 50  # 优化的批量大小
    success_count = 0
    failed_services = []

    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                for i in range(0, len(services_data_list), batch_size):
                    batch = services_data_list[i:i + batch_size]
                    batch_success, batch_failed = process_service_batch(cur, batch)
                    success_count += batch_success
                    failed_services.extend(batch_failed)

                conn.commit()

    except Exception as e:
        logger.error(f"Batch upsert transaction failed: {e}")
        # 回退到单个处理模式
        return fallback_individual_upsert(services_data_list)

    return success_count, failed_services
```

### 4.3 正则模式数据库集成

#### 4.3.1 JSONB元数据结构

```json
{
    "patternCategory": "full_complex_suffix",
    "generatedBy": "aws_service_sync_v2",
    "generatedAt": "2025-01-13T10:30:00Z",
    "serviceCode": "ec2",
    "hasBoundaryProtection": true,
    "isCompoundWithSuffix": true,
    "suffixGroup": 1,
    "suffixType": "instance",
    "assemblyRule": "capture_group_1",
    "expectedFrequency": "medium",
    "cacheStrategy": "standard",
    "indexOptimized": true
}
```

#### 4.3.2 批量模式插入优化

```python
def batch_insert_patterns_with_metadata(patterns_data_list: List[RegexPattern]) -> Tuple[int, List]:
    """
    批量插入正则表达式模式，优化JSONB metadata处理

    优化特性：
    - 利用GIN索引进行冲突检测
    - JSONB字段的高效处理
    - 批量验证正则表达式语法
    - 自动更新JSONB GIN索引统计信息

    Returns:
        tuple: (验证通过数量, 冲突列表)
    """

    batch_size = 100  # JSONB处理的优化批量大小
    validated_count = 0
    conflicts = []

    try:
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                for i in range(0, len(patterns_data_list), batch_size):
                    batch = patterns_data_list[i:i + batch_size]
                    batch_validated, batch_conflicts = process_patterns_batch(cur, batch)
                    validated_count += batch_validated
                    conflicts.extend(batch_conflicts)

                conn.commit()

                # 更新JSONB GIN索引统计信息
                cur.execute("ANALYZE regex_patterns;")

    except Exception as e:
        logger.error(f"Batch pattern insert failed: {e}")
        raise

    return validated_count, conflicts
```

### 4.4 性能优化索引策略

#### 4.4.1 部分索引优化

```sql
-- 关键性能提升 - 部分索引，只索引活跃且有效的模式
CREATE INDEX idx_regex_patterns_active_valid
ON regex_patterns(priority DESC, id ASC)
WHERE is_active = TRUE AND validation_status = 'valid';

-- 只索引活跃的服务
CREATE INDEX idx_service_names_active_code
ON service_names(service_code) WHERE is_active = TRUE;
```

**性能提升效果**：
- 索引大小减少60-80%
- 查询速度提升10-50x
- 维护成本显著降低

#### 4.4.2 JSONB GIN索引

```sql
-- 为JSONB metadata字段创建GIN索引
CREATE INDEX idx_regex_patterns_metadata ON regex_patterns USING GIN (metadata);
```

**查询优化示例**：
```sql
-- 高效查询复合后缀模式
SELECT pattern_name, regex_string, metadata
FROM regex_patterns
WHERE is_active = TRUE
  AND validation_status = 'valid'
  AND metadata @> '{"isCompoundWithSuffix": true}'
ORDER BY priority DESC, id ASC;

-- 按服务代码查询模式
SELECT pattern_name, regex_string
FROM regex_patterns
WHERE is_active = TRUE
  AND validation_status = 'valid'
  AND metadata @> '{"serviceCode": "ec2"}'
ORDER BY priority DESC;
```

---

### 4.5 完整数据重新同步（空表初始化）

本节给出“空表初始化”与“全量重置”时的数据库端参考实现与约束。目标：在两表完全为空（service_names 与 regex_patterns 记录数=0）时高效、原子地完成初始装填，避免并发与不一致。

设计要点：
- 事务级互斥：使用 pg_try_advisory_xact_lock 防止并发全量操作
- 无内置备份：备份放在过程外部由应用层执行；过程内部不做 CREATE TABLE AS 备份
- 顺序写入：先 service_names 再 regex_patterns，避免禁用外键
- 关联键策略：优先 authoritative_full_name（业务唯一键），其次 service_code（需唯一）
- 硬失败门槛：SERVICE_NAME 类型的孤儿模式（active 且无 related_service_id）直接抛错回滚
- 统计与验证：结束前 ANALYZE，两表计数与孤儿统计入日志

参考实现（范式）：

```sql
CREATE OR REPLACE FUNCTION perform_complete_data_resync(
    p_services_data JSONB,      -- JSONB 数组：每项含 authoritative_full_name, base_name, short_name_en, internal_name, service_code, source
    p_patterns_data JSONB,      -- JSONB 数组：每项含 pattern_name, pattern_type, regex_string, priority, metadata, authoritative_full_name 或 service_code
    p_batch_size   INTEGER DEFAULT 100
) RETURNS TABLE (
    operation_status TEXT,
    services_inserted INTEGER,
    patterns_inserted INTEGER,
    execution_time_seconds NUMERIC
) AS $$
DECLARE
    v_t0 TIMESTAMPTZ := clock_timestamp();
    v_locked BOOLEAN;
    v_svc_count INTEGER;
    v_pat_count INTEGER;
    v_orphans INTEGER;
BEGIN
    -- 1) 事务级互斥（同一进程内任意并发全量操作互斥）
    SELECT pg_try_advisory_xact_lock(845932017) INTO v_locked;  -- 固定键
    IF NOT v_locked THEN
        RAISE EXCEPTION 'Another complete resync is in progress';
    END IF;

    -- 2) 再确认“空表”前置条件（避免并发误判）
    SELECT COUNT(*) INTO v_svc_count FROM service_names;
    SELECT COUNT(*) INTO v_pat_count FROM regex_patterns;
    IF v_svc_count > 0 OR v_pat_count > 0 THEN
        RAISE EXCEPTION 'Tables are not empty. Use full resync with external backup instead.';
    END IF;

    -- 3) 装载 service_names（无须禁用外键）
    WITH svc AS (
        SELECT jsonb_array_elements(p_services_data) AS j
    )
    INSERT INTO service_names(
        authoritative_full_name, base_name, internal_name,
        full_name_en, short_name_en, service_code, source, is_active,
        last_synced_at
    )
    SELECT
        (j->>'authoritative_full_name')::VARCHAR(255),
        (j->>'base_name')::VARCHAR(255),
        NULLIF(j->>'internal_name','')::VARCHAR(255),
        (j->>'authoritative_full_name')::VARCHAR(255),
        (j->>'short_name_en')::VARCHAR(100),
        NULLIF(j->>'service_code','')::VARCHAR(50),
        COALESCE(j->>'source','web_scrape')::rule_source,
        TRUE,
        NOW()
    FROM svc
    WHERE (j->>'authoritative_full_name') IS NOT NULL AND length(j->>'authoritative_full_name')>0;

    GET DIAGNOSTICS services_inserted = ROW_COUNT;

    -- 4) 装载 regex_patterns（优先按 authoritative_full_name 关联，其次按唯一 service_code 关联）
    WITH pat AS (
        SELECT jsonb_array_elements(p_patterns_data) AS j
    )
    INSERT INTO regex_patterns(
        pattern_name, pattern_type, regex_string, related_service_id,
        service_code, priority, metadata, is_active, validation_status
    )
    SELECT
        (j->>'pattern_name')::VARCHAR(100),
        (j->>'pattern_type')::regex_pattern_type,
        (j->>'regex_string')::TEXT,
        COALESCE(
            -- 首选：按 authoritative_full_name 精确关联
            (SELECT s.id FROM service_names s WHERE s.authoritative_full_name = j->>'authoritative_full_name' LIMIT 1),
            -- 其次：按 service_code 唯一关联（如不唯一将插入失败由后续校验拦截）
            (SELECT s2.id FROM service_names s2 WHERE s2.service_code = j->>'service_code' LIMIT 1)
        ) AS related_service_id,
        NULLIF(j->>'service_code','')::VARCHAR(50),
        COALESCE((j->>'priority')::INTEGER, 100),
        COALESCE(j->'metadata', '{}'::JSONB),
        TRUE,
        'pending'::validation_status_type
    FROM pat
    WHERE (j->>'regex_string') IS NOT NULL AND length(j->>'regex_string')>0 AND length(j->>'regex_string')<10000;

    GET DIAGNOSTICS patterns_inserted = ROW_COUNT;

    -- 5) 硬失败：禁止 SERVICE_NAME 孤儿模式
    SELECT COUNT(*) INTO v_orphans
    FROM regex_patterns rp
    LEFT JOIN service_names sn ON sn.id = rp.related_service_id
    WHERE rp.pattern_type = 'SERVICE_NAME' AND rp.is_active = TRUE AND sn.id IS NULL;

    IF v_orphans > 0 THEN
        RAISE EXCEPTION 'Found % orphan SERVICE_NAME patterns. Abort and rollback.', v_orphans;
    END IF;

    -- 6) 统计与优化
    ANALYZE service_names;
    ANALYZE regex_patterns;

    operation_status := 'SUCCESS';
    execution_time_seconds := EXTRACT(EPOCH FROM (clock_timestamp() - v_t0));
    RETURN NEXT;
END;
$$ LANGUAGE plpgsql;
```

实现说明：
- 未使用 session_replication_role；通过写入顺序保证外键一致性
- 未做过程内备份；请在应用层于“非空全量重置”时先执行快照/导出
- 关联优先 authoritative_full_name；对 SERVICE_NAME 孤儿模式设硬性失败门槛

---

## 5. API接口设计

### 5.1 内部组件接口

由于模块采用完全独立设计，不提供任何对外API接口。以下为内部组件间的接口规范。

#### 5.1.1 数据模型接口

**ServiceInfo数据模型**：
```python
@dataclass
class ServiceInfo:
    """服务信息数据模型 - v2架构"""

    # 核心字段 - 字段职责分离
    authoritative_full_name: str    # 来自网页的权威服务全称（业务主键）
    base_name: str                  # 用于翻译逻辑的规范化基础名称
    internal_name: Optional[str]    # 来自PDF的"AWS offering (internal name)"
    full_name_en: str              # 首次提及时的英文全称
    short_name_en: str             # 后续提及时的英文简称

    # 扩展字段
    service_code: Optional[str] = None     # AWS官方服务代码
    source: str = 'web_scrape'             # 数据来源
    is_active: bool = True                 # 是否启用
    last_synced_at: Optional[datetime] = None  # 上次同步时间

    def derive_base_name(self) -> str:
        """从authoritative_full_name推导base_name，移除括号内的缩写"""
        return re.sub(r'\s*\([^)]*\)', '', self.authoritative_full_name).strip()

    def generate_service_code(self) -> Optional[str]:
        """基于internal_name生成service_code"""
        if not self.internal_name:
            return None

        # 转换为小写，移除非字母数字字符，保留中间空格
        code = self.internal_name.lower()
        code = re.sub(r'[^a-zA-Z0-9\s]', '', code)
        return code.strip() if code else None
```

**RegexPattern数据模型**：
```python
@dataclass
class RegexPattern:
    """正则表达式模式数据模型"""

    pattern_name: str               # 模式的易读名称
    pattern_type: str               # 模式类型 ENUM
    regex_string: str               # 正则表达式本身
    related_service_id: Optional[int] # 关联的服务ID
    service_code: Optional[str]     # 服务代码
    priority: int = 100             # 匹配优先级（90-130）
    metadata: Optional[Dict[str, Any]] = None   # JSONB元数据
    is_active: bool = True          # 是否启用
    validation_status: str = 'pending'  # 验证状态
```

#### 5.1.2 同步流程接口

**主要数据流**：
```mermaid
sequenceDiagram
    participant EB as EventBridge
    participant L as Lambda Function
    participant WS as Web Scraper
    participant PP as PDF Parser
    participant DP as Data Processor
    participant RPG as Regex Pattern Generator
    participant SC as Storage Client
    participant RDS as PostgreSQL
    participant SM as Secrets Manager

    EB->>L: 触发同步任务
    L->>SM: 获取配置信息
    SM-->>L: 返回数据库和S3配置

    par 并行数据获取
        L->>WS: 启动网页抓取
        WS->>WS: 抓取AWS官网数据
        WS-->>L: 返回网页服务数据
    and
        L->>PP: 启动PDF解析
        PP->>PP: 解析S3 PDF文档
        PP-->>L: 返回PDF服务数据
    end

    L->>DP: 数据处理和合并
    DP->>DP: 智能匹配和冲突解决
    DP-->>L: 返回标准化数据

    L->>SC: 批量存储服务数据
    SC->>RDS: UPSERT操作
    RDS-->>SC: 返回存储结果

    L->>RPG: 生成正则表达式模式
    RPG->>RPG: 生成8种核心模式
    RPG-->>L: 返回模式数据

    L->>SC: 批量存储模式数据
    SC->>RDS: 批量插入模式
    RDS-->>SC: 返回存储结果

    L->>L: 生成同步报告
    L-->>EB: 返回执行结果
```

### 5.2 Lambda函数入口设计

#### 5.2.1 主入口函数

```python
def lambda_handler(event, context):
    """
    Lambda函数入口 - v2.0优化版
    集成核心功能：增量同步、生命周期管理、数据质量保证
    """
    start_time = time.time()

    try:
        logger.info("开始执行AWS服务同步 - v2.0优化版")

        # 1. 初始化配置管理
        config_manager = ConfigManager()
        config = config_manager.get_config()

        # 2. 确定同步策略（增量 vs 全量）
        sync_strategy = determine_sync_strategy(event)
        logger.info(f"使用同步策略: {sync_strategy}")

        # 3. 执行数据同步
        if sync_strategy == 'incremental':
            sync_results = perform_incremental_sync()
        else:
            sync_results = perform_full_sync_with_enhancements()

        # 4. 边界保护系统同步
        boundary_results = sync_boundary_protection_system()

        # 5. 模式生命周期管理
        lifecycle_results = manage_pattern_lifecycle()

        # 6. 数据质量验证
        validation_results = comprehensive_data_validation()

        # 7. 性能统计和结果汇总
        result_summary = {
            'sync_strategy': sync_strategy,
            'sync_results': sync_results,
            'boundary_protection': boundary_results,
            'lifecycle_management': lifecycle_results,
            'data_quality': {
                'overall_score': validation_results['quality_report']['overall_score'],
                'issues_found': sum(len(issues) for issues in validation_results['validation_results'].values()),
                'auto_fixed': validation_results['auto_fix_results']
            },
            'database_version': 'v2.0',
            'features_enabled': [
                'incremental_sync', 'lifecycle_management', 'data_quality_assurance',
                'boundary_protection', 'metadata_generation',
                'performance_optimization', 'conflict_resolution'
            ],
            'execution_time': time.time() - start_time
        }

        logger.info(f"同步完成 - {result_summary}")

        return {
            'statusCode': 200,
            'body': json.dumps({
                'success': True,
                'version': '2.0',
                'summary': result_summary
            })
        }

    except Exception as e:
        logger.error(f"同步失败: {e}")

        # 错误恢复机制
        try:
            error_recovery_results = perform_error_recovery(e)
            logger.info(f"错误恢复完成: {error_recovery_results}")
        except Exception as recovery_error:
            logger.error(f"错误恢复失败: {recovery_error}")

        return {
            'statusCode': 500,
            'body': json.dumps({
                'success': False,
                'error': str(e),
                'version': '2.0',
                'recovery_attempted': True
            })
        }
```

#### 5.2.2 同步策略决策

```python
def determine_sync_strategy(event):
    """确定同步策略"""
    # 检查事件参数
    if event.get('force_full_sync'):
        return 'full'

    # 检查上次同步时间
    last_sync = get_last_sync_timestamp()
    if not last_sync:
        return 'full'  # 首次同步

    # 检查时间间隔
    time_since_last_sync = datetime.utcnow() - last_sync
    if time_since_last_sync > timedelta(days=7):
        return 'full'  # 超过一周执行全量同步

    return 'incremental'
```

### 5.3 数据消费接口

#### 5.3.1 数据库查询接口

其他系统通过以下标准化查询获取数据：

```sql
-- 获取活跃的服务名称数据
SELECT id, authoritative_full_name, base_name, full_name_en, short_name_en, service_code
FROM service_names
WHERE is_active = TRUE
ORDER BY authoritative_full_name;

-- 获取活跃且有效的正则表达式模式
SELECT pattern_name, regex_string, metadata, priority, service_code
FROM regex_patterns
WHERE is_active = TRUE AND validation_status = 'valid'
ORDER BY priority DESC, id ASC;

-- 按服务代码查询相关模式
SELECT pattern_name, regex_string, priority
FROM regex_patterns
WHERE is_active = TRUE
  AND validation_status = 'valid'
  AND service_code = 'ec2'
ORDER BY priority DESC;
```

#### 5.3.2 数据版本管理

```python
def generate_data_version_hash():
    """生成数据版本哈希，供依赖系统检测数据变更"""
    query = """
    SELECT
        COUNT(*) as service_count,
        MAX(updated_at) as last_service_update,
        COUNT(CASE WHEN pattern_type = 'SERVICE_NAME' THEN 1 END) as pattern_count,
        MAX(CASE WHEN pattern_type = 'SERVICE_NAME' THEN updated_at END) as last_pattern_update
    FROM service_names s
    LEFT JOIN regex_patterns r ON s.id = r.related_service_id
    WHERE s.is_active = TRUE AND (r.is_active IS NULL OR r.is_active = TRUE);
    """

    result = execute_query(query)
    version_data = f"{result['service_count']}-{result['pattern_count']}-{result['last_service_update']}-{result['last_pattern_update']}"

    return hashlib.md5(version_data.encode()).hexdigest()
```

---

## 6. 错误处理和监控设计

### 6.1 分层错误处理策略

#### 6.1.1 错误分类体系

**Level 1: 致命错误 (Critical)**
- 数据库连接失败
- AWS Secrets Manager访问失败
- 配置文件格式错误

**Level 2: 业务错误 (Business)**
- 网页抓取失败（网络超时、页面结构变化）
- PDF解析失败（文件损坏、格式不支持）
- 数据验证失败（数据完整性检查不通过）

**Level 3: 数据错误 (Data)**
- 单个服务数据处理失败
- 正则表达式语法错误
- 数据库约束冲突

**Level 4: 警告 (Warning)**
- 数据源部分不可用
- 性能阈值超标
- 数据质量分数低于预期

#### 6.1.2 错误处理机制

```python
class ErrorHandler:
    """分层错误处理器"""

    def __init__(self):
        self.error_counts = defaultdict(int)
        self.recovery_strategies = {
            'critical': self.handle_critical_error,
            'business': self.handle_business_error,
            'data': self.handle_data_error,
            'warning': self.handle_warning
        }

    def handle_error(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """统一错误处理入口"""
        error_level = self.classify_error(error)
        error_id = self.generate_error_id(error, context)

        # 记录错误
        self.log_error(error, error_level, error_id, context)

        # 执行恢复策略
        recovery_result = self.recovery_strategies[error_level](error, context)

        # 更新错误统计
        self.error_counts[error_level] += 1

        return {
            'error_id': error_id,
            'error_level': error_level,
            'recovery_result': recovery_result,
            'should_continue': recovery_result.get('recoverable', False)
        }

    def handle_critical_error(self, error: Exception, context: Dict) -> Dict:
        """处理致命错误 - 立即停止执行"""
        logger.critical(f"致命错误，停止执行: {error}")

        # 发送紧急通知
        self.send_emergency_notification(error, context)

        return {
            'recoverable': False,
            'action': 'terminate',
            'notification_sent': True
        }

    def handle_business_error(self, error: Exception, context: Dict) -> Dict:
        """处理业务错误 - 尝试降级策略"""
        logger.error(f"业务错误，尝试降级处理: {error}")

        # 尝试降级策略
        if context.get('operation') == 'web_scraping':
            return self.fallback_to_cached_data(context)
        elif context.get('operation') == 'pdf_parsing':
            return self.fallback_to_web_only(context)

        return {
            'recoverable': True,
            'action': 'fallback',
            'fallback_strategy': 'partial_sync'
        }

    def handle_data_error(self, error: Exception, context: Dict) -> Dict:
        """处理数据错误 - 跳过问题数据"""
        logger.warning(f"数据错误，跳过当前项: {error}")

        # 记录问题数据用于后续分析
        self.record_problematic_data(context.get('data'), error)

        return {
            'recoverable': True,
            'action': 'skip',
            'data_recorded': True
        }
```

### 6.2 重试机制设计

#### 6.2.1 指数退避重试

```python
class RetryMechanism:
    """智能重试机制"""

    def __init__(self):
        self.retry_configs = {
            'web_scraping': {
                'max_attempts': 3,
                'base_delay': 2,
                'max_delay': 30,
                'backoff_factor': 2,
                'jitter': True
            },
            'pdf_parsing': {
                'max_attempts': 2,
                'base_delay': 1,
                'max_delay': 10,
                'backoff_factor': 2,
                'jitter': False
            },
            'database_operation': {
                'max_attempts': 5,
                'base_delay': 0.5,
                'max_delay': 5,
                'backoff_factor': 1.5,
                'jitter': True
            }
        }

    def retry_with_backoff(self, operation: str, func: Callable, *args, **kwargs):
        """执行带指数退避的重试"""
        config = self.retry_configs.get(operation, self.retry_configs['database_operation'])

        for attempt in range(config['max_attempts']):
            try:
                return func(*args, **kwargs)

            except Exception as e:
                if attempt == config['max_attempts'] - 1:
                    logger.error(f"重试失败，已达最大尝试次数 {config['max_attempts']}: {e}")
                    raise

                delay = min(
                    config['base_delay'] * (config['backoff_factor'] ** attempt),
                    config['max_delay']
                )

                if config['jitter']:
                    delay *= (0.5 + random.random() * 0.5)  # 添加抖动

                logger.warning(f"操作失败，{delay:.2f}秒后重试 (尝试 {attempt + 1}/{config['max_attempts']}): {e}")
                time.sleep(delay)
```

### 6.3 监控和日志设计

#### 6.3.1 结构化日志

```python
class StructuredLogger:
    """结构化日志记录器"""

    def __init__(self):
        self.logger = logging.getLogger('aws_service_sync')
        self.setup_cloudwatch_handler()

    def log_sync_start(self, sync_strategy: str, config: Dict):
        """记录同步开始"""
        self.logger.info("sync_started", extra={
            'event_type': 'sync_lifecycle',
            'sync_strategy': sync_strategy,
            'config_version': config.get('version'),
            'timestamp': datetime.utcnow().isoformat()
        })

    def log_data_processing(self, operation: str, input_count: int, output_count: int, duration: float):
        """记录数据处理过程"""
        self.logger.info("data_processed", extra={
            'event_type': 'data_processing',
            'operation': operation,
            'input_count': input_count,
            'output_count': output_count,
            'processing_rate': output_count / duration if duration > 0 else 0,
            'duration_seconds': duration,
            'timestamp': datetime.utcnow().isoformat()
        })

    def log_performance_metrics(self, metrics: Dict):
        """记录性能指标"""
        self.logger.info("performance_metrics", extra={
            'event_type': 'performance',
            'metrics': metrics,
            'timestamp': datetime.utcnow().isoformat()
        })

    def log_data_quality(self, quality_report: Dict):
        """记录数据质量报告"""
        self.logger.info("data_quality_report", extra={
            'event_type': 'data_quality',
            'overall_score': quality_report.get('overall_score'),
            'issues_count': quality_report.get('issues_count'),
            'auto_fixed_count': quality_report.get('auto_fixed_count'),
            'quality_details': quality_report,
            'timestamp': datetime.utcnow().isoformat()
        })
```

#### 6.3.2 CloudWatch监控指标

```python
class CloudWatchMetrics:
    """CloudWatch自定义指标"""

    def __init__(self):
        self.cloudwatch = boto3.client('cloudwatch', region_name='cn-northwest-1')
        self.namespace = 'AWS/ServiceSync'

    def put_sync_metrics(self, sync_results: Dict):
        """发送同步指标到CloudWatch"""
        metrics = [
            {
                'MetricName': 'ServicesProcessed',
                'Value': sync_results.get('services_processed', 0),
                'Unit': 'Count'
            },
            {
                'MetricName': 'PatternsGenerated',
                'Value': sync_results.get('patterns_generated', 0),
                'Unit': 'Count'
            },
            {
                'MetricName': 'SyncDuration',
                'Value': sync_results.get('duration_seconds', 0),
                'Unit': 'Seconds'
            },
            {
                'MetricName': 'ErrorCount',
                'Value': sync_results.get('error_count', 0),
                'Unit': 'Count'
            },
            {
                'MetricName': 'DataQualityScore',
                'Value': sync_results.get('data_quality_score', 0),
                'Unit': 'Percent'
            }
        ]

        try:
            self.cloudwatch.put_metric_data(
                Namespace=self.namespace,
                MetricData=metrics
            )
            logger.info(f"已发送 {len(metrics)} 个指标到CloudWatch")

        except Exception as e:
            logger.error(f"发送CloudWatch指标失败: {e}")
```

### 6.4 健康检查和告警

#### 6.4.1 数据质量监控

```python
def comprehensive_data_validation():
    """全面的数据质量验证"""
    validation_results = {
        'service_names_validation': validate_service_names(),
        'regex_patterns_validation': validate_regex_patterns(),
        'data_consistency_validation': validate_data_consistency(),
        'performance_validation': validate_performance_metrics()
    }

    # 计算整体质量分数
    quality_score = calculate_overall_quality_score(validation_results)

    # 生成质量报告
    quality_report = {
        'overall_score': quality_score,
        'validation_timestamp': datetime.utcnow().isoformat(),
        'issues_found': sum(len(issues) for issues in validation_results.values()),
        'critical_issues': count_critical_issues(validation_results),
        'recommendations': generate_quality_recommendations(validation_results)
    }

    # 如果质量分数低于阈值，触发告警
    if quality_score < 80:
        send_quality_alert(quality_report)

    return {
        'validation_results': validation_results,
        'quality_report': quality_report,
        'auto_fix_results': attempt_auto_fixes(validation_results)
    }
```

---

## 7. 部署和配置设计

### 7.1 Lambda部署架构

#### 7.1.1 部署包结构

```
aws-service-sync-deployment/
├── lambda_function.py          # Lambda入口文件
├── requirements.txt            # Python依赖
├── aws_service_sync/          # 核心模块
│   ├── __init__.py
│   ├── handler.py             # 主处理逻辑
│   ├── config.py              # 配置管理
│   ├── scrapers/              # 数据获取组件
│   ├── processors/            # 数据处理组件
│   ├── storage/               # 存储管理组件
│   └── utils/                 # 工具类
├── deployment/
│   ├── cloudformation.yaml    # CloudFormation模板
│   ├── deploy.sh              # 部署脚本
│   └── environment/           # 环境配置
│       ├── dev.json
│       ├── staging.json
│       └── prod.json
└── tests/                     # 测试文件
    ├── unit/
    ├── integration/
    └── fixtures/
```

#### 7.1.2 Lambda函数配置

```yaml
# CloudFormation模板片段
AWSServiceSyncFunction:
  Type: AWS::Lambda::Function
  Properties:
    FunctionName: !Sub "${Environment}-aws-service-sync"
    Runtime: python3.11
    Handler: lambda_function.lambda_handler
    Code:
      ZipFile: |
        # 部署时替换为实际代码包
    MemorySize: 1024
    Timeout: 900  # 15分钟
    ReservedConcurrencyLimit: 1  # 确保单实例执行
    Environment:
      Variables:
        ENVIRONMENT: !Ref Environment
        SECRETS_MANAGER_ARN: !Ref SecretsManagerSecret
        LOG_LEVEL: INFO
        ENABLE_DETAILED_LOGGING: "true"
    VpcConfig:
      SecurityGroupIds:
        - !Ref LambdaSecurityGroup
      SubnetIds:
        - !Ref PrivateSubnet1
        - !Ref PrivateSubnet2
    DeadLetterQueue:
      TargetArn: !GetAtt DeadLetterQueue.Arn
    Tags:
      - Key: Project
        Value: MassEmailTranslator
      - Key: Component
        Value: AWSServiceSync
      - Key: Environment
        Value: !Ref Environment
```

#### 7.1.3 IAM权限策略

```yaml
LambdaExecutionRole:
  Type: AWS::IAM::Role
  Properties:
    AssumeRolePolicyDocument:
      Version: '2012-10-17'
      Statement:
        - Effect: Allow
          Principal:
            Service: lambda.amazonaws.com
          Action: sts:AssumeRole
    ManagedPolicyArns:
      - arn:aws-cn:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole
    Policies:
      - PolicyName: AWSServiceSyncPolicy
        PolicyDocument:
          Version: '2012-10-17'
          Statement:
            # Secrets Manager访问
            - Effect: Allow
              Action:
                - secretsmanager:GetSecretValue
              Resource: !Ref SecretsManagerSecret

            # S3 PDF文档访问
            - Effect: Allow
              Action:
                - s3:GetObject
              Resource:
                - "arn:aws-cn:s3:::mass-email-translator-feedback/awsofferingnames_pdf/*"

            # CloudWatch日志
            - Effect: Allow
              Action:
                - logs:CreateLogGroup
                - logs:CreateLogStream
                - logs:PutLogEvents
              Resource: !Sub "arn:aws-cn:logs:${AWS::Region}:${AWS::AccountId}:*"

            # CloudWatch指标
            - Effect: Allow
              Action:
                - cloudwatch:PutMetricData
              Resource: "*"
              Condition:
                StringEquals:
                  cloudwatch:namespace: "AWS/ServiceSync"

            # SQS死信队列
            - Effect: Allow
              Action:
                - sqs:SendMessage
              Resource: !GetAtt DeadLetterQueue.Arn
```

### 7.2 配置管理设计

#### 7.2.1 AWS Secrets Manager配置

```json
{
  "version": "2.0",
  "environment": "production",
  "database": {
    "host": "postgres-shared-prod.cvsmudg6yyle.rds.cn-northwest-1.amazonaws.com.cn",
    "port": 5432,
    "username": "mass_email_translator",
    "password": "[ENCRYPTED_PASSWORD]",
    "database": "mass_email_translator",
    "ssl_mode": "require",
    "connection_pool": {
      "min_connections": 1,
      "max_connections": 5,
      "connection_timeout": 30
    }
  },
  "s3": {
    "snpdf": "s3://mass-email-translator-feedback/awsofferingnames_pdf/AWSOfferingNames05032024.pdf",
    "region": "cn-northwest-1"
  },
  "web_scraping": {
    "target_url": "https://www.amazonaws.cn/about-aws/regional-product-services/",
    "timeout": 30,
    "max_retries": 3,
    "user_agents": [
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
    ]
  },
  "sync_settings": {
    "default_strategy": "incremental",
    "full_sync_interval_days": 7,
    "batch_size": 50,
    "enable_data_validation": true,
    "quality_threshold": 80
  },
  "monitoring": {
    "enable_cloudwatch_metrics": true,
    "enable_detailed_logging": true,
    "log_retention_days": 30,
    "alert_email": "<EMAIL>"
  }
}
```

#### 7.2.2 环境特定配置

```python
class EnvironmentConfig:
    """环境特定配置管理"""

    def __init__(self, environment: str):
        self.environment = environment
        self.config_overrides = {
            'development': {
                'sync_settings': {
                    'batch_size': 10,
                    'enable_data_validation': False
                },
                'monitoring': {
                    'enable_cloudwatch_metrics': False,
                    'enable_detailed_logging': True
                }
            },
            'staging': {
                'sync_settings': {
                    'batch_size': 25,
                    'quality_threshold': 70
                },
                'monitoring': {
                    'log_retention_days': 7
                }
            },
            'production': {
                'sync_settings': {
                    'batch_size': 50,
                    'quality_threshold': 85
                },
                'monitoring': {
                    'log_retention_days': 90
                }
            }
        }

    def get_environment_config(self, base_config: Dict) -> Dict:
        """获取环境特定配置"""
        overrides = self.config_overrides.get(self.environment, {})
        return self.deep_merge(base_config, overrides)

    def deep_merge(self, base: Dict, override: Dict) -> Dict:
        """深度合并配置"""
        result = base.copy()
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self.deep_merge(result[key], value)
            else:
                result[key] = value
        return result
```

### 7.3 调度和触发设计

#### 7.3.1 EventBridge定时调度

```yaml
SyncScheduleRule:
  Type: AWS::Events::Rule
  Properties:
    Name: !Sub "${Environment}-aws-service-sync-schedule"
    Description: "定期触发AWS服务同步"
    ScheduleExpression: "cron(0 2 * * ? *)"  # 每天凌晨2点执行
    State: ENABLED
    Targets:
      - Arn: !GetAtt AWSServiceSyncFunction.Arn
        Id: "AWSServiceSyncTarget"
        Input: |
          {
            "sync_strategy": "incremental",
            "triggered_by": "schedule"
          }

# 每周全量同步
WeeklyFullSyncRule:
  Type: AWS::Events::Rule
  Properties:
    Name: !Sub "${Environment}-aws-service-sync-weekly"
    Description: "每周全量同步"
    ScheduleExpression: "cron(0 3 ? * SUN *)"  # 每周日凌晨3点
    State: ENABLED
    Targets:
      - Arn: !GetAtt AWSServiceSyncFunction.Arn
        Id: "WeeklyFullSyncTarget"
        Input: |
          {
            "sync_strategy": "full",
            "triggered_by": "weekly_schedule"
          }
```

#### 7.3.2 手动触发支持

```python
def handle_manual_trigger(event: Dict) -> Dict:
    """处理手动触发的同步请求"""

    # 验证触发权限
    if not validate_trigger_permissions(event):
        raise PermissionError("无权限执行手动同步")

    # 解析触发参数
    sync_strategy = event.get('sync_strategy', 'incremental')
    force_full = event.get('force_full_sync', False)
    target_services = event.get('target_services', [])  # 指定服务同步

    # 构建同步配置
    sync_config = {
        'strategy': 'full' if force_full else sync_strategy,
        'target_services': target_services,
        'triggered_by': 'manual',
        'trigger_user': event.get('user_id'),
        'trigger_timestamp': datetime.utcnow().isoformat()
    }

    logger.info(f"手动触发同步: {sync_config}")

    return sync_config
```

### 7.4 安全策略设计

#### 7.4.1 网络安全

```yaml
# VPC安全组配置
LambdaSecurityGroup:
  Type: AWS::EC2::SecurityGroup
  Properties:
    GroupDescription: "AWS Service Sync Lambda安全组"
    VpcId: !Ref VPC
    SecurityGroupEgress:
      # HTTPS出站访问（用于网页抓取）
      - IpProtocol: tcp
        FromPort: 443
        ToPort: 443
        CidrIp: 0.0.0.0/0
        Description: "HTTPS出站访问"

      # PostgreSQL数据库访问
      - IpProtocol: tcp
        FromPort: 5432
        ToPort: 5432
        DestinationSecurityGroupId: !Ref DatabaseSecurityGroup
        Description: "PostgreSQL数据库访问"

      # DNS解析
      - IpProtocol: udp
        FromPort: 53
        ToPort: 53
        CidrIp: 0.0.0.0/0
        Description: "DNS解析"

# 数据库安全组
DatabaseSecurityGroup:
  Type: AWS::EC2::SecurityGroup
  Properties:
    GroupDescription: "数据库访问安全组"
    VpcId: !Ref VPC
    SecurityGroupIngress:
      - IpProtocol: tcp
        FromPort: 5432
        ToPort: 5432
        SourceSecurityGroupId: !Ref LambdaSecurityGroup
        Description: "Lambda函数访问"
```

#### 7.4.2 数据加密

```python
class SecurityManager:
    """安全管理器"""

    def __init__(self):
        self.kms_client = boto3.client('kms', region_name='cn-northwest-1')
        self.encryption_key_id = os.environ.get('KMS_KEY_ID')

    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        try:
            response = self.kms_client.encrypt(
                KeyId=self.encryption_key_id,
                Plaintext=data.encode('utf-8')
            )
            return base64.b64encode(response['CiphertextBlob']).decode('utf-8')
        except Exception as e:
            logger.error(f"数据加密失败: {e}")
            raise

    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        try:
            ciphertext_blob = base64.b64decode(encrypted_data.encode('utf-8'))
            response = self.kms_client.decrypt(CiphertextBlob=ciphertext_blob)
            return response['Plaintext'].decode('utf-8')
        except Exception as e:
            logger.error(f"数据解密失败: {e}")
            raise

    def validate_ssl_certificate(self, url: str) -> bool:
        """验证SSL证书"""
        try:
            response = requests.get(url, timeout=10, verify=True)
            return response.status_code == 200
        except requests.exceptions.SSLError:
            logger.warning(f"SSL证书验证失败: {url}")
            return False
        except Exception as e:
            logger.error(f"SSL验证异常: {e}")
            return False
```

### 7.5 部署自动化

#### 7.5.1 CI/CD流水线

```bash
#!/bin/bash
# deploy.sh - 自动化部署脚本

set -e

ENVIRONMENT=${1:-dev}
AWS_REGION="cn-northwest-1"
STACK_NAME="aws-service-sync-${ENVIRONMENT}"

echo "开始部署AWS服务同步模块到环境: ${ENVIRONMENT}"

# 1. 构建部署包
echo "构建部署包..."
rm -rf build/
mkdir -p build/
cp -r aws_service_sync/ build/
cp lambda_function.py build/
pip install -r requirements.txt -t build/

# 2. 创建ZIP包
echo "创建部署ZIP包..."
cd build/
zip -r ../aws-service-sync-${ENVIRONMENT}.zip .
cd ..

# 3. 上传到S3
echo "上传部署包到S3..."
aws s3 cp aws-service-sync-${ENVIRONMENT}.zip \
    s3://deployment-artifacts-${ENVIRONMENT}/aws-service-sync/ \
    --region ${AWS_REGION}

# 4. 部署CloudFormation栈
echo "部署CloudFormation栈..."
aws cloudformation deploy \
    --template-file deployment/cloudformation.yaml \
    --stack-name ${STACK_NAME} \
    --parameter-overrides \
        Environment=${ENVIRONMENT} \
        DeploymentPackageKey=aws-service-sync/aws-service-sync-${ENVIRONMENT}.zip \
    --capabilities CAPABILITY_IAM \
    --region ${AWS_REGION}

# 5. 验证部署
echo "验证部署..."
aws lambda invoke \
    --function-name ${ENVIRONMENT}-aws-service-sync \
    --payload '{"test": true}' \
    --region ${AWS_REGION} \
    response.json

if [ $? -eq 0 ]; then
    echo "部署成功完成!"
else
    echo "部署验证失败!"
    exit 1
fi
```

---

## 8. 性能优化策略

### 8.1 数据库性能优化

#### 8.1.1 索引策略优化

```sql
-- 核心性能提升索引
-- 1. 部分索引 - 只索引活跃且有效的记录
CREATE INDEX CONCURRENTLY idx_regex_patterns_active_valid
ON regex_patterns(priority DESC, id ASC)
WHERE is_active = TRUE AND validation_status = 'valid';

-- 2. 复合索引 - 优化常用查询组合
CREATE INDEX CONCURRENTLY idx_service_names_active_code
ON service_names(service_code, authoritative_full_name)
WHERE is_active = TRUE;

-- 3. JSONB GIN索引 - 优化元数据查询
CREATE INDEX CONCURRENTLY idx_regex_patterns_metadata
ON regex_patterns USING GIN (metadata);

-- 4. 时间戳索引 - 优化增量同步查询
CREATE INDEX CONCURRENTLY idx_service_names_sync_time
ON service_names(last_synced_at DESC)
WHERE is_active = TRUE;
```

**性能提升效果**：
- 查询速度提升：10-50x
- 索引大小减少：60-80%
- 内存使用优化：40-60%

#### 8.1.2 批量操作优化

```python
class OptimizedBatchProcessor:
    """优化的批量处理器"""

    def __init__(self):
        self.optimal_batch_sizes = {
            'service_upsert': 50,      # 服务数据UPSERT
            'pattern_insert': 100,     # 正则模式插入
            'validation_check': 200    # 数据验证检查
        }

    def batch_upsert_with_optimization(self, services_data: List[ServiceInfo]) -> Dict:
        """优化的批量UPSERT操作"""
        batch_size = self.optimal_batch_sizes['service_upsert']
        total_processed = 0
        performance_metrics = {
            'start_time': time.time(),
            'batches_processed': 0,
            'records_per_second': 0,
            'memory_usage': []
        }

        try:
            with get_optimized_db_connection() as conn:
                with conn.cursor() as cur:
                    # 预编译SQL语句
                    upsert_sql = self.prepare_optimized_upsert_sql()

                    for i in range(0, len(services_data), batch_size):
                        batch_start = time.time()
                        batch = services_data[i:i + batch_size]

                        # 使用execute_values进行批量操作
                        psycopg2.extras.execute_values(
                            cur, upsert_sql,
                            [self.prepare_service_tuple(service) for service in batch],
                            template=None, page_size=batch_size
                        )

                        total_processed += len(batch)
                        performance_metrics['batches_processed'] += 1

                        # 记录性能指标
                        batch_time = time.time() - batch_start
                        records_per_sec = len(batch) / batch_time
                        performance_metrics['memory_usage'].append(self.get_memory_usage())

                        logger.debug(f"批次处理完成: {len(batch)} 记录, {records_per_sec:.2f} 记录/秒")

                conn.commit()

        except Exception as e:
            logger.error(f"批量UPSERT失败: {e}")
            raise

        # 计算总体性能指标
        total_time = time.time() - performance_metrics['start_time']
        performance_metrics['records_per_second'] = total_processed / total_time
        performance_metrics['total_processed'] = total_processed
        performance_metrics['total_time'] = total_time

        return performance_metrics
```

### 8.2 内存和CPU优化

#### 8.2.1 内存管理优化

```python
class MemoryOptimizedProcessor:
    """内存优化处理器"""

    def __init__(self):
        self.memory_threshold = 800 * 1024 * 1024  # 800MB阈值
        self.gc_frequency = 100  # 每处理100个项目执行一次垃圾回收

    def process_large_dataset_with_memory_optimization(self, data_source):
        """内存优化的大数据集处理"""
        processed_count = 0

        # 使用生成器避免一次性加载所有数据
        for batch in self.yield_data_batches(data_source, batch_size=50):
            # 处理批次数据
            processed_batch = self.process_batch_efficiently(batch)

            # 立即存储，释放内存
            self.store_batch_results(processed_batch)

            processed_count += len(batch)

            # 定期执行垃圾回收
            if processed_count % self.gc_frequency == 0:
                self.perform_memory_cleanup()

                # 检查内存使用
                current_memory = self.get_memory_usage()
                if current_memory > self.memory_threshold:
                    logger.warning(f"内存使用过高: {current_memory / 1024 / 1024:.2f}MB")
                    self.force_garbage_collection()

        return processed_count

    def yield_data_batches(self, data_source, batch_size: int):
        """生成器模式批量读取数据"""
        batch = []
        for item in data_source:
            batch.append(item)
            if len(batch) >= batch_size:
                yield batch
                batch = []  # 清空批次，释放内存

        if batch:  # 处理最后一个不完整的批次
            yield batch

    def perform_memory_cleanup(self):
        """执行内存清理"""
        import gc
        gc.collect()

        # 清理临时变量
        if hasattr(self, '_temp_cache'):
            self._temp_cache.clear()
```

#### 8.2.2 并发处理优化

```python
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor

class ConcurrentProcessor:
    """并发处理优化器"""

    def __init__(self):
        self.max_concurrent_requests = 5  # 限制并发请求数
        self.thread_pool = ThreadPoolExecutor(max_workers=3)

    async def concurrent_web_scraping(self, urls: List[str]) -> List[Dict]:
        """并发网页抓取"""
        semaphore = asyncio.Semaphore(self.max_concurrent_requests)

        async def fetch_with_semaphore(session, url):
            async with semaphore:
                return await self.fetch_url_async(session, url)

        async with aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=10)
        ) as session:
            tasks = [fetch_with_semaphore(session, url) for url in urls]
            results = await asyncio.gather(*tasks, return_exceptions=True)

        # 过滤异常结果
        valid_results = [r for r in results if not isinstance(r, Exception)]
        error_count = len(results) - len(valid_results)

        if error_count > 0:
            logger.warning(f"并发抓取完成，{error_count} 个请求失败")

        return valid_results

    def parallel_pdf_processing(self, pdf_files: List[str]) -> List[Dict]:
        """并行PDF处理"""
        with ThreadPoolExecutor(max_workers=3) as executor:
            future_to_file = {
                executor.submit(self.process_single_pdf, pdf_file): pdf_file
                for pdf_file in pdf_files
            }

            results = []
            for future in concurrent.futures.as_completed(future_to_file):
                pdf_file = future_to_file[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    logger.error(f"PDF处理失败 {pdf_file}: {e}")

            return results
```

### 8.3 缓存策略优化

#### 8.3.1 多层缓存架构

```python
class MultiLevelCache:
    """多层缓存管理器"""

    def __init__(self):
        # L1: 内存缓存 (最快，容量小)
        self.memory_cache = {}
        self.memory_cache_ttl = {}
        self.memory_cache_max_size = 100

        # L2: 本地文件缓存 (中等速度，容量中等)
        self.file_cache_dir = "/tmp/aws_service_sync_cache"
        os.makedirs(self.file_cache_dir, exist_ok=True)

        # L3: 数据库缓存 (较慢，容量大，持久化)
        self.db_cache_table = "sync_cache"

    def get_cached_data(self, cache_key: str) -> Optional[Any]:
        """多层缓存查询"""
        # L1: 检查内存缓存
        if cache_key in self.memory_cache:
            if self.is_cache_valid(cache_key, self.memory_cache_ttl.get(cache_key)):
                logger.debug(f"内存缓存命中: {cache_key}")
                return self.memory_cache[cache_key]
            else:
                # 缓存过期，清理
                del self.memory_cache[cache_key]
                del self.memory_cache_ttl[cache_key]

        # L2: 检查文件缓存
        file_cache_path = os.path.join(self.file_cache_dir, f"{cache_key}.json")
        if os.path.exists(file_cache_path):
            try:
                with open(file_cache_path, 'r', encoding='utf-8') as f:
                    cached_data = json.load(f)

                if self.is_cache_valid(cache_key, cached_data.get('timestamp')):
                    logger.debug(f"文件缓存命中: {cache_key}")
                    # 提升到内存缓存
                    self.set_memory_cache(cache_key, cached_data['data'])
                    return cached_data['data']
                else:
                    os.remove(file_cache_path)  # 删除过期缓存
            except Exception as e:
                logger.warning(f"文件缓存读取失败: {e}")

        # L3: 检查数据库缓存
        db_cached_data = self.get_database_cache(cache_key)
        if db_cached_data:
            logger.debug(f"数据库缓存命中: {cache_key}")
            # 提升到上层缓存
            self.set_file_cache(cache_key, db_cached_data)
            self.set_memory_cache(cache_key, db_cached_data)
            return db_cached_data

        return None

    def set_cached_data(self, cache_key: str, data: Any, ttl_hours: int = 24):
        """设置多层缓存"""
        # 同时设置所有层级的缓存
        self.set_memory_cache(cache_key, data, ttl_hours)
        self.set_file_cache(cache_key, data, ttl_hours)
        self.set_database_cache(cache_key, data, ttl_hours)
```

### 8.4 网络优化

#### 8.4.1 连接池和重用

```python
class OptimizedNetworkClient:
    """优化的网络客户端"""

    def __init__(self):
        # HTTP连接池
        self.session = requests.Session()
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=10,
            pool_maxsize=20,
            max_retries=3,
            pool_block=False
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)

        # 数据库连接池
        self.db_pool = psycopg2.pool.ThreadedConnectionPool(
            minconn=1,
            maxconn=5,
            host=config['database']['host'],
            database=config['database']['database'],
            user=config['database']['username'],
            password=config['database']['password']
        )

    def get_optimized_db_connection(self):
        """获取优化的数据库连接"""
        return self.db_pool.getconn()

    def return_db_connection(self, conn):
        """归还数据库连接到池"""
        self.db_pool.putconn(conn)
```

---

## 9. 测试策略

### 9.1 测试架构设计

#### 9.1.1 测试层次结构

```
tests/
├── unit/                          # 单元测试
│   ├── test_web_scraper.py       # 网页抓取器测试
│   ├── test_pdf_parser.py        # PDF解析器测试
│   ├── test_data_processor.py    # 数据处理器测试
│   ├── test_regex_generator.py   # 正则生成器测试
│   └── test_storage_client.py    # 存储客户端测试
├── integration/                   # 集成测试
│   ├── test_end_to_end_sync.py   # 端到端同步测试
│   ├── test_database_integration.py  # 数据库集成测试
│   └── test_aws_services_integration.py  # AWS服务集成测试
├── performance/                   # 性能测试
│   ├── test_batch_processing.py  # 批量处理性能测试
│   ├── test_memory_usage.py      # 内存使用测试
│   └── test_concurrent_processing.py  # 并发处理测试
├── fixtures/                      # 测试数据
│   ├── sample_web_data.html      # 示例网页数据
│   ├── sample_pdf_data.pdf       # 示例PDF数据
│   └── expected_results.json     # 预期结果数据
└── conftest.py                    # pytest配置
```

#### 9.1.2 测试数据管理

```python
class TestDataManager:
    """测试数据管理器"""

    def __init__(self):
        self.fixtures_dir = Path(__file__).parent / "fixtures"
        self.test_db_config = self.load_test_db_config()

    def setup_test_database(self):
        """设置测试数据库"""
        # 创建测试数据库schema
        with psycopg2.connect(**self.test_db_config) as conn:
            with conn.cursor() as cur:
                # 创建测试表
                cur.execute(open('database/test_schema.sql').read())

                # 插入测试数据
                self.insert_test_data(cur)

                conn.commit()

    def cleanup_test_database(self):
        """清理测试数据库"""
        with psycopg2.connect(**self.test_db_config) as conn:
            with conn.cursor() as cur:
                cur.execute("TRUNCATE TABLE service_names, regex_patterns CASCADE;")
                conn.commit()

    def load_sample_web_data(self) -> str:
        """加载示例网页数据"""
        with open(self.fixtures_dir / "sample_web_data.html", 'r', encoding='utf-8') as f:
            return f.read()

    def load_expected_results(self) -> Dict:
        """加载预期结果数据"""
        with open(self.fixtures_dir / "expected_results.json", 'r', encoding='utf-8') as f:
            return json.load(f)
```

### 9.2 单元测试设计

#### 9.2.1 核心组件测试

```python
import pytest
from unittest.mock import Mock, patch, MagicMock
from aws_service_sync.scrapers.web_scraper import WebScraper
from aws_service_sync.processors.data_processor import DataProcessor

class TestWebScraper:
    """网页抓取器单元测试"""

    @pytest.fixture
    def web_scraper(self):
        return WebScraper()

    @pytest.fixture
    def sample_html(self, test_data_manager):
        return test_data_manager.load_sample_web_data()

    def test_extract_service_names_success(self, web_scraper, sample_html):
        """测试成功提取服务名称"""
        services = web_scraper._extract_service_names(sample_html)

        assert len(services) > 0
        assert "Amazon Elastic Compute Cloud (Amazon EC2)" in services
        assert "Amazon Simple Storage Service (Amazon S3)" in services

    def test_extract_service_names_empty_html(self, web_scraper):
        """测试空HTML处理"""
        services = web_scraper._extract_service_names("")
        assert services == []

    def test_validate_scraped_data_within_threshold(self, web_scraper):
        """测试数据验证 - 在阈值范围内"""
        # 模拟历史数据
        with patch.object(web_scraper, '_get_historical_count', return_value=100):
            # 当前数据在±30%范围内
            assert web_scraper.validate_scraped_data(['service'] * 80) == True
            assert web_scraper.validate_scraped_data(['service'] * 120) == True

    def test_validate_scraped_data_outside_threshold(self, web_scraper):
        """测试数据验证 - 超出阈值范围"""
        with patch.object(web_scraper, '_get_historical_count', return_value=100):
            # 当前数据超出±30%范围
            assert web_scraper.validate_scraped_data(['service'] * 60) == False
            assert web_scraper.validate_scraped_data(['service'] * 140) == False

    @patch('requests.Session.get')
    def test_scrape_services_network_error(self, mock_get, web_scraper):
        """测试网络错误处理"""
        mock_get.side_effect = requests.exceptions.RequestException("Network error")

        with pytest.raises(requests.exceptions.RequestException):
            web_scraper.scrape_services()

class TestDataProcessor:
    """数据处理器单元测试"""

    @pytest.fixture
    def data_processor(self):
        return DataProcessor()

    @pytest.fixture
    def sample_web_services(self):
        return [
            "Amazon Elastic Compute Cloud (Amazon EC2)",
            "Amazon Simple Storage Service (Amazon S3)",
            "Amazon Relational Database Service (Amazon RDS)"
        ]

    @pytest.fixture
    def sample_pdf_services(self):
        return [
            ServiceInfo(
                authoritative_full_name="Amazon Elastic Compute Cloud (Amazon EC2)",
                base_name="Amazon Elastic Compute Cloud",
                internal_name="ec2",
                full_name_en="Amazon Elastic Compute Cloud (Amazon EC2)",
                short_name_en="Amazon EC2",
                service_code="ec2"
            ),
            ServiceInfo(
                authoritative_full_name="Amazon Simple Storage Service (Amazon S3)",
                base_name="Amazon Simple Storage Service",
                internal_name="s3",
                full_name_en="Amazon Simple Storage Service (Amazon S3)",
                short_name_en="Amazon S3",
                service_code="s3"
            )
        ]

    def test_intelligent_matching_exact_match(self, data_processor, sample_pdf_services):
        """测试精确匹配"""
        web_name = "Amazon Elastic Compute Cloud (Amazon EC2)"
        match = data_processor.intelligent_matching(web_name, sample_pdf_services)

        assert match is not None
        assert match.service_code == "ec2"

    def test_intelligent_matching_fuzzy_match(self, data_processor, sample_pdf_services):
        """测试模糊匹配"""
        web_name = "Amazon EC2"  # 简化名称
        match = data_processor.intelligent_matching(web_name, sample_pdf_services)

        assert match is not None
        assert match.service_code == "ec2"

    def test_intelligent_matching_no_match(self, data_processor, sample_pdf_services):
        """测试无匹配情况"""
        web_name = "Amazon Unknown Service"
        match = data_processor.intelligent_matching(web_name, sample_pdf_services)

        assert match is None

    def test_process_services_with_conflict_resolution(self, data_processor,
                                                     sample_web_services, sample_pdf_services):
        """测试服务处理和冲突解决"""
        result = data_processor.process_services_with_conflict_resolution(
            sample_web_services, sample_pdf_services
        )

        assert len(result) == 3  # 3个网页服务

        # 检查匹配的服务
        ec2_service = next((s for s in result if s.service_code == "ec2"), None)
        assert ec2_service is not None
        assert ec2_service.authoritative_full_name == "Amazon Elastic Compute Cloud (Amazon EC2)"
        assert ec2_service.short_name_en == "Amazon EC2"  # 来自PDF

        # 检查未匹配的服务
        rds_service = next((s for s in result if "RDS" in s.authoritative_full_name), None)
        assert rds_service is not None
        assert rds_service.service_code is None  # 未匹配到PDF数据
```

### 9.3 集成测试设计

#### 9.3.1 端到端测试

```python
class TestEndToEndSync:
    """端到端同步测试"""

    @pytest.fixture(scope="class")
    def test_environment(self):
        """设置测试环境"""
        test_data_manager = TestDataManager()
        test_data_manager.setup_test_database()

        yield test_data_manager

        test_data_manager.cleanup_test_database()

    def test_full_sync_workflow(self, test_environment):
        """测试完整同步工作流"""
        # 模拟Lambda事件
        event = {
            "sync_strategy": "full",
            "triggered_by": "test"
        }
        context = MagicMock()

        # 执行同步
        result = lambda_handler(event, context)

        # 验证结果
        assert result['statusCode'] == 200
        response_body = json.loads(result['body'])
        assert response_body['success'] == True
        assert 'summary' in response_body

        # 验证数据库状态
        with psycopg2.connect(**test_environment.test_db_config) as conn:
            with conn.cursor() as cur:
                # 检查服务数据
                cur.execute("SELECT COUNT(*) FROM service_names WHERE is_active = TRUE")
                service_count = cur.fetchone()[0]
                assert service_count > 0

                # 检查正则模式数据
                cur.execute("SELECT COUNT(*) FROM regex_patterns WHERE is_active = TRUE")
                pattern_count = cur.fetchone()[0]
                assert pattern_count > 0

                # 验证8种模式类型都存在
                cur.execute("""
                    SELECT DISTINCT pattern_type
                    FROM regex_patterns
                    WHERE is_active = TRUE
                """)
                pattern_types = [row[0] for row in cur.fetchall()]
                expected_types = ['SERVICE_NAME', 'CONTEXT_PROTECTED']
                for expected_type in expected_types:
                    assert expected_type in pattern_types

    def test_incremental_sync_workflow(self, test_environment):
        """测试增量同步工作流"""
        # 先执行一次全量同步
        self.test_full_sync_workflow(test_environment)

        # 记录初始状态
        with psycopg2.connect(**test_environment.test_db_config) as conn:
            with conn.cursor() as cur:
                cur.execute("SELECT COUNT(*) FROM service_names")
                initial_count = cur.fetchone()[0]

        # 执行增量同步
        event = {
            "sync_strategy": "incremental",
            "triggered_by": "test"
        }
        context = MagicMock()

        result = lambda_handler(event, context)

        # 验证增量同步结果
        assert result['statusCode'] == 200
        response_body = json.loads(result['body'])
        assert response_body['success'] == True

        # 验证数据一致性
        with psycopg2.connect(**test_environment.test_db_config) as conn:
            with conn.cursor() as cur:
                cur.execute("SELECT COUNT(*) FROM service_names")
                final_count = cur.fetchone()[0]
                # 增量同步不应该显著改变记录数量
                assert abs(final_count - initial_count) <= 5
```

### 9.4 性能测试设计

#### 9.4.1 负载测试

```python
class TestPerformance:
    """性能测试套件"""

    def test_batch_processing_performance(self):
        """测试批量处理性能"""
        # 生成大量测试数据
        test_services = [
            ServiceInfo(
                authoritative_full_name=f"Test Service {i}",
                base_name=f"Test Service {i}",
                full_name_en=f"Test Service {i}",
                short_name_en=f"TS{i}",
                service_code=f"ts{i}"
            )
            for i in range(1000)
        ]

        # 测试批量UPSERT性能
        start_time = time.time()

        rds_client = RDSClient()
        success_count, failed_services = rds_client.batch_upsert_services(test_services)

        end_time = time.time()
        processing_time = end_time - start_time

        # 性能断言
        assert success_count == 1000
        assert len(failed_services) == 0
        assert processing_time < 30  # 应在30秒内完成

        # 计算处理速率
        processing_rate = success_count / processing_time
        assert processing_rate > 30  # 每秒至少处理30条记录

        logger.info(f"批量处理性能: {processing_rate:.2f} 记录/秒")

    def test_memory_usage_under_load(self):
        """测试负载下的内存使用"""
        import psutil
        import os

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss

        # 处理大量数据
        large_dataset = self.generate_large_test_dataset(5000)
        processor = MemoryOptimizedProcessor()

        processed_count = processor.process_large_dataset_with_memory_optimization(large_dataset)

        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory

        # 内存使用断言
        assert processed_count == 5000
        assert memory_increase < 100 * 1024 * 1024  # 内存增长不超过100MB

        logger.info(f"内存使用增长: {memory_increase / 1024 / 1024:.2f}MB")

    def test_concurrent_processing_performance(self):
        """测试并发处理性能"""
        urls = [
            "https://example.com/page1",
            "https://example.com/page2",
            "https://example.com/page3",
            "https://example.com/page4",
            "https://example.com/page5"
        ]

        concurrent_processor = ConcurrentProcessor()

        # 测试并发处理
        start_time = time.time()

        with patch('aiohttp.ClientSession.get') as mock_get:
            # 模拟网络响应
            mock_response = MagicMock()
            mock_response.text = asyncio.coroutine(lambda: "<html>test</html>")()
            mock_get.return_value.__aenter__.return_value = mock_response

            results = asyncio.run(concurrent_processor.concurrent_web_scraping(urls))

        end_time = time.time()
        processing_time = end_time - start_time

        # 并发性能断言
        assert len(results) == len(urls)
        assert processing_time < 10  # 并发处理应在10秒内完成

        logger.info(f"并发处理时间: {processing_time:.2f}秒")
```

---

## 10. 运维和维护

### 10.1 监控和告警

#### 10.1.1 关键指标监控

```python
class OperationalMetrics:
    """运维监控指标"""

    def __init__(self):
        self.cloudwatch = boto3.client('cloudwatch', region_name='cn-northwest-1')
        self.namespace = 'AWS/ServiceSync'

    def collect_operational_metrics(self) -> Dict[str, Any]:
        """收集运维指标"""
        metrics = {
            'sync_health': self.check_sync_health(),
            'data_quality': self.check_data_quality(),
            'performance': self.check_performance_metrics(),
            'error_rates': self.check_error_rates(),
            'resource_usage': self.check_resource_usage()
        }

        # 发送到CloudWatch
        self.send_metrics_to_cloudwatch(metrics)

        return metrics

    def check_sync_health(self) -> Dict[str, Any]:
        """检查同步健康状态"""
        with get_db_connection() as conn:
            with conn.cursor() as cur:
                # 检查最近同步时间
                cur.execute("""
                    SELECT MAX(last_synced_at) as last_sync,
                           COUNT(*) as total_services,
                           COUNT(CASE WHEN is_active THEN 1 END) as active_services
                    FROM service_names
                """)
                result = cur.fetchone()

                last_sync = result[0]
                total_services = result[1]
                active_services = result[2]

                # 计算健康分数
                health_score = 100
                if last_sync:
                    hours_since_sync = (datetime.utcnow() - last_sync).total_seconds() / 3600
                    if hours_since_sync > 48:  # 超过48小时未同步
                        health_score -= 50
                    elif hours_since_sync > 24:  # 超过24小时未同步
                        health_score -= 25

                if active_services < total_services * 0.8:  # 活跃服务少于80%
                    health_score -= 30

                return {
                    'health_score': health_score,
                    'last_sync': last_sync.isoformat() if last_sync else None,
                    'hours_since_sync': hours_since_sync if last_sync else None,
                    'total_services': total_services,
                    'active_services': active_services,
                    'status': 'healthy' if health_score >= 80 else 'degraded' if health_score >= 50 else 'unhealthy'
                }

    def setup_cloudwatch_alarms(self):
        """设置CloudWatch告警"""
        alarms = [
            {
                'AlarmName': 'AWSServiceSync-SyncFailure',
                'MetricName': 'SyncFailures',
                'Threshold': 1,
                'ComparisonOperator': 'GreaterThanOrEqualToThreshold',
                'AlarmDescription': 'AWS服务同步失败告警'
            },
            {
                'AlarmName': 'AWSServiceSync-DataQualityLow',
                'MetricName': 'DataQualityScore',
                'Threshold': 80,
                'ComparisonOperator': 'LessThanThreshold',
                'AlarmDescription': '数据质量分数过低告警'
            },
            {
                'AlarmName': 'AWSServiceSync-HighErrorRate',
                'MetricName': 'ErrorRate',
                'Threshold': 10,
                'ComparisonOperator': 'GreaterThanThreshold',
                'AlarmDescription': '错误率过高告警'
            }
        ]

        for alarm_config in alarms:
            self.cloudwatch.put_metric_alarm(
                AlarmName=alarm_config['AlarmName'],
                ComparisonOperator=alarm_config['ComparisonOperator'],
                EvaluationPeriods=2,
                MetricName=alarm_config['MetricName'],
                Namespace=self.namespace,
                Period=300,
                Statistic='Average',
                Threshold=alarm_config['Threshold'],
                ActionsEnabled=True,
                AlarmActions=[
                    'arn:aws-cn:sns:cn-northwest-1:************:aws-service-sync-alerts'
                ],
                AlarmDescription=alarm_config['AlarmDescription']
            )
```

### 10.2 故障排除指南

#### 10.2.1 常见问题诊断

```python
class TroubleshootingGuide:
    """故障排除指南"""

    def diagnose_sync_issues(self) -> Dict[str, Any]:
        """诊断同步问题"""
        diagnosis = {
            'timestamp': datetime.utcnow().isoformat(),
            'issues_found': [],
            'recommendations': [],
            'severity': 'info'
        }

        # 检查数据库连接
        db_status = self.check_database_connectivity()
        if not db_status['connected']:
            diagnosis['issues_found'].append({
                'type': 'database_connection',
                'message': '数据库连接失败',
                'details': db_status['error']
            })
            diagnosis['recommendations'].append('检查数据库连接配置和网络连通性')
            diagnosis['severity'] = 'critical'

        # 检查AWS服务访问
        aws_status = self.check_aws_services_access()
        if not aws_status['secrets_manager']:
            diagnosis['issues_found'].append({
                'type': 'secrets_manager_access',
                'message': 'Secrets Manager访问失败'
            })
            diagnosis['recommendations'].append('检查IAM权限和Secrets Manager配置')
            diagnosis['severity'] = max(diagnosis['severity'], 'error')

        # 检查数据源可用性
        data_sources_status = self.check_data_sources()
        if not data_sources_status['web_accessible']:
            diagnosis['issues_found'].append({
                'type': 'web_source_unavailable',
                'message': 'AWS官网无法访问'
            })
            diagnosis['recommendations'].append('检查网络连接和目标URL状态')

        if not data_sources_status['s3_accessible']:
            diagnosis['issues_found'].append({
                'type': 's3_source_unavailable',
                'message': 'S3 PDF文档无法访问'
            })
            diagnosis['recommendations'].append('检查S3权限和文件路径')

        # 检查数据质量
        quality_status = self.check_data_quality_issues()
        if quality_status['quality_score'] < 80:
            diagnosis['issues_found'].append({
                'type': 'data_quality_low',
                'message': f'数据质量分数过低: {quality_status["quality_score"]}'
            })
            diagnosis['recommendations'].append('检查数据源变化和处理逻辑')

        return diagnosis

    def generate_health_report(self) -> str:
        """生成健康状况报告"""
        diagnosis = self.diagnose_sync_issues()
        operational_metrics = OperationalMetrics().collect_operational_metrics()

        report = f"""
# AWS服务同步模块健康报告

**生成时间**: {diagnosis['timestamp']}
**整体状态**: {diagnosis['severity'].upper()}

## 系统健康指标

- **同步健康分数**: {operational_metrics['sync_health']['health_score']}/100
- **数据质量分数**: {operational_metrics['data_quality']['overall_score']}/100
- **活跃服务数量**: {operational_metrics['sync_health']['active_services']}
- **最近同步时间**: {operational_metrics['sync_health']['last_sync']}

## 发现的问题

"""

        if diagnosis['issues_found']:
            for issue in diagnosis['issues_found']:
                report += f"- **{issue['type']}**: {issue['message']}\n"
        else:
            report += "- 未发现问题\n"

        report += "\n## 建议措施\n\n"

        if diagnosis['recommendations']:
            for rec in diagnosis['recommendations']:
                report += f"- {rec}\n"
        else:
            report += "- 系统运行正常，无需特殊措施\n"

        return report
```

### 10.3 维护任务

#### 10.3.1 定期维护脚本

```python
class MaintenanceTasks:
    """定期维护任务"""

    def __init__(self):
        self.db_client = RDSClient()

    def cleanup_old_patterns(self, days_to_keep: int = 30):
        """清理旧的正则表达式模式"""
        cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)

        with self.db_client.get_db_connection() as conn:
            with conn.cursor() as cur:
                # 标记旧模式为非活跃
                cur.execute("""
                    UPDATE regex_patterns
                    SET is_active = FALSE, updated_at = NOW()
                    WHERE created_at < %s
                      AND is_active = TRUE
                      AND validation_status = 'invalid'
                """, (cutoff_date,))

                deactivated_count = cur.rowcount

                # 删除非常旧的无效模式
                very_old_date = datetime.utcnow() - timedelta(days=days_to_keep * 2)
                cur.execute("""
                    DELETE FROM regex_patterns
                    WHERE created_at < %s
                      AND is_active = FALSE
                      AND validation_status = 'invalid'
                """, (very_old_date,))

                deleted_count = cur.rowcount
                conn.commit()

        logger.info(f"维护完成: 停用 {deactivated_count} 个模式, 删除 {deleted_count} 个旧模式")

        return {
            'deactivated_patterns': deactivated_count,
            'deleted_patterns': deleted_count
        }

    def optimize_database_performance(self):
        """优化数据库性能"""
        with self.db_client.get_db_connection() as conn:
            with conn.cursor() as cur:
                # 更新表统计信息
                cur.execute("ANALYZE service_names;")
                cur.execute("ANALYZE regex_patterns;")

                # 重建索引（如果需要）
                cur.execute("""
                    REINDEX INDEX CONCURRENTLY idx_regex_patterns_active_valid;
                """)

                # 清理死元组
                cur.execute("VACUUM ANALYZE service_names;")
                cur.execute("VACUUM ANALYZE regex_patterns;")

                conn.commit()

        logger.info("数据库性能优化完成")

    def backup_critical_data(self):
        """备份关键数据"""
        backup_timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')

        with self.db_client.get_db_connection() as conn:
            with conn.cursor() as cur:
                # 导出活跃服务数据
                cur.execute("""
                    SELECT authoritative_full_name, base_name, full_name_en,
                           short_name_en, service_code, last_synced_at
                    FROM service_names
                    WHERE is_active = TRUE
                    ORDER BY authoritative_full_name
                """)

                services_data = cur.fetchall()

                # 导出活跃模式数据
                cur.execute("""
                    SELECT pattern_name, pattern_type, regex_string,
                           service_code, priority, metadata
                    FROM regex_patterns
                    WHERE is_active = TRUE AND validation_status = 'valid'
                    ORDER BY priority DESC, pattern_name
                """)

                patterns_data = cur.fetchall()

        # 保存备份文件
        backup_data = {
            'timestamp': backup_timestamp,
            'services': [dict(zip(['authoritative_full_name', 'base_name', 'full_name_en',
                                 'short_name_en', 'service_code', 'last_synced_at'], row))
                        for row in services_data],
            'patterns': [dict(zip(['pattern_name', 'pattern_type', 'regex_string',
                                 'service_code', 'priority', 'metadata'], row))
                        for row in patterns_data]
        }

        backup_filename = f"aws_service_sync_backup_{backup_timestamp}.json"
        with open(f"/tmp/{backup_filename}", 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, indent=2, default=str)

        # 上传到S3
        s3_client = boto3.client('s3', region_name='cn-northwest-1')
        s3_client.upload_file(
            f"/tmp/{backup_filename}",
            "mass-email-translator-feedback",
            f"backups/aws-service-sync/{backup_filename}"
        )

        logger.info(f"数据备份完成: {backup_filename}")

        return {
            'backup_file': backup_filename,
            'services_count': len(services_data),
            'patterns_count': len(patterns_data)
        }

# 定期维护任务调度
def scheduled_maintenance():
    """定期维护任务入口"""
    maintenance = MaintenanceTasks()

    try:
        # 每周执行的维护任务
        cleanup_result = maintenance.cleanup_old_patterns()
        optimization_result = maintenance.optimize_database_performance()
        backup_result = maintenance.backup_critical_data()

        # 生成维护报告
        maintenance_report = {
            'timestamp': datetime.utcnow().isoformat(),
            'cleanup': cleanup_result,
            'optimization': 'completed',
            'backup': backup_result,
            'status': 'success'
        }

        logger.info(f"定期维护完成: {maintenance_report}")

        return maintenance_report

    except Exception as e:
        logger.error(f"定期维护失败: {e}")
        raise
```

        return maintenance_report

    except Exception as e:
        logger.error(f"定期维护失败: {e}")
        raise
```

---

## 总结

本设计文档详细描述了AWS服务名称同步模块v2.0的完整架构和实现方案。该模块作为一个完全独立的数据同步服务，通过数据库中介模式为其他系统提供标准化的AWS服务名称数据支持。

### 核心特性总结

1. **完全独立性**: 模块不依赖其他系统，支持独立部署和维护
2. **数据库中介模式**: 通过PostgreSQL v2.0数据库实现系统间解耦
3. **8种核心模式**: 完整的正则表达式模式体系，覆盖所有使用场景
4. **性能优化**: 10-50x查询性能提升，通过部分索引和JSONB优化
5. **智能同步**: 支持增量和全量同步策略，智能冲突解决
6. **数据质量保证**: 完整的验证、监控和自动修复机制
7. **运维友好**: 结构化日志、CloudWatch监控、自动化部署

### 技术亮点

- **JSONB元数据**: 灵活的元数据存储和高效查询
- **部分索引优化**: 显著减少索引大小和提升查询性能
- **边界保护机制**: 统一的CONTEXT_PROTECTED模式避免误匹配
- **分层错误处理**: 完善的错误分类和恢复策略
- **多层缓存**: 内存、文件、数据库三级缓存架构
- **并发优化**: 异步处理和连接池管理

### 架构定位确认

该设计确保系统具备高性能、高可靠性与易维护性，完全符合本模块"**独立数据同步服务、数据库中介模式、手动部署**"的最新架构定位与需求目标。设计文档为开发团队提供了完整的实现指导，确保模块能够高效、稳定地运行，并为未来的扩展和维护奠定了坚实的基础。

### 核心接口定义

#### 1. 配置管理接口

```python
class ConfigManager:
    """AWS Secrets Manager配置管理器"""
    
    def __init__(self):
        self.secrets_client = boto3.client('secretsmanager', region_name='cn-northwest-1')
        self.secret_arn = os.environ.get('SECRETS_MANAGER_ARN')
        self._config_cache = None
        self._cache_ttl = 3600  # 1小时TTL
    
    def get_config(self) -> Dict[str, Any]:
        """从Secrets Manager获取配置信息，支持缓存"""
        pass
    
    def get_database_config(self) -> Dict[str, str]:
        """获取数据库连接配置"""
        pass
    
    def get_s3_pdf_path(self) -> Tuple[str, str]:
        """解析S3 PDF路径，返回(bucket, key)"""
        pass
```

#### 2. 网页抓取接口

```python
class WebScraper:
    """AWS中国区官网服务名称抓取器"""
    
    def __init__(self):
        self.target_url = "https://www.amazonaws.cn/about-aws/regional-product-services/"
        self.session = requests.Session()
    
    def scrape_services(self) -> List[str]:
        """抓取官网服务名称列表，返回'提供的服务'字段值"""
        pass
    
    def validate_scraped_data(self, services: List[str]) -> bool:
        """验证抓取数据的完整性（±30%变化范围）"""
        pass
```

#### 3. PDF解析接口

```python
class PDFParser:
    """S3 PDF文档解析器"""
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.s3_client = boto3.client('s3', region_name='cn-northwest-1')
    
    def parse_pdf(self) -> List[ServiceInfo]:
        """解析S3中的PDF文件，提取三列数据"""
        pass
    
    def download_pdf_from_s3(self, bucket: str, key: str) -> bytes:
        """从S3下载PDF文件"""
        pass
    
    def extract_three_columns(self, pdf_content: bytes) -> List[Dict[str, str]]:
        """提取PDF中的三列数据：AWS offering, Long name, Short name"""
        pass
    
    def standardize_pdf_data(self, raw_data: List[Dict]) -> List[ServiceInfo]:
        """标准化PDF数据（AWS->Amazon替换等）"""
        pass
```

#### 4. 数据处理接口

```python
class DataProcessor:
    """多数据源智能匹配和处理器"""
    
    def process_services(self, web_services: List[str], pdf_services: List[ServiceInfo]) -> List[ServiceInfo]:
        """处理和合并多数据源服务数据"""
        pass
    
    def intelligent_matching(self, web_name: str, pdf_services: List[ServiceInfo]) -> Optional[ServiceInfo]:
        """智能匹配算法：精确匹配、模糊匹配、正则匹配"""
        pass
    
    def generate_service_info(self, authoritative_name: str, pdf_match: Optional[ServiceInfo]) -> ServiceInfo:
        """生成标准化的ServiceInfo对象"""
        pass
    
    def derive_base_name(self, authoritative_name: str) -> str:
        """从权威全称推导base_name（移除括号）"""
        pass
    
    def generate_service_code(self, internal_name: Optional[str]) -> Optional[str]:
        """基于internal_name生成service_code"""
        pass
```

#### 5. 正则表达式模式生成接口

```python
class RegexPatternGenerator:
    """8种核心正则表达式模式生成器"""
    
    def generate_comprehensive_service_patterns(self, service_data: ServiceInfo) -> List[RegexPattern]:
        """为单个服务生成完整的8种模式类型"""
        pass
    
    def generate_full_complex_suffix_pattern(self, service_data: ServiceInfo) -> RegexPattern:
        """生成全称复合后缀模式 (优先级: 120)"""
        pass
    
    def generate_full_standard_pattern(self, service_data: ServiceInfo) -> RegexPattern:
        """生成全称标准模式 (优先级: 115)"""
        pass
    
    def generate_short_complex_suffix_pattern(self, service_data: ServiceInfo) -> RegexPattern:
        """生成简称复合后缀模式 (优先级: 110)"""
        pass
    
    def generate_short_standard_pattern(self, service_data: ServiceInfo) -> RegexPattern:
        """生成简称标准模式 (优先级: 105)"""
        pass
    
    def generate_acronym_complex_suffix_pattern(self, service_data: ServiceInfo) -> RegexPattern:
        """生成缩写复合后缀模式 (优先级: 100)"""
        pass
    
    def generate_acronym_standard_pattern(self, service_data: ServiceInfo) -> RegexPattern:
        """生成缩写标准模式 (优先级: 95)"""
        pass
    
    def generate_special_variant_patterns(self, service_data: ServiceInfo) -> List[RegexPattern]:
        """生成特殊变体模式 (优先级: 125)"""
        pass
    
    def generate_context_protected_pattern(self, service_data: ServiceInfo) -> List[RegexPattern]:
        """生成上下文保护模式 (优先级: 90)"""
        pass
    
    def generate_boundary_protected_pattern(self, service_name: str) -> str:
        """生成带完整边界保护的模式"""
        pass
```

#### 6. 数据库存储接口

```python
class RDSClient:
    """RDS PostgreSQL数据库客户端"""
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.connection_pool = None
    
    def store_services(self, services: List[ServiceInfo]) -> int:
        """批量存储服务数据，使用UPSERT操作"""
        pass
    
    def upsert_service(self, service: ServiceInfo) -> bool:
        """单个服务的UPSERT操作，使用authoritative_full_name作为业务主键"""
        pass
    
    def batch_upsert_services(self, services: List[ServiceInfo]) -> int:
        """批量UPSERT操作，使用psycopg2.extras.execute_values"""
        pass
    
    def store_regex_patterns(self, patterns: List[RegexPattern]) -> Tuple[int, List[str]]:
        """存储正则表达式模式，返回(验证通过数量, 冲突列表)"""
        pass
    
    def validate_pattern_syntax(self, pattern: RegexPattern) -> Tuple[bool, Optional[str]]:
        """验证正则表达式语法正确性"""
        pass
    
    def detect_pattern_conflicts(self, patterns: List[RegexPattern]) -> List[Dict[str, Any]]:
        """检测模式之间的潜在冲突"""
        pass
```

### 数据流处理

#### 主要数据流

```mermaid
sequenceDiagram
    participant EB as EventBridge
    participant LF as Lambda Function
    participant CM as ConfigManager
    participant WS as WebScraper
    participant PP as PDFParser
    participant DP as DataProcessor
    participant RPG as RegexPatternGenerator
    participant RDS as RDS Database
    
    EB->>LF: 触发同步任务
    LF->>CM: 获取配置信息
    CM->>LF: 返回数据库和S3配置
    
    par 并行数据获取
        LF->>WS: 抓取网页数据
        WS->>LF: 返回服务名称列表
    and
        LF->>PP: 解析PDF数据
        PP->>LF: 返回三列数据
    end
    
    LF->>DP: 智能匹配和处理
    DP->>LF: 返回标准化服务数据
    
    LF->>RDS: 存储服务数据
    RDS->>LF: 确认存储成功
    
    LF->>RPG: 生成正则表达式模式
    RPG->>LF: 返回8种模式类型
    
    LF->>RDS: 存储正则模式
    RDS->>LF: 确认模式存储
    
    LF->>EB: 返回执行结果
```

## 数据模型

### 核心数据结构

#### ServiceInfo数据模型

```python
from dataclasses import dataclass
from datetime import datetime
from typing import Optional
import re

@dataclass
class ServiceInfo:
    """服务信息数据模型 - v2架构"""
    
    # 核心字段 - 字段职责分离
    authoritative_full_name: str    # 来自网页的权威服务全称（业务主键）
    base_name: str                  # 用于翻译逻辑状态跟踪的规范化基础名称
    internal_name: Optional[str]    # 来自PDF的"AWS offering (internal name)"
    full_name_en: str              # 首次提及时的英文全称
    short_name_en: str             # 后续提及时的英文简称
    
    # 扩展字段
    full_name_cn: Optional[str] = None     # 中文全称（预留）
    short_name_cn: Optional[str] = None    # 中文简称（预留）
    service_code: Optional[str] = None     # AWS官方服务代码
    source: str = 'web_scrape'             # 数据来源
    is_active: bool = True                 # 是否启用
    notes: Optional[str] = None            # 备注信息
    last_synced_at: Optional[datetime] = None  # 上次同步时间
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.base_name:
            self.base_name = self.derive_base_name()
        if not self.service_code and self.internal_name:
            self.service_code = self.generate_service_code()
        if not self.full_name_en:
            self.full_name_en = self.authoritative_full_name
        if not self.short_name_en:
            self.short_name_en = self.authoritative_full_name
    
    def derive_base_name(self) -> str:
        """从authoritative_full_name推导base_name，移除括号内的缩写"""
        return re.sub(r'\s*\([^)]*\)', '', self.authoritative_full_name).strip()
    
    def generate_service_code(self) -> Optional[str]:
        """基于internal_name生成service_code
        
        处理规则：
        - 转换为小写
        - 移除非字母数字字符
        - 保留中间空格（与需求2第4条保持一致）
        
        用途：
        - 系统内部标识
        - 正则表达式模式分组管理
        """
        if not self.internal_name:
            return None
        
        # 转换为小写
        code = self.internal_name.lower()
        
        # 移除非字母数字字符，但保留空格
        code = re.sub(r'[^a-zA-Z0-9\s]', '', code)
        
        # 去除开头和结尾的空格，保留中间空格
        code = code.strip()
        
        return code if code else None
```

#### RegexPattern数据模型

```python
@dataclass
class RegexPattern:
    """正则表达式模式数据模型"""

    pattern_name: str               # 模式的易读名称
    pattern_type: str               # 模式类型 ENUM 语义 (SERVICE_NAME, TIMEZONE, CLI_COMMAND, URL, GENERAL, CONTEXT_PROTECTED)
    regex_string: str               # 正则表达式本身
    related_service_id: Optional[int] # 关联的服务ID
    service_code: Optional[str]     # 服务代码
    priority: int = 100             # 匹配优先级（90-130）
    metadata: Optional[Dict[str, Any]] = None   # JSONB 对应结构，如 isCompoundWithSuffix, suffixGroup, patternCategory, hasBoundaryProtection
    is_active: bool = True          # 是否启用
    validation_status: str = 'pending'  # 验证状态：pending/valid/invalid
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
```

### 数据库架构设计

#### 核心表结构

**1. service_names表 (v2架构)**

```sql
CREATE TABLE service_names (
    id BIGSERIAL PRIMARY KEY,
    
    -- 业务主键：来自网页的权威全称
    authoritative_full_name VARCHAR(255) NOT NULL UNIQUE,
    
    -- 翻译逻辑用：规范化基础名称（不含括号）
    base_name VARCHAR(255) NOT NULL,
    
    -- PDF数据：内部名称
    internal_name VARCHAR(255),
    
    -- 英文名称：首次和后续提及
    full_name_en VARCHAR(255) NOT NULL,    -- 与authoritative_full_name一致
    short_name_en VARCHAR(100) NOT NULL,   -- 来自PDF或等于全称
    
    -- 中文名称（预留）
    full_name_cn VARCHAR(255),
    short_name_cn VARCHAR(100),
    
    -- 元数据
    service_code VARCHAR(50),              -- AWS服务代码
    source rule_source NOT NULL DEFAULT 'manual',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    notes TEXT,
    last_synced_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 关键索引
CREATE INDEX idx_service_names_base_name ON service_names(base_name);
CREATE INDEX idx_service_names_service_code ON service_names(service_code);
CREATE INDEX idx_service_names_source ON service_names(source);
CREATE INDEX idx_service_names_last_synced ON service_names(last_synced_at);
```

**2. regex_patterns表 (8种模式类型)**

```sql
CREATE TABLE regex_patterns (
    id BIGSERIAL PRIMARY KEY,
    pattern_name VARCHAR(100) NOT NULL UNIQUE,
    pattern_type VARCHAR(50) NOT NULL,     -- SERVICE_NAME, TIMEZONE, etc.
    regex_string TEXT NOT NULL,
    related_service_id BIGINT REFERENCES service_names(id),
    service_code VARCHAR(50),
    priority INTEGER NOT NULL DEFAULT 100, -- 90-130优先级范围
    notes TEXT,                            -- 模式元数据
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    validation_status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 优化索引
CREATE INDEX idx_regex_patterns_priority ON regex_patterns(priority DESC, id ASC);
CREATE INDEX idx_regex_patterns_service ON regex_patterns(related_service_id);
CREATE INDEX idx_regex_patterns_type_active ON regex_patterns(pattern_type, is_active);
CREATE INDEX idx_regex_patterns_service_code ON regex_patterns(service_code);
```

#### 字段职责完整说明

**service_code字段的完整说明：**
- **数据来源**：基于PDF中的"AWS offering (internal name)"字段
- **处理规则**：
  - 转换为小写
  - 移除非字母数字字符
  - 保留中间空格（与需求2第4条保持一致）
- **用途说明**：
  - 系统内部标识
  - 正则表达式模式分组管理

**字段职责分离架构：**

| 字段 | 职责 | 用途 |
|------|------|------|
| `authoritative_full_name` | 数据同步的业务主键 | 作为UPSERT操作的唯一业务键 |
| `base_name` | 翻译逻辑状态跟踪 | 用于翻译系统的状态跟踪和分组 |
| `full_name_en` | 首次提及替换值 | 提供首次提及时的替换值 |
| `short_name_en` | 后续提及替换值 | 提供后续提及时的替换值 |
| `internal_name` | PDF原始数据保留 | 保留原始PDF数据用于追溯 |
| `service_code` | 系统内部标识和模式分组管理 | 用于系统内部标识和正则表达式模式分组 |

### 8种核心正则表达式模式类型

#### 模式类型定义

| 优先级 | 模式类型 | 描述 | 示例 |
|--------|----------|------|------|
| 125 | 特殊变体模式 | 处理特殊服务变体 | `Aurora PostgreSQL` |
| 120 | 全称复合后缀模式 | 完整名称+复杂后缀 | `Amazon EC2 P3 instances` |
| 115 | 全称标准模式 | 精确匹配+边界保护 | `Amazon Elastic Compute Cloud (EC2)` |
| 110 | 简称复合后缀模式 | 简称+复杂后缀 | `Amazon EC2 instances` |
| 105 | 简称标准模式 | 简称+边界保护 | `Amazon EC2` |
| 100 | 缩写复合后缀模式 | 缩写+后缀 | `EC2 instances` |
| 95 | 缩写标准模式 | 纯缩写+边界保护 | `EC2` |
| 90 | 上下文保护模式 | 避免误匹配 | 避免ARN、URL中匹配 |

#### 边界保护机制

```python
# v2.0 统一边界保护原则
# - 模式本体禁止使用可变宽度负向后行（Python re 不支持）
# - 通过 CONTEXT_PROTECTED 基线模式统一承载上下文保护
# - 业务模式仅保留固定宽度的轻量保护（如 (?<![:_-]) 与 (?![:_-])）

def generate_boundary_protected_pattern(service_name: str) -> str:
    """生成带轻量固定宽度边界保护的业务模式（上下文保护由CONTEXT_PROTECTED承担）"""
    code_protection = r'(?<![:_-])'
    trailing_protection = r'(?![:_-])'
    return f"{code_protection}\\b{re.escape(service_name)}\\b{trailing_protection}"
```

#### 特殊服务处理策略

**Aurora服务族**
```python
def generate_aurora_patterns() -> List[RegexPattern]:
    """生成Aurora服务的特殊模式"""
    patterns = []
    
    # 通用Aurora模式（排除特定引擎）
    patterns.append(RegexPattern(
        pattern_name='AURORA_GENERAL',
        pattern_type='SERVICE_NAME',
        regex_string=r'(?<![:_-])\bAurora\b(?![\s-](?:PostgreSQL|MySQL)\b)',
        priority=125,
        notes='General Aurora pattern excluding specific variants'
    ))
    
    # 特定引擎变体
    for engine in ['PostgreSQL', 'MySQL']:
        patterns.append(RegexPattern(
            pattern_name=f'AURORA_{engine.upper()}_VARIANT',
            pattern_type='SERVICE_NAME',
            regex_string=f"(?:Amazon\\s+Aurora|AWS\\s+Aurora|\\bAurora)\\s+{engine}\\b",
            priority=130,
            notes=f'Aurora {engine} specific variant'
        ))
    
    return patterns
```

**RDS服务族**
```python
def generate_rds_patterns() -> List[RegexPattern]:
    """生成RDS服务的特殊模式"""
    engines = ['PostgreSQL', 'MySQL', 'MariaDB', 'Oracle', 'SQL Server']
    patterns = []
    
    # RDS for Engine模式
    for engine in engines:
        patterns.append(RegexPattern(
            pattern_name=f'RDS_FOR_{engine.replace(" ", "_").upper()}',
            pattern_type='SERVICE_NAME',
            regex_string=f"(?:Amazon\\s+RDS|AWS\\s+RDS|\\bRDS)\\s+for\\s+{re.escape(engine)}",
            priority=125,
            notes=f'RDS for {engine} specific pattern'
        ))
    
    # 通用RDS模式（排除特定引擎）
    engine_exclusion = '|'.join([f'for\\s+{re.escape(engine)}' for engine in engines])
    patterns.append(RegexPattern(
        pattern_name='RDS_GENERAL',
        pattern_type='SERVICE_NAME',
        regex_string=f"(?:Amazon\\s+Relational\\s+Database\\s+Service|Amazon\\s+RDS|AWS\\s+RDS|\\bRDS)(?!\\s+(?:{engine_exclusion}))",
        priority=115,
        notes='General RDS pattern excluding specific engine variants'
    ))
    
    return patterns
```

## 正则表达式模式生成系统

### 模式生成策略

AWS服务同步系统的核心职责是生成和维护正则表达式模式，这些模式由上游系统直接从数据库读取并消费；本系统不提供任何对外接口。系统遵循功能精简原则，专注于模式的生成、验证和存储。

#### 8种核心模式类型

基于需求文档，系统需要生成8种核心正则表达式模式类型：

1. **全称复合后缀模式** (优先级: 120) - 匹配完整服务名称 + 复杂后缀
2. **全称标准模式** (优先级: 115) - 精确匹配完整服务名称，带边界保护
3. **简称复合后缀模式** (优先级: 110) - 匹配简称 + 复杂后缀
4. **简称标准模式** (优先级: 105) - 精确匹配服务简称，带边界保护
5. **缩写复合后缀模式** (优先级: 100) - 匹配纯缩写 + 后缀
6. **缩写标准模式** (优先级: 95) - 精确匹配纯缩写，带边界保护
7. **特殊变体模式** (优先级: 125) - 处理特殊服务变体（如Aurora PostgreSQL）
8. **上下文保护模式** (优先级: 90) - 在特定上下文中避免误匹配

#### 模式生成实现

```python
class RegexPatternGenerator:
    """正则表达式模式生成器 - 专注于模式生成和验证"""
    
    def __init__(self):
        self.pattern_templates = self._load_pattern_templates()
        self.boundary_protection = self._load_boundary_protection_rules()
    
    def generate_comprehensive_service_patterns(self, service_data: ServiceInfo) -> List[RegexPattern]:
        """为单个服务生成完整的8种模式类型"""
        patterns = []
        
        # 1. 全称复合后缀模式
        if self._supports_complex_suffix(service_data):
            patterns.append(self._generate_full_complex_suffix_pattern(service_data))
        
        # 2. 全称标准模式
        patterns.append(self._generate_full_standard_pattern(service_data))
        
        # 3. 简称复合后缀模式
        if self._supports_complex_suffix(service_data):
            patterns.append(self._generate_short_complex_suffix_pattern(service_data))
        
        # 4. 简称标准模式
        patterns.append(self._generate_short_standard_pattern(service_data))
        
        # 5. 缩写复合后缀模式
        if self._supports_complex_suffix(service_data) and self._has_acronym(service_data):
            patterns.append(self._generate_acronym_complex_suffix_pattern(service_data))
        
        # 6. 缩写标准模式
        if self._has_acronym(service_data):
            patterns.append(self._generate_acronym_standard_pattern(service_data))
        
        # 7. 特殊变体模式
        if self._has_special_variants(service_data):
            patterns.extend(self._generate_special_variant_patterns(service_data))
        
        # 8. 上下文保护模式
        patterns.extend(self._generate_context_protected_patterns(service_data))
        
        return patterns
    
    def _generate_full_standard_pattern(self, service_data: ServiceInfo) -> RegexPattern:
        """生成全称标准模式"""
        full_name = service_data.full_name_en
        protected_pattern = self._apply_boundary_protection(full_name)
        
        return RegexPattern(
            pattern_name=f"{self._get_service_acronym(service_data)}_FULL_STANDARD",
            pattern_type='SERVICE_NAME',
            regex_string=protected_pattern,
            related_service_id=service_data.id,
            service_code=service_data.service_code,
            priority=115,
            notes='Standard full name pattern with boundary protection'
        )
    
    def _apply_boundary_protection(self, service_name: str) -> str:
        """应用边界保护机制"""
        # ARN保护
        # v2.0 统一边界保护原则：上下文保护由 CONTEXT_PROTECTED 承担，业务模式仅保留轻量保护
        code_protection = r'(?<![:_-])'
        trailing_protection = r'(?![:_-])'
        escaped_name = re.escape(service_name)
        return f"{code_protection}\\b{escaped_name}\\b{trailing_protection}"
    
    def validate_pattern_syntax(self, pattern: RegexPattern) -> Tuple[bool, Optional[str]]:
        """验证正则表达式语法"""
        try:
            re.compile(pattern.regex_string)
            return True, None
        except re.error as e:
            return False, str(e)
    
    def _supports_complex_suffix(self, service_data: ServiceInfo) -> bool:
        """判断服务是否支持复杂后缀"""
        suffix_supported_services = ['ec2', 'rds', 'ecs', 'eks', 'ebs']
        return service_data.service_code in suffix_supported_services
    
    def _has_special_variants(self, service_data: ServiceInfo) -> bool:
        """判断服务是否有特殊变体"""
        variant_services = ['aurora', 'rds', 'health']
        return service_data.service_code in variant_services
    
    def _get_service_acronym(self, service_data: ServiceInfo) -> str:
        """从服务名称中提取缩写"""
        match = re.search(r'\(([^)]+)\)', service_data.full_name_en)
        if match:
            return match.group(1)
        
        # 如果没有括号，尝试从首字母生成
        words = service_data.full_name_en.replace('Amazon ', '').replace('AWS ', '').split()
        return ''.join([word[0].upper() for word in words if word[0].isupper()])
```

### 特殊服务处理系统

```python
class SpecialServiceHandler:
    """特殊服务处理系统 - 为Aurora、Health、RDS等特殊服务提供专门处理策略"""
    
    def __init__(self):
        self.special_service_configs = self._load_special_service_configs()
        self.variant_patterns = {}
    
    def identify_special_service(self, service_info: ServiceInfo) -> str:
        """识别特殊服务类型"""
        service_name_lower = service_info.authoritative_full_name.lower()
        
        if "aurora" in service_name_lower:
            return "aurora"
        elif "health" in service_name_lower:
            return "health"
        elif "rds" in service_name_lower or "relational database" in service_name_lower:
            return "rds"
        elif "lambda" in service_name_lower:
            return "lambda"
        elif "elastic compute cloud" in service_name_lower or "ec2" in service_name_lower:
            return "ec2"
        else:
            return "normal"
    
    def generate_special_patterns(self, service_info: ServiceInfo, special_type: str) -> List[RegexPattern]:
        """为特殊服务生成专门的模式"""
        if special_type == "aurora":
            return self._generate_aurora_patterns(service_info)
        elif special_type == "health":
            return self._generate_health_patterns(service_info)
        elif special_type == "rds":
            return self._generate_rds_patterns(service_info)
        elif special_type == "lambda":
            return self._generate_lambda_patterns(service_info)
        elif special_type == "ec2":
            return self._generate_ec2_patterns(service_info)
        else:
            return []
    
    def _generate_aurora_patterns(self, service_info: ServiceInfo) -> List[RegexPattern]:
        """生成Aurora服务的特殊模式"""
        patterns = []
        service_name_lower = service_info.authoritative_full_name.lower()
        
        # 检测数据库引擎变体
        if "postgresql" in service_name_lower:
            variant = "postgresql"
            patterns.append(RegexPattern(
                pattern_name=f'AURORA_POSTGRESQL_VARIANT_{service_info.service_code}',
                pattern_type='SERVICE_NAME',
                regex_string=r'(?:Amazon\s+Aurora|AWS\s+Aurora|\bAurora)\s+PostgreSQL\b',
                priority=130,
                notes='Aurora PostgreSQL specific variant',
                related_service_id=None,
                service_code=service_info.service_code
            ))
        elif "mysql" in service_name_lower:
            variant = "mysql"
            patterns.append(RegexPattern(
                pattern_name=f'AURORA_MYSQL_VARIANT_{service_info.service_code}',
                pattern_type='SERVICE_NAME',
                regex_string=r'(?:Amazon\s+Aurora|AWS\s+Aurora|\bAurora)\s+MySQL\b',
                priority=130,
                notes='Aurora MySQL specific variant',
                related_service_id=None,
                service_code=service_info.service_code
            ))
        
        # 通用Aurora模式（排除特定引擎）
        patterns.append(RegexPattern(
            pattern_name=f'AURORA_GENERAL_{service_info.service_code}',
            pattern_type='SERVICE_NAME',
            regex_string=r'(?<![:_-])\bAurora\b(?![\s-](?:PostgreSQL|MySQL)\b)',
            priority=125,
            notes='General Aurora pattern excluding specific variants',
            related_service_id=None,
            service_code=service_info.service_code
        ))
        
        return patterns
    
    def _generate_health_patterns(self, service_info: ServiceInfo) -> List[RegexPattern]:
        """生成Health服务的特殊模式"""
        patterns = []
        service_name_lower = service_info.authoritative_full_name.lower()
        
        # Health Dashboard特殊处理
        if "dashboard" in service_name_lower:
            patterns.append(RegexPattern(
                pattern_name=f'HEALTH_DASHBOARD_{service_info.service_code}',
                pattern_type='SERVICE_NAME',
                regex_string=r'(?:AWS\s+)?Health\s+Dashboard\b',
                priority=130,
                notes='Health Dashboard specific pattern',
                related_service_id=None,
                service_code=service_info.service_code
            ))
        
        # 通用Health模式（排除Dashboard）
        patterns.append(RegexPattern(
            pattern_name=f'HEALTH_GENERAL_{service_info.service_code}',
            pattern_type='SERVICE_NAME',
            regex_string=r'(?:AWS\s+)?Health(?!\s+Dashboard)\b',
            priority=125,
            notes='General Health pattern excluding Dashboard',
            related_service_id=None,
            service_code=service_info.service_code
        ))
        
        return patterns
    
    def _generate_rds_patterns(self, service_info: ServiceInfo) -> List[RegexPattern]:
        """生成RDS服务的特殊模式"""
        patterns = []
        engines = ['PostgreSQL', 'MySQL', 'MariaDB', 'Oracle', 'SQL Server']
        
        # 检测特定数据库引擎
        service_name_lower = service_info.authoritative_full_name.lower()
        detected_engine = None
        
        for engine in engines:
            if engine.lower() in service_name_lower:
                detected_engine = engine
                break
        
        if detected_engine:
            # RDS for Engine模式
            patterns.append(RegexPattern(
                pattern_name=f'RDS_FOR_{detected_engine.replace(" ", "_").upper()}_{service_info.service_code}',
                pattern_type='SERVICE_NAME',
                regex_string=f"(?:Amazon\\s+RDS|AWS\\s+RDS|\\bRDS)\\s+for\\s+{re.escape(detected_engine)}",
                priority=130,
                notes=f'RDS for {detected_engine} specific pattern',
                related_service_id=None,
                service_code=service_info.service_code
            ))
        
        # 通用RDS模式（排除特定引擎）
        engine_exclusion = '|'.join([f'for\\s+{re.escape(engine)}' for engine in engines])
        patterns.append(RegexPattern(
            pattern_name=f'RDS_GENERAL_{service_info.service_code}',
            pattern_type='SERVICE_NAME',
            regex_string=f"(?:Amazon\\s+Relational\\s+Database\\s+Service|Amazon\\s+RDS|AWS\\s+RDS|\\bRDS)(?!\\s+(?:{engine_exclusion}))",
            priority=125,
            notes='General RDS pattern excluding specific engine variants',
            related_service_id=None,
            service_code=service_info.service_code
        ))
        
        return patterns
    
    def _load_special_service_configs(self) -> Dict[str, Any]:
        """加载特殊服务配置"""
        return {
            "aurora": {
                "engines": ["PostgreSQL", "MySQL"],
                "base_priority": 125,
                "variant_priority": 130
            },
            "rds": {
                "engines": ["PostgreSQL", "MySQL", "MariaDB", "Oracle", "SQL Server"],
                "base_priority": 115,
                "variant_priority": 125
            },
            "health": {
                "variants": ["Dashboard"],
                "base_priority": 125,
                "variant_priority": 130
            }
        }

## 数据质量保证和验证系统

### 数据验证架构

```python
class DataQualityValidator:
    """数据质量保证和验证系统"""
    
    def __init__(self, rds_client: RDSClient):
        self.rds_client = rds_client
        self.validation_rules = self._load_validation_rules()
        self.quality_metrics = {}
    
    def validate_scraped_data(self, web_services: List[str], pdf_services: List[ServiceInfo]) -> ValidationResult:
        """验证抓取数据的质量"""
        result = ValidationResult()
        
        # 验证数据完整性
        result.web_data_validation = self._validate_web_data(web_services)
        result.pdf_data_validation = self._validate_pdf_data(pdf_services)
        
        # 验证数据一致性
        result.consistency_validation = self._validate_data_consistency(web_services, pdf_services)
        
        # 验证数据变化幅度
        result.change_validation = self._validate_data_changes(web_services)
        
        return result
    
    def validate_service_info(self, service: ServiceInfo) -> ServiceValidationResult:
        """验证单个服务信息的完整性"""
        result = ServiceValidationResult(service_id=service.authoritative_full_name)
        
        # 必填字段验证
        required_fields = ['authoritative_full_name', 'base_name', 'full_name_en', 'short_name_en']
        for field in required_fields:
            if not getattr(service, field):
                result.add_error(f"Required field '{field}' is missing or empty")
        
        # 字段格式验证
        if service.authoritative_full_name and len(service.authoritative_full_name) > 255:
            result.add_error("authoritative_full_name exceeds maximum length")
        
        # 业务逻辑验证
        if service.base_name and service.authoritative_full_name:
            expected_base_name = re.sub(r'\s*\([^)]*\)', '', service.authoritative_full_name).strip()
            if service.base_name != expected_base_name:
                result.add_warning(f"base_name derivation mismatch: expected '{expected_base_name}', got '{service.base_name}'")
        
        return result
    
    def validate_regex_patterns(self, patterns: List[RegexPattern]) -> PatternValidationResult:
        """验证正则表达式模式的语法和逻辑"""
        result = PatternValidationResult()
        
        for pattern in patterns:
            pattern_result = self._validate_single_pattern(pattern)
            result.add_pattern_result(pattern.pattern_name, pattern_result)
        
        # 检测模式冲突
        conflicts = self._detect_pattern_conflicts(patterns)
        result.conflicts = conflicts
        
        return result
    
    def _validate_web_data(self, web_services: List[str]) -> WebDataValidation:
        """验证网页抓取数据"""
        validation = WebDataValidation()
        
        if not web_services:
            validation.add_error("No web services data found")
            return validation
        
        if len(web_services) < 10:
            validation.add_warning(f"Low service count: {len(web_services)} services found")
        
        # 检查重复项
        duplicates = [service for service in set(web_services) if web_services.count(service) > 1]
        if duplicates:
            validation.add_warning(f"Duplicate services found: {duplicates}")
        
        validation.service_count = len(web_services)
        validation.unique_count = len(set(web_services))
        
        return validation
    
    def _validate_pdf_data(self, pdf_services: List[ServiceInfo]) -> PDFDataValidation:
        """验证PDF解析数据"""
        validation = PDFDataValidation()
        
        if not pdf_services:
            validation.add_error("No PDF services data found")
            return validation
        
        # 验证三列数据完整性
        incomplete_services = []
        for service in pdf_services:
            if not all([service.internal_name, service.full_name_en, service.short_name_en]):
                incomplete_services.append(service.internal_name or "Unknown")
        
        if incomplete_services:
            validation.add_warning(f"Incomplete PDF data for services: {incomplete_services}")
        
        validation.service_count = len(pdf_services)
        validation.complete_count = len(pdf_services) - len(incomplete_services)
        
        return validation
    
    def _validate_data_changes(self, current_services: List[str]) -> ChangeValidation:
        """验证数据变化幅度（±30%）"""
        validation = ChangeValidation()
        
        try:
            # 获取历史数据
            historical_count = self.rds_client.get_historical_service_count()
            current_count = len(current_services)
            
            if historical_count > 0:
                change_ratio = abs(current_count - historical_count) / historical_count
                validation.change_ratio = change_ratio
                validation.historical_count = historical_count
                validation.current_count = current_count
                
                if change_ratio > 0.3:
                    validation.add_error(f"Service count change exceeds 30%: {change_ratio:.2%}")
                elif change_ratio > 0.2:
                    validation.add_warning(f"Significant service count change: {change_ratio:.2%}")
            
        except Exception as e:
            validation.add_error(f"Failed to validate data changes: {str(e)}")
        
        return validation
    
    def _validate_single_pattern(self, pattern: RegexPattern) -> SinglePatternValidation:
        """验证单个正则表达式模式"""
        validation = SinglePatternValidation(pattern_name=pattern.pattern_name)
        
        try:
            # 语法验证
            re.compile(pattern.regex_string)
            validation.syntax_valid = True
        except re.error as e:
            validation.syntax_valid = False
            validation.add_error(f"Regex syntax error: {str(e)}")
        
        # 优先级验证
        if not (90 <= pattern.priority <= 130):
            validation.add_error(f"Priority {pattern.priority} out of valid range (90-130)")
        
        # 模式复杂度验证
        if len(pattern.regex_string) > 1000:
            validation.add_warning("Pattern is very complex and may impact performance")
        
        return validation
    
    def _detect_pattern_conflicts(self, patterns: List[RegexPattern]) -> List[PatternConflict]:
        """检测模式之间的潜在冲突"""
        conflicts = []
        
        # 按优先级分组
        priority_groups = {}
        for pattern in patterns:
            if pattern.priority not in priority_groups:
                priority_groups[pattern.priority] = []
            priority_groups[pattern.priority].append(pattern)
        
        # 检测同优先级冲突
        for priority, group_patterns in priority_groups.items():
            if len(group_patterns) > 1:
                for i, pattern1 in enumerate(group_patterns):
                    for pattern2 in group_patterns[i+1:]:
                        if self._patterns_may_conflict(pattern1, pattern2):
                            conflicts.append(PatternConflict(
                                pattern1=pattern1.pattern_name,
                                pattern2=pattern2.pattern_name,
                                priority=priority,
                                conflict_type="same_priority_overlap"
                            ))
        
        return conflicts
    
    def _patterns_may_conflict(self, pattern1: RegexPattern, pattern2: RegexPattern) -> bool:
        """检查两个模式是否可能冲突"""
        # 简化的冲突检测逻辑
        # 实际实现中可以使用更复杂的算法
        return (pattern1.service_code == pattern2.service_code and 
                pattern1.pattern_type == pattern2.pattern_type)
    
    def _load_validation_rules(self) -> Dict[str, Any]:
        """加载验证规则配置"""
        return {
            "max_change_ratio": 0.3,
            "min_service_count": 10,
            "max_pattern_length": 1000,
            "required_fields": ['authoritative_full_name', 'base_name', 'full_name_en', 'short_name_en']
        }

@dataclass
class ValidationResult:
    """验证结果数据模型"""
    web_data_validation: Optional['WebDataValidation'] = None
    pdf_data_validation: Optional['PDFDataValidation'] = None
    consistency_validation: Optional['ConsistencyValidation'] = None
    change_validation: Optional['ChangeValidation'] = None
    
    def is_valid(self) -> bool:
        """检查整体验证是否通过"""
        validations = [self.web_data_validation, self.pdf_data_validation, 
                      self.consistency_validation, self.change_validation]
        return all(v.is_valid() if v else True for v in validations)
    
    def get_all_errors(self) -> List[str]:
        """获取所有错误信息"""
        errors = []
        validations = [self.web_data_validation, self.pdf_data_validation, 
                      self.consistency_validation, self.change_validation]
        for validation in validations:
            if validation:
                errors.extend(validation.errors)
        return errors
```

## 批量操作优化和性能管理系统

### 性能优化架构

```python
class PerformanceOptimizer:
    """批量操作优化和性能管理系统"""
    
    def __init__(self, rds_client: RDSClient):
        self.rds_client = rds_client
        self.batch_size = 100
        self.connection_pool_size = 10
        self.performance_metrics = {}
    
    def optimize_batch_operations(self, services: List[ServiceInfo]) -> BatchOperationResult:
        """优化批量操作性能"""
        result = BatchOperationResult()
        
        try:
            # 分批处理
            batches = self._create_batches(services, self.batch_size)
            result.total_batches = len(batches)
            
            successful_operations = 0
            failed_operations = 0
            
            for i, batch in enumerate(batches):
                batch_result = self._process_batch(batch, i + 1)
                result.batch_results.append(batch_result)
                
                if batch_result.success:
                    successful_operations += len(batch)
                else:
                    failed_operations += len(batch)
                    # 批量失败时回退到单条处理
                    single_results = self._fallback_to_single_processing(batch)
                    result.fallback_results.extend(single_results)
            
            result.successful_operations = successful_operations
            result.failed_operations = failed_operations
            result.success_rate = successful_operations / len(services) if services else 0
            
        except Exception as e:
            result.add_error(f"Batch optimization failed: {str(e)}")
        
        return result
    
    def _create_batches(self, items: List[Any], batch_size: int) -> List[List[Any]]:
        """创建批次"""
        batches = []
        for i in range(0, len(items), batch_size):
            batches.append(items[i:i + batch_size])
        return batches
    
    def _process_batch(self, batch: List[ServiceInfo], batch_number: int) -> BatchResult:
        """处理单个批次"""
        result = BatchResult(batch_number=batch_number, batch_size=len(batch))
        
        try:
            start_time = time.time()
            
            # 使用psycopg2.extras.execute_values进行批量插入
            success_count = self.rds_client.batch_upsert_services(batch)
            
            end_time = time.time()
            result.processing_time = end_time - start_time
            result.success = True
            result.processed_count = success_count
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
        
        return result
    
    def _fallback_to_single_processing(self, batch: List[ServiceInfo]) -> List[SingleProcessResult]:
        """回退到单条记录处理"""
        results = []
        
        for service in batch:
            result = SingleProcessResult(service_name=service.authoritative_full_name)
            try:
                success = self.rds_client.upsert_service(service)
                result.success = success
            except Exception as e:
                result.success = False
                result.error_message = str(e)
            
            results.append(result)
        
        return results
    
    def optimize_database_connections(self) -> ConnectionOptimizationResult:
        """优化数据库连接"""
        result = ConnectionOptimizationResult()
        
        try:
            # 配置连接池
            pool_config = {
                'minconn': 1,
                'maxconn': self.connection_pool_size,
                'host': self.rds_client.config.get_database_config()['host'],
                'port': self.rds_client.config.get_database_config()['port'],
                'database': self.rds_client.config.get_database_config()['database'],
                'user': self.rds_client.config.get_database_config()['username'],
                'password': self.rds_client.config.get_database_config()['password']
            }
            
            # 创建连接池
            self.rds_client.initialize_connection_pool(pool_config)
            result.pool_initialized = True
            result.pool_size = self.connection_pool_size
            
        except Exception as e:
            result.pool_initialized = False
            result.error_message = str(e)
        
        return result
    
    

@dataclass
class BatchOperationResult:
    """批量操作结果"""
    total_batches: int = 0
    successful_operations: int = 0
    failed_operations: int = 0
    success_rate: float = 0.0
    batch_results: List['BatchResult'] = field(default_factory=list)
    fallback_results: List['SingleProcessResult'] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    
    def add_error(self, error: str):
        self.errors.append(error)

@dataclass
class BatchResult:
    """单个批次处理结果"""
    batch_number: int
    batch_size: int
    success: bool = False
    processed_count: int = 0
    processing_time: float = 0.0
    error_message: Optional[str] = None
```

## 同步操作历史记录系统

### 历史记录架构

```python
class SyncHistoryManager:
    """同步操作历史记录系统"""
    
    def __init__(self, rds_client: RDSClient):
        self.rds_client = rds_client
        self.current_sync_id = None
    
    def start_sync_session(self, trigger_source: str = 'eventbridge') -> str:
        """开始同步会话，返回同步ID"""
        sync_id = str(uuid.uuid4())
        self.current_sync_id = sync_id
        
        sync_record = SyncHistoryRecord(
            sync_id=sync_id,
            trigger_source=trigger_source,
            status='started',
            start_time=datetime.utcnow()
        )
        
        self._record_sync_event(sync_record)
        return sync_id
    
    def record_sync_progress(self, stage: str, details: Dict[str, Any]):
        """记录同步进度"""
        if not self.current_sync_id:
            raise ValueError("No active sync session")
        
        progress_record = SyncProgressRecord(
            sync_id=self.current_sync_id,
            stage=stage,
            details=details,
            timestamp=datetime.utcnow()
        )
        
        self._record_progress_event(progress_record)
    
    def complete_sync_session(self, result: SyncResult):
        """完成同步会话"""
        if not self.current_sync_id:
            raise ValueError("No active sync session")
        
        completion_record = SyncHistoryRecord(
            sync_id=self.current_sync_id,
            status='completed' if result.success else 'failed',
            end_time=datetime.utcnow(),
            total_services_processed=result.total_services,
            successful_operations=result.successful_operations,
            failed_operations=result.failed_operations,
            error_summary=result.error_summary
        )
        
        self._update_sync_record(completion_record)
        self.current_sync_id = None
    
    def get_sync_history(self, limit: int = 50) -> List[SyncHistoryRecord]:
        """获取同步历史记录"""
        return self.rds_client.get_sync_history(limit)
    
    def get_sync_details(self, sync_id: str) -> SyncSessionDetails:
        """获取特定同步会话的详细信息"""
        history_record = self.rds_client.get_sync_record(sync_id)
        progress_records = self.rds_client.get_sync_progress(sync_id)
        
        return SyncSessionDetails(
            history_record=history_record,
            progress_records=progress_records
        )
    
    def _record_sync_event(self, record: SyncHistoryRecord):
        """记录同步事件到数据库"""
        self.rds_client.insert_sync_history(record)
    
    def _record_progress_event(self, record: SyncProgressRecord):
        """记录进度事件到数据库"""
        self.rds_client.insert_sync_progress(record)
    
    def _update_sync_record(self, record: SyncHistoryRecord):
        """更新同步记录"""
        self.rds_client.update_sync_history(record)

@dataclass
class SyncHistoryRecord:
    """同步历史记录数据模型"""
    sync_id: str
    trigger_source: str = 'eventbridge'
    status: str = 'started'  # started, in_progress, completed, failed
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    total_services_processed: int = 0
    successful_operations: int = 0
    failed_operations: int = 0
    error_summary: Optional[str] = None
    
    @property
    def duration(self) -> Optional[float]:
        """计算同步持续时间（秒）"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None

@dataclass
class SyncProgressRecord:
    """同步进度记录数据模型"""
    sync_id: str
    stage: str  # web_scraping, pdf_parsing, data_processing, pattern_generation, database_storage
    details: Dict[str, Any]
    timestamp: datetime
    
@dataclass
class SyncSessionDetails:
    """同步会话详细信息"""
    history_record: SyncHistoryRecord
    progress_records: List[SyncProgressRecord]
```

## 部署说明（手动）

本模块采用手动部署方式，不提供任何自动化部署/CI/CD/监控告警设计。以下为手动部署所需的前提条件与基本配置说明。

### 部署前提条件
- AWS 账户与区域：建议 cn-northwest-1（与数据库、S3、Secrets Manager 匹配）
- 运行环境：AWS Lambda（Python 3.9+）或等效容器化运行时
- 网络：若连接 RDS（非公网），需配置 VPC 子网与安全组入站/出站规则
- 数据库：PostgreSQL（见数据库架构脚本 mass_email_database_schema_v2.sql）

### 必需环境变量
- SECRETS_MANAGER_ARN：Secrets Manager 中存储配置的 Secret ARN
- LOG_LEVEL：日志级别（INFO/DEBUG/WARN）
- REGION：部署区域（如 cn-northwest-1）

### Secrets Manager 配置结构（示例）
```json
{
  "database": {
    "host": "<rds-endpoint>",
    "port": 5432,
    "username": "mass_email_translator",
    "password": "<REDACTED>",
    "database": "mass_email_translator"
  },
  "s3": {
    "snpdf": "s3://mass-email-translator-feedback/awsofferingnames_pdf/AWSOfferingNames05032024.pdf"
  }
}
```

### 最小化 IAM 角色权限（参考）
- 信任策略：允许 lambda.amazonaws.com 假设角色
- 访问策略（按最小权限）：
  - Secrets Manager：secretsmanager:GetSecretValue（限定到目标 Secret）
  - S3：s3:GetObject（限定到所需 PDF 路径）
  - CloudWatch Logs：logs:CreateLogGroup、logs:CreateLogStream、logs:PutLogEvents
  - 若使用 VPC：ec2:CreateNetworkInterface、ec2:DescribeNetworkInterfaces、ec2:DeleteNetworkInterface

> 注：如需定时执行，可手动创建 EventBridge 规则并指向该 Lambda；本设计文档不提供自动化模板。

### 部署架构

本模块采用手动部署方式，不提供自动化部署架构与模板。请参考“部署说明（手动）”章节的前提条件、环境变量、Secrets示例与最小权限IAM清单，按需在控制台或IaC工具中手动完成创建与配置。
## 测试策略

本模块采用最小化测试策略，聚焦核心业务功能的基本验证：
- 单元测试（可选）：对关键函数进行基础正确性校验（如数据标准化、正则生成语法校验）
- 手动验证：通过控制台/日志检查关键路径（网页抓取、PDF解析、数据处理、模式生成、入库）
- 日志核对：确认结构化日志包含关键事件（sync_start、sync_progress、sync_complete、error_occurred）

说明：不提供复杂测试框架、自动化测试事件集或性能测试脚本的设计，保持测试最小化，服务于手动部署与独立运行场景。

## 错误处理和恢复策略

### 错误处理架构

```python
class ErrorHandlingSystem:
    """分级错误处理和恢复策略系统"""
    
    def __init__(self):
        self.error_handlers = self._initialize_error_handlers()
        self.retry_strategies = self._initialize_retry_strategies()
        self.recovery_mechanisms = self._initialize_recovery_mechanisms()
    
    def handle_error(self, error: Exception, context: ErrorContext) -> ErrorHandlingResult:
        """统一错误处理入口"""
        error_type = self._classify_error(error)
        severity = self._assess_error_severity(error, context)
        
        result = ErrorHandlingResult(
            error_type=error_type,
            severity=severity,
            original_error=str(error),
            context=context
        )
        
        # 根据错误类型和严重程度选择处理策略
        handler = self.error_handlers.get(error_type)
        if handler:
            result = handler.handle(error, context, result)
        
        # 记录错误
        self._log_error(result)
        
        return result
    
    def _classify_error(self, error: Exception) -> str:
        """错误分类"""
        if isinstance(error, requests.exceptions.RequestException):
            return "network_error"
        elif isinstance(error, psycopg2.Error):
            return "database_error"
        elif isinstance(error, FileNotFoundError):
            return "file_system_error"
        elif isinstance(error, ValueError):
            return "validation_error"
        elif isinstance(error, TimeoutError):
            return "timeout_error"
        else:
            return "unknown_error"
    
    def _assess_error_severity(self, error: Exception, context: ErrorContext) -> str:
        """评估错误严重程度"""
        if isinstance(error, (ConnectionError, TimeoutError)):
            return "HIGH"
        elif isinstance(error, psycopg2.IntegrityError):
            return "CRITICAL"
        elif isinstance(error, ValueError):
            return "MEDIUM"
        else:
            return "LOW"
    
    def _initialize_error_handlers(self) -> Dict[str, 'ErrorHandler']:
        """初始化错误处理器"""
        return {
            "network_error": NetworkErrorHandler(),
            "database_error": DatabaseErrorHandler(),
            "file_system_error": FileSystemErrorHandler(),
            "validation_error": ValidationErrorHandler(),
            "timeout_error": TimeoutErrorHandler()
        }
    
    def _initialize_retry_strategies(self) -> Dict[str, 'RetryStrategy']:
        """初始化重试策略"""
        return {
            "network_error": ExponentialBackoffRetry(max_attempts=3, base_delay=1),
            "database_error": ExponentialBackoffRetry(max_attempts=5, base_delay=2),
            "file_system_error": LinearRetry(max_attempts=3, delay=1),
            "timeout_error": ExponentialBackoffRetry(max_attempts=2, base_delay=5)
        }
    
    def _initialize_recovery_mechanisms(self) -> Dict[str, 'RecoveryMechanism']:
        """初始化恢复机制"""
        return {
            "network_error": NetworkRecovery(),
            "database_error": DatabaseRecovery(),
            "data_corruption": DataRecovery()
        }
    
    def _log_error(self, result: ErrorHandlingResult):
        """记录错误到结构化日志"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": "ERROR",
            "component": "error_handling_system",
            "event_type": "error_occurred",
            "error_type": result.error_type,
            "severity": result.severity,
            "error_message": result.original_error,
            "context": result.context.__dict__,
            "recovery_attempted": result.recovery_attempted,
            "recovery_successful": result.recovery_successful
        }
        
        logger.error(json.dumps(log_entry))

class NetworkErrorHandler:
    """网络错误处理器"""
    
    def handle(self, error: Exception, context: ErrorContext, result: ErrorHandlingResult) -> ErrorHandlingResult:
        """处理网络错误"""
        if context.retry_count < 3:
            # 实施重试
            result.should_retry = True
            result.retry_delay = min(2 ** context.retry_count, 60)  # 指数退避，最大60秒
            result.recovery_attempted = True
        else:
            # 重试次数耗尽，使用缓存数据或跳过
            result.should_retry = False
            result.use_cached_data = True
            result.recovery_message = "Using cached data due to network failure"
        
        return result

class DatabaseErrorHandler:
    """数据库错误处理器"""
    
    def handle(self, error: Exception, context: ErrorContext, result: ErrorHandlingResult) -> ErrorHandlingResult:
        """处理数据库错误"""
        if isinstance(error, psycopg2.IntegrityError):
            # 数据完整性错误，跳过当前记录
            result.should_skip_record = True
            result.recovery_message = "Skipping record due to integrity constraint violation"
        elif isinstance(error, psycopg2.OperationalError):
            # 连接错误，尝试重新连接
            if context.retry_count < 5:
                result.should_retry = True
                result.retry_delay = min(2 ** context.retry_count, 30)
                result.recovery_attempted = True
            else:
                result.should_fail = True
                result.recovery_message = "Database connection failed after maximum retries"
        
        return result

@dataclass
class ErrorContext:
    """错误上下文"""
    operation: str
    retry_count: int = 0
    data_context: Optional[Dict[str, Any]] = None
    timestamp: datetime = field(default_factory=datetime.utcnow)

@dataclass
class ErrorHandlingResult:
    """错误处理结果"""
    error_type: str
    severity: str
    original_error: str
    context: ErrorContext
    should_retry: bool = False
    retry_delay: float = 0
    should_skip_record: bool = False
    should_fail: bool = False
    use_cached_data: bool = False
    recovery_attempted: bool = False
    recovery_successful: bool = False
    recovery_message: Optional[str] = None
```

## 总结

本设计文档全面覆盖了AWS服务同步系统v2.0优化版的所有需求，包括：

### 核心功能系统
1. **多数据源抓取系统** - 网页抓取和PDF解析
2. **智能数据处理系统** - 多数据源匹配和标准化
3. **8种核心正则模式生成系统** - 完整的模式类型体系
4. **边界保护机制系统** - 避免误匹配的保护策略

### 架构优化系统
5. **v2.0组件化架构** - 模块化组件设计
6. **高性能匹配算法** - Aho-Corasick + 分段正则
7. **数据库v2架构** - 字段职责分离和性能优化

### 质量保证系统
8. **数据质量保证系统** - 完整的验证和质量检查
9. **批量操作优化系统** - 性能优化和批处理
10. **分级错误处理系统** - 完善的错误处理和恢复

### 运维支持系统
11. **配置管理系统** - AWS Secrets Manager集成
12. **同步历史记录系统** - 完整的操作历史追踪
14. **监控和日志系统** - 结构化日志和CloudWatch集成
15. **测试策略系统** - 完整的测试框架和验证机制

该设计确保系统具备高性能、高可靠性与易维护性，符合本模块“独立数据同步服务、数据库中介模式、手动部署”的最新架构定位与需求目标。
    
    def _generate_lambda_patterns(self, service_info: ServiceInfo) -> List[RegexPattern]:
        """生成Lambda服务的特殊模式"""
        patterns = []
        
        # Lambda函数特殊模式
        patterns.append(RegexPattern(
            pattern_name=f'LAMBDA_FUNCTION_{service_info.service_code}',
            pattern_type='SERVICE_NAME',
            regex_string=r'(?:AWS\s+)?Lambda\s+(?:function|Function)s?\b',
            priority=130,
            notes='Lambda function specific pattern',
            related_service_id=None,
            service_code=service_info.service_code
        ))
        
        # 通用Lambda模式
        patterns.append(RegexPattern(
            pattern_name=f'LAMBDA_GENERAL_{service_info.service_code}',
            pattern_type='SERVICE_NAME',
            regex_string=r'(?:AWS\s+)?Lambda(?!\s+(?:function|Function))\b',
            priority=125,
            notes='General Lambda pattern',
            related_service_id=None,
            service_code=service_info.service_code
        ))
        
        return patterns
    
    def _generate_ec2_patterns(self, service_info: ServiceInfo) -> List[RegexPattern]:
        """生成EC2服务的特殊模式"""
        patterns = []
        
        # EC2实例类型特殊模式
        instance_types = ['P3', 'P4', 'G4', 'C5', 'M5', 'R5', 'T3', 'T4g']
        
        for instance_type in instance_types:
            patterns.append(RegexPattern(
                pattern_name=f'EC2_{instance_type}_INSTANCES_{service_info.service_code}',
                pattern_type='SERVICE_NAME',
                regex_string=f"(?:Amazon\\s+)?EC2\\s+{instance_type}\\s+instances?\\b",
                priority=135,
                notes=f'EC2 {instance_type} instances specific pattern',
                related_service_id=None,
                service_code=service_info.service_code
            ))
        
        # EC2通用实例模式
        patterns.append(RegexPattern(
            pattern_name=f'EC2_INSTANCES_GENERAL_{service_info.service_code}',
            pattern_type='SERVICE_NAME',
            regex_string=r'(?:Amazon\s+)?EC2\s+instances?\b',
            priority=125,
            notes='General EC2 instances pattern',
            related_service_id=None,
            service_code=service_info.service_code
        ))
        
        return patterns
    
    def _load_special_service_configs(self) -> Dict[str, Any]:
        """加载特殊服务配置"""
        return {
            "aurora": {
                "variants": ["postgresql", "mysql"],
                "priority_boost": 5,
                "context_sensitive": True
            },
            "health": {
                "variants": ["dashboard"],
                "priority_boost": 5,
                "context_sensitive": True
            },
            "rds": {
                "variants": ["postgresql", "mysql", "mariadb", "oracle", "sqlserver"],
                "priority_boost": 5,
                "context_sensitive": True
            },
            "lambda": {
                "variants": ["function"],
                "priority_boost": 3,
                "context_sensitive": False
            },
            "ec2": {
                "variants": ["instances", "p3", "p4", "g4", "c5", "m5", "r5", "t3", "t4g"],
                "priority_boost": 3,
                "context_sensitive": False
            }
        }
```

### 优先级冲突解决算法

```python
@dataclass
class MatchResult:
    """匹配结果数据结构"""
    start: int
    end: int
    pattern: RegexPattern
    matched_text: str
    priority: int
    pattern_id: int

class PriorityResolver:
    """优先级冲突解决算法"""
    
    def resolve_conflicts(self, matches: List[MatchResult]) -> List[MatchResult]:
        """解决重叠匹配的优先级冲突"""
        if not matches:
            return []
        
        # 按位置分组重叠的匹配
        overlap_groups = self._group_overlapping_matches(matches)
        
        resolved_matches = []
        for group in overlap_groups:
            winner = self._resolve_group_conflict(group)
            resolved_matches.append(winner)
        
        return sorted(resolved_matches, key=lambda m: m.start)
    
    def _group_overlapping_matches(self, matches: List[MatchResult]) -> List[List[MatchResult]]:
        """将重叠的匹配分组"""
        sorted_matches = sorted(matches, key=lambda m: (m.start, m.end))
        groups = []
        current_group = [sorted_matches[0]] if sorted_matches else []
        
        for match in sorted_matches[1:]:
            if self._is_overlapping(current_group[-1], match):
                current_group.append(match)
            else:
                if current_group:
                    groups.append(current_group)
                current_group = [match]
        
        if current_group:
            groups.append(current_group)
        
        return groups
    
    def _resolve_group_conflict(self, group: List[MatchResult]) -> MatchResult:
        """解决单个组内的冲突"""
        if len(group) == 1:
            return group[0]
        
        # 优先级冲突解决策略：
        # 1. 优先级最高的（数值越大优先级越高）
        # 2. 如果优先级相等，选择匹配长度最长的
        # 3. 如果长度也相等，选择数据库ID最小的（更早创建，更稳定）
        
        def conflict_key(match: MatchResult) -> tuple:
            return (
                -match.priority,  # 负号使得数值大的优先级高
                -(match.end - match.start),  # 负号使得长度长的优先
                match.pattern_id  # ID小的优先
            )
        
        return min(group, key=conflict_key)
    
    def _is_overlapping(self, match1: MatchResult, match2: MatchResult) -> bool:
        """检查两个匹配是否重叠"""
        return not (match1.end <= match2.start or match2.end <= match1.start)
```



## JSONB元数据支持系统

### 元数据结构定义

```python
@dataclass
class PatternMetadata:
    """正则表达式模式元数据结构"""
    
    # 基础模式属性
    isCompoundWithSuffix: bool = False
    suffixGroup: Optional[int] = None
    patternCategory: str = ""
    hasBoundaryProtection: bool = True
    
    # 模式类型标识
    patternType: str = ""  # 8种核心模式类型之一
    
    # 特殊服务处理
    specialServiceType: str = "normal"  # normal, aurora, health, rds
    serviceVariant: Optional[str] = None  # postgresql, mysql, dashboard等
    
    # 边界保护配置
    boundaryProtectionTypes: List[str] = field(default_factory=lambda: ["arn", "url", "code"])
    
    # 组件化架构支持
    componentMetadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_json(self) -> Dict[str, Any]:
        """转换为JSON格式"""
        return {
            "isCompoundWithSuffix": self.isCompoundWithSuffix,
            "suffixGroup": self.suffixGroup,
            "patternCategory": self.patternCategory,
            "hasBoundaryProtection": self.hasBoundaryProtection,
            "patternType": self.patternType,
            "specialServiceType": self.specialServiceType,
            "serviceVariant": self.serviceVariant,
            "boundaryProtectionTypes": self.boundaryProtectionTypes,
            "componentMetadata": self.componentMetadata
        }
    
    @classmethod
    def from_json(cls, json_data: Dict[str, Any]) -> 'PatternMetadata':
        """从JSON数据创建实例"""
        return cls(
            isCompoundWithSuffix=json_data.get("isCompoundWithSuffix", False),
            suffixGroup=json_data.get("suffixGroup"),
            patternCategory=json_data.get("patternCategory", ""),
            hasBoundaryProtection=json_data.get("hasBoundaryProtection", True),
            patternType=json_data.get("patternType", ""),
            specialServiceType=json_data.get("specialServiceType", "normal"),
            serviceVariant=json_data.get("serviceVariant"),
            boundaryProtectionTypes=json_data.get("boundaryProtectionTypes", ["arn", "url", "code"]),
            componentMetadata=json_data.get("componentMetadata", {})
        )
```



## 错误处理

### 分级错误处理系统

#### 错误分类和严重程度

```python
from enum import Enum
from dataclasses import dataclass
from typing import Dict, Any, Optional
from datetime import datetime

class ErrorCategory(Enum):
    NETWORK = "network"           # 网络相关错误
    DATABASE = "database"         # 数据库相关错误
    FILE_SYSTEM = "file_system"   # 文件系统相关错误
    PARSING = "parsing"           # 数据解析相关错误
    VALIDATION = "validation"     # 数据验证相关错误
    TIMEOUT = "timeout"           # 超时相关错误
    RESOURCE = "resource"         # 资源相关错误
    UNKNOWN = "unknown"           # 未知错误

class ErrorSeverity(Enum):
    LOW = "low"                   # 低严重程度，可忽略
    MEDIUM = "medium"             # 中等严重程度，需要记录
    HIGH = "high"                 # 高严重程度，需要告警
    CRITICAL = "critical"         # 严重错误，需要立即处理

@dataclass
class ErrorInfo:
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    exception: Exception
    context: Dict[str, Any]
    timestamp: datetime
    retry_count: int = 0
    is_recoverable: bool = True
```

#### 错误处理器实现

```python
class ErrorHandler:
    """统一错误处理器"""
    
    def __init__(self, logger):
        self.logger = logger
        self.error_stats = defaultdict(int)
        self.error_mapping = self._build_error_mapping()
    
    def handle_error(self, error: Exception, context: Dict[str, Any]) -> ErrorInfo:
        """统一错误处理入口"""
        error_info = self._classify_error(error)
        error_info.context = context
        error_info.timestamp = datetime.utcnow()
        
        # 记录错误统计
        self.error_stats[error_info.category] += 1
        
        # 根据严重程度处理
        if error_info.severity == ErrorSeverity.CRITICAL:
            self._handle_critical_error(error_info)
        elif error_info.severity == ErrorSeverity.HIGH:
            self._handle_high_severity_error(error_info)
        
        # 记录结构化日志
        self._log_error(error_info)
        
        return error_info
    
    def _classify_error(self, error: Exception) -> ErrorInfo:
        """错误分类和严重程度评估"""
        error_type = type(error).__name__
        
        if error_type in self.error_mapping:
            category, severity, recoverable = self.error_mapping[error_type]
        else:
            category, severity, recoverable = ErrorCategory.UNKNOWN, ErrorSeverity.MEDIUM, True
        
        return ErrorInfo(
            category=category,
            severity=severity,
            message=str(error),
            exception=error,
            context={},
            timestamp=datetime.utcnow(),
            is_recoverable=recoverable
        )
    
    def _build_error_mapping(self) -> Dict[str, tuple]:
        """构建错误类型映射"""
        return {
            # 网络错误
            'ConnectionError': (ErrorCategory.NETWORK, ErrorSeverity.HIGH, True),
            'TimeoutError': (ErrorCategory.TIMEOUT, ErrorSeverity.MEDIUM, True),
            'RequestException': (ErrorCategory.NETWORK, ErrorSeverity.MEDIUM, True),
            
            # 数据库错误
            'OperationalError': (ErrorCategory.DATABASE, ErrorSeverity.HIGH, True),
            'IntegrityError': (ErrorCategory.DATABASE, ErrorSeverity.HIGH, False),
            'DatabaseError': (ErrorCategory.DATABASE, ErrorSeverity.CRITICAL, True),
            
            # 文件系统错误
            'FileNotFoundError': (ErrorCategory.FILE_SYSTEM, ErrorSeverity.MEDIUM, False),
            'PermissionError': (ErrorCategory.FILE_SYSTEM, ErrorSeverity.HIGH, False),
            'IOError': (ErrorCategory.FILE_SYSTEM, ErrorSeverity.MEDIUM, True),
            
            # 解析错误
            'JSONDecodeError': (ErrorCategory.PARSING, ErrorSeverity.MEDIUM, False),
            'ValueError': (ErrorCategory.VALIDATION, ErrorSeverity.MEDIUM, False),
            'KeyError': (ErrorCategory.VALIDATION, ErrorSeverity.MEDIUM, False),
            
            # 资源错误
            'MemoryError': (ErrorCategory.RESOURCE, ErrorSeverity.CRITICAL, False),
            'ResourceError': (ErrorCategory.RESOURCE, ErrorSeverity.HIGH, True),
        }
```

### 重试机制设计

#### 重试策略配置

```python
@dataclass
class RetryConfig:
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    backoff_factor: float = 2.0
    retryable_exceptions: Tuple[Type[Exception], ...] = (Exception,)

class RetryConfigs:
    """预定义的重试配置"""
    
    NETWORK_REQUEST = RetryConfig(
        max_attempts=3,
        base_delay=2.0,
        max_delay=30.0,
        backoff_factor=2.0,
        retryable_exceptions=(requests.RequestException, ConnectionError, TimeoutError)
    )
    
    DATABASE_OPERATION = RetryConfig(
        max_attempts=5,
        base_delay=1.0,
        max_delay=60.0,
        backoff_factor=2.0,
        retryable_exceptions=(psycopg2.OperationalError, psycopg2.InterfaceError)
    )
    
    FILE_OPERATION = RetryConfig(
        max_attempts=3,
        base_delay=1.0,
        max_delay=10.0,
        backoff_factor=1.5,
        retryable_exceptions=(IOError, OSError)
    )
```

#### 重试管理器实现

```python
class RetryManager:
    """重试机制管理器"""
    
    def __init__(self, config: RetryConfig, logger):
        self.config = config
        self.logger = logger
    
    def execute_with_retry(self, func: callable, *args, **kwargs) -> Any:
        """执行函数并提供重试机制"""
        last_exception = None
        
        for attempt in range(1, self.config.max_attempts + 1):
            try:
                result = func(*args, **kwargs)
                if attempt > 1:
                    self.logger.info(f"重试成功，第{attempt}次尝试")
                return result
                
            except self.config.retryable_exceptions as e:
                last_exception = e
                
                if attempt == self.config.max_attempts:
                    self.logger.error(f"重试失败，已达到最大尝试次数({self.config.max_attempts})")
                    break
                
                delay = self._calculate_delay(attempt)
                self.logger.warning(f"第{attempt}次尝试失败，{delay:.1f}秒后重试: {e}")
                time.sleep(delay)
        
        raise last_exception
    
    def _calculate_delay(self, attempt: int) -> float:
        """计算指数退避延迟"""
        delay = self.config.base_delay * (self.config.backoff_factor ** (attempt - 1))
        return min(delay, self.config.max_delay)
```

### 超时和资源管理

#### 超时控制机制

```python
class TimeoutManager:
    """超时控制管理器"""
    
    def __init__(self, timeout_seconds: int = 300):  # 默认5分钟
        self.timeout_seconds = timeout_seconds
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass
    
    def check_timeout(self):
        """检查是否超时"""
        if self.start_time and time.time() - self.start_time > self.timeout_seconds:
            raise TimeoutError(f"操作超时 ({self.timeout_seconds}秒)")
    
    def get_remaining_time(self) -> float:
        """获取剩余时间"""
        if not self.start_time:
            return self.timeout_seconds
        elapsed = time.time() - self.start_time
        return max(0, self.timeout_seconds - elapsed)
```



**1. 基础功能验证**

```python
def test_lambda_basic_functionality():
    """基础功能验证测试"""
    
    # 测试事件模板
    test_event = {
        "version": "0",
        "id": "test-event-id",
        "detail-type": "Scheduled Event",
        "source": "aws.events",
        "account": "************",
        "time": "2024-01-15T10:00:00Z",
        "region": "cn-northwest-1",
        "detail": {
            "trigger_type": "scheduled",
            "sync_mode": "full",
            "test_mode": True
        }
    }
    
    # 验证点
    validation_points = [
        "Lambda函数成功启动",
        "配置信息正确加载",
        "数据库连接建立成功",
        "网页抓取功能正常",
        "PDF解析功能正常",
        "数据存储功能正常",
        "正则模式生成正常",
        "执行时间在预期范围内"
    ]
    
    return test_event, validation_points
```

**2. 环境配置验证**

```bash
# 环境变量检查
aws lambda get-function-configuration --function-name aws-service-sync-dev

# Secrets Manager连接测试
aws secretsmanager get-secret-value --secret-id mass-email-translator-config

# IAM权限验证
aws sts get-caller-identity
aws iam simulate-principal-policy --policy-source-arn <lambda-role-arn> --action-names secretsmanager:GetSecretValue
```

#### CloudWatch日志分析

**关键日志事件监控**

```json
{
  "sync_start": {
    "event_type": "sync_start",
    "level": "INFO",
    "message": "开始执行同步任务",
    "context": {
      "sync_mode": "full",
      "data_sources": ["web", "pdf"]
    }
  },
  "sync_complete": {
    "event_type": "sync_complete",
    "level": "INFO",
    "message": "同步任务完成",
    "metrics": {
      "processed_count": 25,
      "success_count": 23,
      "error_count": 2,
      "success_rate": 0.92,
      "execution_time_ms": 45000
    }
  },
  "error_occurred": {
    "event_type": "error_occurred",
    "level": "ERROR",
    "message": "同步过程中发生错误",
    "error_details": {
      "error_type": "NetworkError",
      "error_message": "Connection timeout",
      "retry_count": 2
    }
  }
}
```

**日志查询示例**

```
# 查询同步任务执行状态
fields @timestamp, event_type, message, metrics.success_rate
| filter event_type = "sync_complete"
| sort @timestamp desc
| limit 20

# 查询错误日志
fields @timestamp, error_details.error_type, error_details.error_message
| filter event_type = "error_occurred"
| sort @timestamp desc
| limit 50

# 查询性能指标
fields @timestamp, metrics.execution_time_ms, metrics.processed_count
| filter event_type = "sync_complete"
| stats avg(metrics.execution_time_ms) by bin(5m)
```

#### 数据质量验证

**1. 数据完整性检查**

```sql
-- 检查同步后的数据完整性
SELECT 
    COUNT(*) as total_services,
    COUNT(CASE WHEN authoritative_full_name IS NOT NULL THEN 1 END) as has_auth_name,
    COUNT(CASE WHEN base_name IS NOT NULL THEN 1 END) as has_base_name,
    COUNT(CASE WHEN service_code IS NOT NULL THEN 1 END) as has_service_code,
    COUNT(CASE WHEN last_synced_at > NOW() - INTERVAL '1 hour' THEN 1 END) as recently_synced
FROM service_names 
WHERE is_active = true;

-- 检查正则模式生成情况
SELECT 
    pattern_type,
    COUNT(*) as pattern_count,
    COUNT(CASE WHEN validation_status = 'valid' THEN 1 END) as valid_patterns,
    AVG(priority) as avg_priority
FROM regex_patterns 
WHERE is_active = true
GROUP BY pattern_type
ORDER BY pattern_type;
```

**2. 数据一致性验证**

```sql
-- 验证外键关系
SELECT 
    rp.pattern_name,
    rp.service_code,
    sn.authoritative_full_name
FROM regex_patterns rp
LEFT JOIN service_names sn ON rp.related_service_id = sn.id
WHERE rp.is_active = true AND sn.id IS NULL;

-- 检查重复数据
SELECT 
    authoritative_full_name,
    COUNT(*) as duplicate_count
FROM service_names
GROUP BY authoritative_full_name
HAVING COUNT(*) > 1;
```

### 性能测试策略

#### 负载测试

```python
def performance_test_config():
    """性能测试配置"""
    return {
        "test_scenarios": [
            {
                "name": "正常负载",
                "service_count": 50,
                "expected_time_ms": 30000,
                "memory_limit_mb": 300
            },
            {
                "name": "高负载",
                "service_count": 100,
                "expected_time_ms": 60000,
                "memory_limit_mb": 400
            },
            {
                "name": "极限负载",
                "service_count": 200,
                "expected_time_ms": 120000,
                "memory_limit_mb": 500
            }
        ],
        "performance_thresholds": {
            "max_execution_time_ms": 300000,  # 5分钟
            "max_memory_usage_mb": 512,
            "min_success_rate": 0.95,
            "max_error_rate": 0.05
        }
    }
```

#### 监控指标设置

```python
def setup_cloudwatch_alarms():
    """设置CloudWatch告警"""
    alarms = [
        {
            "name": "aws-service-sync-error-rate",
            "metric": "ErrorRate",
            "threshold": 0.05,
            "comparison": "GreaterThanThreshold",
            "evaluation_periods": 2,
            "period": 300
        },
        {
            "name": "aws-service-sync-execution-time",
            "metric": "Duration",
            "threshold": 300000,  # 5分钟
            "comparison": "GreaterThanThreshold",
            "evaluation_periods": 1,
            "period": 300
        },
        {
            "name": "aws-service-sync-success-rate",
            "metric": "SuccessRate",
            "threshold": 0.95,
            "comparison": "LessThanThreshold",
            "evaluation_periods": 2,
            "period": 300
        }
    ]
    return alarms
```

### 集成测试

#### 端到端测试流程

```python
def end_to_end_test():
    """端到端集成测试"""
    
    test_steps = [
        {
            "step": 1,
            "name": "配置验证",
            "action": "验证AWS Secrets Manager配置",
            "expected": "成功获取数据库和S3配置"
        },
        {
            "step": 2,
            "name": "网页抓取",
            "action": "抓取AWS中国区官网服务列表",
            "expected": "成功获取服务名称列表，数量>20"
        },
        {
            "step": 3,
            "name": "PDF解析",
            "action": "解析S3中的PDF文件",
            "expected": "成功解析三列数据，数量>20"
        },
        {
            "step": 4,
            "name": "数据匹配",
            "action": "执行智能匹配算法",
            "expected": "匹配成功率>80%"
        },
        {
            "step": 5,
            "name": "数据存储",
            "action": "存储到RDS数据库",
            "expected": "成功存储，无数据丢失"
        },
        {
            "step": 6,
            "name": "模式生成",
            "action": "生成8种正则表达式模式",
            "expected": "每个服务生成8种模式，语法验证通过"
        },
        {
            "step": 7,
            "name": "模式存储",
            "action": "存储正则模式到数据库",
            "expected": "成功存储，无冲突"
        },
        {
            "step": 8,
            "name": "日志验证",
            "action": "检查CloudWatch日志",
            "expected": "包含完整的结构化日志"
        }
    ]
    
    return test_steps
```

## 总结

AWS服务同步系统设计遵循功能精简原则，实现了完整的多数据源同步、智能数据处理、8种核心正则表达式模式生成和完善的错误处理机制。系统采用事件驱动架构，结合AWS托管服务，提供高可用、可扩展的数据同步能力。

### 关键设计特点

1. **功能精简**: 专注核心业务功能，避免过度设计
2. **逻辑外化**: 将业务规则存储在数据库中，支持动态更新
3. **智能处理**: 多数据源智能匹配和8种模式类型自动生成
4. **质量保证**: 完整的数据验证、错误处理和监控机制
5. **运维友好**: 结构化日志、CloudWatch集成和部署指导

### 技术栈总结

- **计算**: AWS Lambda (Python 3.9+)
- **调度**: Amazon EventBridge
- **存储**: Amazon RDS (PostgreSQL), Amazon S3
- **配置**: AWS Secrets Manager
- **监控**: Amazon CloudWatch
- **部署**: AWS CLI, 自动化脚本

该设计确保系统能够稳定、高效地为批量邮件翻译系统提供准确、最新的AWS服务名称数据支持。        },
        {
            "step": 8,
            "name": "日志验证",
            "action": "检查CloudWatch日志",
            "expected": "包含完整的结构化日志"
        }
    ]
    
    return test_steps
```

## 总结

AWS服务同步系统设计遵循功能精简原则，实现了完整的多数据源同步、智能数据处理、8种核心正则表达式模式生成和完善的错误处理机制。系统采用事件驱动架构，结合AWS托管服务，提供高可用、可扩展的数据同步能力。

### 关键设计特点

1. **功能精简**: 专注核心业务功能，避免过度设计
2. **逻辑外化**: 将业务规则存储在数据库中，支持动态更新
3. **智能处理**: 多数据源智能匹配和8种模式类型自动生成
4. **质量保证**: 完整的数据验证、错误处理和监控机制
5. **运维友好**: 结构化日志、CloudWatch集成和部署指导

### 技术栈总结

- **计算**: AWS Lambda (Python 3.9+)
- **调度**: Amazon EventBridge
- **存储**: Amazon RDS (PostgreSQL), Amazon S3
- **配置**: AWS Secrets Manager
- **监控**: Amazon CloudWatch
- **部署**: AWS CLI, 自动化脚本

该设计确保系统能够稳定、高效地为批量邮件翻译系统提供准确、最新的AWS服务名称数据支持。