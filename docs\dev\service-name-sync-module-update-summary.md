---

### 核心决策

**决策：我们应该重新定义 `base_name` 的角色，并引入一个新的、更权威的字段作为同步过程的唯一键。`base_name` 回归其最初的、服务于翻译逻辑的本质。**

**最终方案**：
1.  **保留 `base_name` 字段**，但明确其定义为：**用于翻译逻辑（首次/后续提及）的、不含括号缩写的、规范化的服务名称**。它将根据权威来源**推导**得出。
2.  **新增一个字段 `authoritative_full_name`**，其定义为：**从官方网页（amazonaws.cn）抓取到的、作为权威数据源的“Services Offered”全称**。这个字段将是 `UNIQUE NOT NULL` 的，并作为 `aws-service-sync` 功能执行 `INSERT/UPDATE` 操作的**业务主键 (Business Key)**。
3.  **新增一个字段 `internal_name`**，用于存储从PDF中抓取到的 `AWS offering (internal name)`。这确保了我们捕获了所有可用信息，以备将来使用，但它不作为核心键。

### 为什么这是最佳方案？

1.  **解决了数据源的权威性问题**: 您的同步逻辑明确指出，网页是权威的全称来源。因此，用网页数据作为更新/插入的唯一标识符（`authoritative_full_name`）是最直接、最稳健的。
2.  **解除了冲突**: `base_name` 不再需要同时扮演“唯一标识符”和“逻辑分组键”两个角色。它的职责被清晰地分离了：
    *   `authoritative_full_name`: 负责**“是什么”**（唯一标识）。
    *   `base_name`: 负责**“如何分组处理”**（逻辑标识）。
3.  **保证了同步的鲁棒性**: 同步流程现在非常清晰：以 `authoritative_full_name` 为准，在数据库中查找记录。如果找到就更新，找不到就创建。这个流程不会因为PDF中缺少某个服务而失败。
4.  **保留了翻译逻辑的稳定性**: 现有的翻译逻辑（`standardizeEnglishChunk`）依赖一个稳定的、不带括号的 `base_name` 来跟踪服务提及状态。我们的方案保留了这一点，确保翻译核心功能不受影响。
5.  **数据完整性**: 我们捕获了来自网页和PDF的所有关键信息（`authoritative_full_name`, `internal_name`, `short_name_en`），没有丢失任何上下文。

---

### 1. 更新后的 `service_names` 表结构

这是您应该使用的最终表结构 DDL。

```sql
-- ---------------------------------
-- 表: service_names (服务名称) - v2 (支持自动化同步)
-- 描述: 存储所有AWS服务的标准中英文名称、缩写等信息
-- ---------------------------------
-- 先删除旧表（如果存在）
DROP TABLE IF EXISTS service_names CASCADE;

CREATE TABLE service_names (
    id BIGSERIAL PRIMARY KEY,
    
    -- 新增：作为同步流程的业务主键，来自网页的权威全称
    authoritative_full_name VARCHAR(255) NOT NULL UNIQUE,
    
    -- 重新定义的 base_name，用于翻译逻辑分组
    base_name VARCHAR(255) NOT NULL,
    
    -- 新增：来自PDF的 internal name
    internal_name VARCHAR(255),

    full_name_en VARCHAR(255) NOT NULL,            -- 英文全称, 与 authoritative_full_name 相同
    short_name_en VARCHAR(100) NOT NULL,           -- 英文缩写, 来自PDF或等于全称
    
    full_name_cn VARCHAR(255),                     -- 中文全称（预留字段）
    short_name_cn VARCHAR(100),                    -- 中文缩写（预留字段）
    service_code VARCHAR(50),                      -- AWS官方服务代码, 如 "ec2"
    source rule_source NOT NULL DEFAULT 'manual', -- 数据来源
    is_active BOOLEAN NOT NULL DEFAULT TRUE,       -- 是否启用
    notes TEXT,                                    -- 备注
    last_synced_at TIMESTAMPTZ,                    -- 上次同步时间
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 为关键字段添加索引
CREATE INDEX idx_service_names_base_name ON service_names(base_name);

-- 表和列的注释更新
COMMENT ON TABLE service_names IS '存储AWS服务的标准中英文名称和缩写信息，支持多数据源同步';
COMMENT ON COLUMN service_names.authoritative_full_name IS '来自官网网页的权威服务全称，作为同步操作的唯一业务键';
COMMENT ON COLUMN service_names.base_name IS '用于翻译逻辑的规范化基础名称（不含括号），根据 authoritative_full_name 推导';
COMMENT ON COLUMN service_names.internal_name IS '来自PDF文档的 "AWS offering (internal name)"';
COMMENT ON COLUMN service_names.full_name_en IS '首次使用时的英文全称，与 authoritative_full_name 保持一致';
COMMENT ON COLUMN service_names.short_name_en IS '后续使用的英文简称，主要来自PDF';

-- 别忘了为新表重新应用触发器
CREATE TRIGGER set_timestamp_service_names
    BEFORE UPDATE ON service_names
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();

```

### 2. 更新后的 `aws-service-sync` 功能实现逻辑

现在，您的4步功能细节将按照这个新的表结构来实现：

**假设您已经完成了步骤1和2，得到了：**
*   `web_full_names_list`: 一个包含所有网页 "Services Offered" 字符串的列表。
*   `pdf_data_map`: 一个以 `Long name (First use)` (已将aws替换为Amazon) 为键，值为 `{ short_name: '...', internal_name: '...' }` 的映射表。

**新的步骤3 & 4 (合并实现):**

```python
# 这是一个Python伪代码示例，演示核心逻辑

for web_full_name in web_full_names_list:
    
    # 初始化变量
    authoritative_name = web_full_name
    long_name_for_match = web_full_name.replace('AWS', 'Amazon') # 统一品牌词以便匹配
    short_name = authoritative_name # 默认简称等于全称
    internal_name = None

    # --- 核心逻辑开始 ---

    # 步骤 3.1: 尝试在PDF数据中进行正则匹配
    pdf_match_found = False
    # 注意：这里的 regex_patterns 需要从数据库加载
    for pattern_info in regex_patterns: 
        if re.search(pattern_info['regex'], long_name_for_match):
            # 假设正则匹配成功，我们找到了对应的PDF条目
            # 这是一个简化的匹配，实际可能更复杂
            # 关键在于找到 long_name_for_match 在 pdf_data_map 中的 key
            
            # 伪代码：假设我们通过正则找到了pdf_data_map中的key
            pdf_key = find_key_in_pdf_map(long_name_for_match, pdf_data_map)
            if pdf_key and pdf_key in pdf_data_map:
                short_name = pdf_data_map[pdf_key]['short_name']
                internal_name = pdf_data_map[pdf_key]['internal_name']
                pdf_match_found = True
                break # 找到即停止

    # 步骤 3.2: 如果PDF中没有匹配到，则简称与全称相同 (已在初始化时处理)

    # 步骤 4.1: 推导 base_name (这是新设计的核心部分)
    # 逻辑：移除括号内的缩写，并清理末尾空格
    base_name = re.sub(r'\s*\([^)]*\)$', '', authoritative_name).strip()

    # 步骤 4.2: 将整合后的数据新建/更新到数据库
    # 使用 INSERT ... ON CONFLICT (UPSERT) 语句，以 authoritative_full_name 为唯一键
    
    sql = """
    INSERT INTO service_names 
        (authoritative_full_name, base_name, internal_name, full_name_en, short_name_en, source, last_synced_at)
    VALUES 
        (%(auth_name)s, %(base_name)s, %(internal_name)s, %(full_name)s, %(short_name)s, 'web_scrape', NOW())
    ON CONFLICT (authoritative_full_name) 
    DO UPDATE SET
        base_name = EXCLUDED.base_name,
        internal_name = EXCLUDED.internal_name,
        full_name_en = EXCLUDED.full_name_en,
        short_name_en = EXCLUDED.short_name_en,
        source = EXCLUDED.source,
        last_synced_at = NOW(),
        updated_at = NOW();
    """
    
    params = {
        'auth_name': authoritative_name,
        'base_name': base_name,
        'internal_name': internal_name,
        'full_name': authoritative_name,
        'short_name': short_name
    }
    
    # 执行数据库操作
    # cursor.execute(sql, params)

# 提交事务
# db_connection.commit()

```

