# Requirements Document

## Introduction

本文档定义了 AWS 中国区 Mass Email 批量邮件翻译和用户反馈收集系统的需求。该系统采用多阶段处理流程，通过 JavaScript 预处理、LLM 翻译和后处理的方式，实现自动化的邮件翻译规范应用。同时提供基于 Tampermonkey 的用户反馈收集机制，整体采用混合云原生架构部署在 AWS 中国区。

系统核心包含两个主要功能模块：
1. **翻译自动化模块**：多阶段文本处理和翻译流水线
2. **反馈收集模块**：基于 Lambda + S3 + Tampermonkey 的用户反馈系统

## Requirements

### Requirement 1

**User Story:** 作为一个 PS 工程师，我希望系统能够自动应用 AWS 中国区翻译规范，以便提高邮件翻译的一致性和准确性。

#### Acceptance Criteria

1. WHEN 系统接收到原始英文邮件内容 THEN 系统 SHALL 自动将 "AWS" 替换为 "Amazon"
2. WHEN 系统处理包含 "AWS Support" 的内容 THEN 系统 SHALL 将其替换为 "Amazon Web Services"
3. WHEN 系统遇到 "all the AWS regions" 或 "all the commercial regions" THEN 系统 SHALL 替换为标准的中国区域描述
4. WHEN 系统处理时间信息 THEN 系统 SHALL 将 UTC 时间转换为 UTC+8 北京时间
5. WHEN 系统处理 CLI 命令 THEN 系统 SHALL 根据区域内容使用正确的 --region 参数

### Requirement 2

**User Story:** 作为一个 PS 工程师，我希望系统能够正确翻译 Amazon 相关术语，以便符合中国区的本地化要求。

#### Acceptance Criteria

1. WHEN 系统翻译 "Amazon" THEN 系统 SHALL 翻译为 "亚马逊云科技"
2. WHEN 系统翻译 "Amazon account" THEN 系统 SHALL 翻译为 "亚马逊云科技账户"
3. WHEN 系统翻译 "Amazon console" THEN 系统 SHALL 翻译为 "亚马逊云科技控制台"
4. WHEN 系统翻译 "Amazon Support" THEN 系统 SHALL 翻译为 "亚马逊云科技中国支持团队"
5. WHEN 系统翻译产品名称 THEN 系统 SHALL 首次使用全称，后续使用简称

### Requirement 3

**User Story:** 作为一个 PS 工程师，我希望系统能够处理链接本地化，以便确保所有链接在中国区可访问。

#### Acceptance Criteria

1. WHEN 系统遇到 "https://aws.amazon.com/support" THEN 系统 SHALL 替换为 "https://console.amazonaws.cn/support/"
2. WHEN 系统处理包含 "aws.amazon.com" 的链接 THEN 系统 SHALL 替换为 "amazonaws.cn" 或 "www.amazonaws.cn"
3. WHEN 系统处理链接 THEN 系统 SHALL 将所有大写字母转为小写
4. WHEN 系统遇到非 amazonaws.cn 域名的链接 THEN 系统 SHALL 标识但不修改

### Requirement 4

**User Story:** 作为一个用户，我希望能够在 AWS Issues 页面提供反馈，以便改进服务质量。

#### Acceptance Criteria

1. WHEN 用户访问 AWS Issues 页面 THEN 系统 SHALL 在页面加载后 2 秒注入反馈按钮
2. WHEN 用户点击反馈按钮 THEN 系统 SHALL 显示满意度调查表单
3. WHEN 用户提交反馈 THEN 系统 SHALL 调用 Lambda API 获取 S3 预签名 URL
4. WHEN 系统获得预签名 URL THEN 系统 SHALL 将反馈数据上传到 S3
5. WHEN 上传完成 THEN 系统 SHALL 显示成功状态反馈

### Requirement 5

**User Story:** 作为一个系统管理员，我希望反馈数据能够安全存储，以便后续分析和处理。

#### Acceptance Criteria

1. WHEN Lambda 函数接收到反馈请求 THEN 系统 SHALL 生成带有 5 分钟有效期的 S3 预签名 URL
2. WHEN 生成预签名 URL THEN 系统 SHALL 使用时间戳和 UUID 创建唯一文件名
3. WHEN 处理 CORS 请求 THEN 系统 SHALL 仅允许来自 "https://issues.cn-northwest-1.amazonaws.cn" 的请求
4. WHEN 存储反馈数据 THEN 系统 SHALL 使用 JSON 格式并按日期层级组织文件结构
5. WHEN 发生错误 THEN 系统 SHALL 记录详细日志到 CloudWatch

### Requirement 6

**User Story:** 作为一个开发者，我希望系统具有良好的可维护性和可扩展性，以便支持未来的功能扩展。

#### Acceptance Criteria

1. WHEN 部署 Lambda 函数 THEN 系统 SHALL 支持通过 AWS CLI 进行自动化部署
2. WHEN 更新 Tampermonkey 脚本 THEN 系统 SHALL 维护版本备份
3. WHEN 系统运行 THEN 系统 SHALL 提供详细的调试和监控信息
4. WHEN 配置环境变量 THEN 系统 SHALL 支持不同环境的配置管理
5. WHEN 进行本地测试 THEN 系统 SHALL 提供完整的测试事件和测试脚本

### Requirement 7

**User Story:** 作为一个 PS 工程师，我希望系统能够通过多阶段处理流程实现精确的翻译自动化，以便确保翻译的一致性和准确性。

#### Acceptance Criteria

1. WHEN 系统进入阶段 0 THEN 系统 SHALL 分析文本行类型（元数据行、Wording 行、普通内容行）
2. WHEN 系统进入阶段 1 THEN 系统 SHALL 执行 JavaScript 英文术语标准化并生成占位符
3. WHEN 系统进入阶段 2 THEN 系统 SHALL 通过 LLM 翻译所有非占位符的英文内容
4. WHEN 系统进入阶段 3 THEN 系统 SHALL 恢复占位符并执行最终中文格式化
5. WHEN 处理服务名称 THEN 系统 SHALL 根据全局或行内作用域追踪首次/后续出现状态

### Requirement 8

**User Story:** 作为系统架构师，我希望翻译系统采用组件化架构设计，支持高性能匹配算法和灵活的模式管理，以便实现高效的服务名称识别和处理。

#### Acceptance Criteria

1. WHEN 系统初始化 THEN 系统 SHALL 实现PatternLoader组件，负责从数据库加载活跃的正则表达式模式并构建内存查找结构
2. WHEN 执行文本匹配操作 THEN 系统 SHALL 实现ServiceMatcher组件，作为核心匹配引擎应用高性能算法识别服务名称
3. WHEN 处理复合后缀模式 THEN 系统 SHALL 实现SuffixAssembler组件，处理复合后缀模式并重组标准化名称
4. WHEN 进行边界保护 THEN 系统 SHALL 实现BoundaryGuard组件，剔除ARN、URL、代码标识符等特殊上下文中的误匹配
5. WHEN 组件协作 THEN 系统 SHALL 确保各组件间通过标准接口协作，支持独立测试和维护
6. WHEN 集成数据库 THEN 系统 SHALL 使用AWS服务同步系统提供的regex_patterns表数据，实现翻译引擎与数据同步系统的解耦

### Requirement 9

**User Story:** 作为一个 PS 工程师，我希望系统能够正确处理占位符机制，以便保护关键技术内容不被错误翻译。

#### Acceptance Criteria

1. WHEN 系统识别 CLI 命令 THEN 系统 SHALL 将其替换为 `__CLICMD_X__` 占位符
2. WHEN 系统识别 ARN 字符串 THEN 系统 SHALL 将其替换为 `__ARNSTR_X__` 占位符
3. WHEN 系统识别 JSON 代码块 THEN 系统 SHALL 将其替换为 `__JSONBLOCK_X__` 占位符
4. WHEN 系统识别服务名称 THEN 系统 SHALL 将其替换为 `__SRVCNM_X__` 占位符
5. WHEN LLM 处理文本 THEN 系统 SHALL 确保所有占位符原封不动地保留

### Requirement 10

**User Story:** 作为一个 PS 工程师，我希望系统能够精确处理时间转换，以便确保时区信息的准确性。

#### Acceptance Criteria

1. WHEN 系统遇到 PDT 时间 THEN 系统 SHALL 将其转换为 UTC+8（原时间 + 15 小时）
2. WHEN 系统遇到 PST 时间 THEN 系统 SHALL 将其转换为 UTC+8（原时间 + 16 小时）
3. WHEN 系统遇到 UTC 时间 THEN 系统 SHALL 将其转换为 UTC+8（原时间 + 8 小时）
4. WHEN 系统转换时间 THEN 系统 SHALL 使用 `YYYY年M月D日 上午/下午 h:mm 北京时间` 格式
5. WHEN 原文只有日期 THEN 系统 SHALL 格式化为 `YYYY年M月D日` 不添加时间

### Requirement 11

**User Story:** 作为一个用户，我希望反馈系统能够提供详细的调试信息，以便了解提交过程的状态。

#### Acceptance Criteria

1. WHEN 用户提交反馈 THEN 系统 SHALL 在模态框中显示实时调试日志
2. WHEN API 调用发生 THEN 系统 SHALL 记录请求状态和响应内容
3. WHEN 文件上传进行 THEN 系统 SHALL 显示上传进度和状态信息
4. WHEN 发生错误 THEN 系统 SHALL 提供详细的错误信息和建议
5. WHEN 操作成功 THEN 系统 SHALL 显示成功确认并自动关闭模态框

### Requirement 12

**User Story:** 作为一个系统管理员，我希望 Lambda 函数能够安全高效地处理反馈请求，以便确保系统的稳定性。

#### Acceptance Criteria

1. WHEN Lambda 函数启动 THEN 系统 SHALL 从环境变量读取 S3_BUCKET_NAME 和 ALLOWED_ORIGIN
2. WHEN 生成预签名 URL THEN 系统 SHALL 使用 `feedback/YYYY/MM/DD/HH-MM-SS-{uuid}.json` 文件路径格式
3. WHEN 设置 CORS 头 THEN 系统 SHALL 在成功和错误响应中都包含 Access-Control-Allow-Origin
4. WHEN 处理异常 THEN 系统 SHALL 记录详细错误日志并返回标准化错误响应
5. WHEN 生成响应 THEN 系统 SHALL 返回包含 uploadURL 和 objectKey 的 JSON 格式数据

### Requirement 13

**User Story:** 作为一个质量保证人员，我希望系统能够验证翻译质量，以便确保输出内容的准确性。

#### Acceptance Criteria

1. WHEN 系统完成翻译 THEN 系统 SHALL 验证所有产品名称使用的正确性
2. WHEN 系统处理区域信息 THEN 系统 SHALL 确保区域表述符合中国区标准
3. WHEN 系统处理时间格式 THEN 系统 SHALL 验证时区转换的准确性
4. WHEN 系统处理链接 THEN 系统 SHALL 验证链接在中国区的可访问性
5. WHEN 系统生成最终内容 THEN 系统 SHALL 确保语言流畅性和专业性

### Requirement 14

**User Story:** 作为性能工程师，我希望翻译系统采用基于Aho-Corasick + 分段正则的高性能匹配算法，实现O(n)复杂度的文本处理。

#### Acceptance Criteria

1. WHEN 构建匹配引擎 THEN 系统 SHALL 使用Aho-Corasick算法构建自动机，实现O(n)复杂度的预扫描
2. WHEN 执行文本匹配 THEN 系统 SHALL 只对预扫描命中的候选区域应用对应的正则表达式，避免全文正则扫描
3. WHEN 处理优先级冲突 THEN 系统 SHALL 实现优先级裁剪机制，一旦某个字符区间被高优先级模式命中，跳过低优先级模式检查
4. WHEN 解决重叠匹配 THEN 系统 SHALL 按优先级、长度、数据库ID顺序解决冲突，确保匹配结果的一致性和稳定性
5. WHEN 优化性能 THEN 系统 SHALL 使用部分索引和连接池，实现数据库查询性能提升10-50x
6. WHEN 处理大文本 THEN 系统 SHALL 支持分段处理和内存优化，避免大文本处理时的性能瓶颈

### Requirement 15

**User Story:** 作为数据架构师，我希望翻译系统支持JSONB元数据字段，实现灵活的模式管理和组件化架构支持。

#### Acceptance Criteria

1. WHEN 设计metadata字段结构 THEN 系统 SHALL 支持以下标准化的JSONB结构：
   ```json
   {
     "isCompoundWithSuffix": boolean,
     "suffixGroup": number,
     "patternCategory": string,
     "hasBoundaryProtection": boolean,
     "patternType": string,
     "specialServiceType": string,
     "serviceVariant": string,
     "boundaryProtectionTypes": array,
     "componentMetadata": object
   }
   ```
2. WHEN 存储基础模式属性 THEN 系统 SHALL 在metadata字段中包含isCompoundWithSuffix（是否复合后缀模式）、suffixGroup（后缀捕获组编号）、patternCategory（模式类别如"full_complex_suffix"）、hasBoundaryProtection（是否具有边界保护）等基础属性
3. WHEN 标识模式类型 THEN 系统 SHALL 在metadata字段的patternType中标记8种核心模式类型（如"full_complex_suffix"、"full_standard"、"short_complex_suffix"、"short_standard"、"abbrev_complex_suffix"、"abbrev_standard"、"special_variant"、"context_protection"）
4. WHEN 处理特殊服务 THEN 系统 SHALL 在metadata字段中包含specialServiceType（如"aurora"、"health"、"rds"、"normal"）和serviceVariant（如"postgresql"、"mysql"、"dashboard"）字段
5. WHEN 配置边界保护 THEN 系统 SHALL 在metadata字段的boundaryProtectionTypes数组中标记具体的保护类型（如["arn", "url", "code"]）
6. WHEN 支持组件化架构 THEN 系统 SHALL 在metadata字段的componentMetadata对象中存储各组件（PatternLoader、ServiceMatcher、SuffixAssembler、BoundaryGuard）所需的特定元数据
7. WHEN 处理复合模式 THEN 系统 SHALL 使用metadata字段中的suffixGroup信息进行后缀捕获组处理
8. WHEN 查询模式 THEN 系统 SHALL 支持基于metadata字段的GIN索引查询，提高查询性能
9. WHEN 元数据验证 THEN 系统 SHALL 验证metadata字段的JSON格式正确性、必要字段完整性和字段值的有效性（如patternType必须是8种核心类型之一）

### Requirement 16

**User Story:** 作为算法工程师，我希望翻译系统具备完善的优先级冲突解决算法，处理重叠匹配和优先级相等的情况。

#### Acceptance Criteria

1. WHEN 检测重叠匹配 THEN 系统 SHALL 将重叠的匹配按文本位置分组，识别需要解决冲突的匹配组
2. WHEN 优先级不同 THEN 系统 SHALL 选择优先级最高的匹配，遵循"数值越大优先级越高"原则
3. WHEN 优先级相等 THEN 系统 SHALL 应用最长匹配优先策略，选择匹配文本最长的模式
4. WHEN 长度也相等 THEN 系统 SHALL 选择数据库ID最小的模式（更早创建，更稳定）
5. WHEN 分组处理 THEN 系统 SHALL 对每个重叠组独立处理，确保不同区域的匹配不相互影响
6. WHEN 算法验证 THEN 系统 SHALL 确保冲突解决算法的确定性和可重现性，相同输入产生相同输出

### Requirement 17

**User Story:** 作为业务分析师，我希望翻译系统能够为Aurora、Health、RDS等特殊服务提供专门的处理策略。

#### Acceptance Criteria

1. WHEN 处理Aurora服务族 THEN 系统 SHALL 支持PostgreSQL、MySQL变体的专门处理，生成相应的变体模式
2. WHEN 处理Health服务族 THEN 系统 SHALL 区分Health Dashboard和通用Health服务，避免误匹配
3. WHEN 处理RDS服务族 THEN 系统 SHALL 支持多种数据库引擎的特定模式生成
4. WHEN 上下文感知 THEN 系统 SHALL 支持CLI命令和配置文件上下文的智能识别
5. WHEN 特殊变体模式 THEN 系统 SHALL 为特殊服务创建优先级125的变体模式，确保准确匹配
6. WHEN 服务分类 THEN 系统 SHALL 在metadata字段中标记特殊服务类型，便于后续处理和维护