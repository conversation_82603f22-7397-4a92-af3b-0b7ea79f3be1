-- ====================================================================
-- translation_jobs 表分区自动化管理 - 初始化脚本
-- 用途：为新数据库创建完整的分区管理功能
-- 版本：v1.0
-- 适用：PostgreSQL 13+
-- 创建：maintenance schema 及所有分区管理函数
-- ====================================================================

-- 创建维护专用 schema
CREATE SCHEMA IF NOT EXISTS maintenance;

-- 设置搜索路径以便后续函数调用
SET search_path TO maintenance, public;

-- ====================================================================
-- 工具函数：生成分区名称
-- 输入：年份、月份
-- 输出：标准分区名称 (translation_jobs_YYYY_MM)
-- ====================================================================
CREATE OR REPLACE FUNCTION maintenance.generate_partition_name(p_year int, p_month int)
RETURNS text 
LANGUAGE plpgsql 
IMMUTABLE AS $$
BEGIN
    -- 参数验证
    IF p_year IS NULL OR p_month IS NULL THEN
        RAISE EXCEPTION 'Year and month cannot be NULL';
    END IF;
    
    IF p_year < 2020 OR p_year > 2050 THEN
        RAISE EXCEPTION 'Year must be between 2020 and 2050, got: %', p_year;
    END IF;
    
    IF p_month < 1 OR p_month > 12 THEN
        RAISE EXCEPTION 'Month must be between 1 and 12, got: %', p_month;
    END IF;
    
    RETURN format('translation_jobs_%s_%s',
                  to_char(make_date(p_year, p_month, 1), 'YYYY'),
                  to_char(make_date(p_year, p_month, 1), 'MM'));
END $$;

-- ====================================================================
-- 工具函数：计算分区时间范围
-- 输入：年份、月份
-- 输出：分区的开始和结束日期
-- ====================================================================
CREATE OR REPLACE FUNCTION maintenance.calculate_partition_range(p_year int, p_month int)
RETURNS TABLE(start_date date, end_date date) 
LANGUAGE plpgsql 
IMMUTABLE AS $$
BEGIN
    -- 参数验证
    IF p_year IS NULL OR p_month IS NULL THEN
        RAISE EXCEPTION 'Year and month cannot be NULL';
    END IF;
    
    start_date := make_date(p_year, p_month, 1);
    end_date   := (start_date + INTERVAL '1 month')::date;
    RETURN NEXT;
END $$;

-- ====================================================================
-- 核心函数：确保指定月份的分区存在
-- 功能：创建分区并为其创建所有必要的索引
-- 参数：p_year (年份), p_month (月份)
-- 特点：幂等操作，重复执行不会出错
-- ====================================================================
CREATE OR REPLACE FUNCTION maintenance.ensure_month_partition(p_year int, p_month int)
RETURNS void 
LANGUAGE plpgsql AS $$
DECLARE
    part_name text;
    start_d date;
    end_d date;
    partition_exists boolean;
    
    -- 需要在分区上创建的索引定义（与父表保持一致）
    idx_definitions text[] := ARRAY[
        'CREATE INDEX IF NOT EXISTS %I ON %I (status)',
        'CREATE INDEX IF NOT EXISTS %I ON %I (submitted_by)',
        'CREATE INDEX IF NOT EXISTS %I ON %I (submitted_at)',
        'CREATE INDEX IF NOT EXISTS %I ON %I (completed_at)',
        'CREATE INDEX IF NOT EXISTS %I ON %I USING GIN (placeholder_map)',
        'CREATE INDEX IF NOT EXISTS %I ON %I USING GIN (service_mention_state)'
    ];
    
    -- 对应的父表索引名称（用于生成分区索引名）
    idx_base_names text[] := ARRAY[
        'idx_translation_jobs_status',
        'idx_translation_jobs_submitted_by',
        'idx_translation_jobs_submitted_at',
        'idx_translation_jobs_completed_at',
        'idx_translation_jobs_placeholder_map',
        'idx_translation_jobs_service_mention_state'
    ];
    
    i int;
    part_idx_name text;
    created_indexes int := 0;
BEGIN
    -- 生成分区名称和时间范围
    part_name := maintenance.generate_partition_name(p_year, p_month);
    SELECT start_date, end_date INTO start_d, end_d
    FROM maintenance.calculate_partition_range(p_year, p_month);

    RAISE NOTICE 'Processing partition: % (% to %)', part_name, start_d, end_d;

    -- 检查分区是否已存在
    SELECT EXISTS (
        SELECT 1
        FROM pg_class c
        JOIN pg_inherits i ON c.oid = i.inhrelid
        JOIN pg_class p ON p.oid = i.inhparent
        WHERE p.relname = 'translation_jobs' 
          AND c.relname = part_name
    ) INTO partition_exists;

    -- 创建分区（如果不存在）
    IF NOT partition_exists THEN
        EXECUTE format(
            'CREATE TABLE %I PARTITION OF translation_jobs FOR VALUES FROM (%L) TO (%L)',
            part_name, start_d::text, end_d::text
        );
        RAISE NOTICE 'Created partition: %', part_name;
    ELSE
        RAISE NOTICE 'Partition % already exists, skipping creation', part_name;
    END IF;

    -- 为分区创建索引
    FOR i IN ARRAY_LOWER(idx_definitions, 1) .. ARRAY_UPPER(idx_definitions, 1) LOOP
        part_idx_name := format('%s_%s', idx_base_names[i], to_char(start_d, 'YYYY_MM'));
        
        BEGIN
            EXECUTE format(idx_definitions[i], part_idx_name, part_name);
            created_indexes := created_indexes + 1;
            RAISE NOTICE 'Created/verified index: %', part_idx_name;
        EXCEPTION WHEN OTHERS THEN
            RAISE WARNING 'Failed to create index % on partition %: %', part_idx_name, part_name, SQLERRM;
        END;
    END LOOP;

    RAISE NOTICE 'Partition % setup complete. Created/verified % indexes.', part_name, created_indexes;
END $$;

-- ====================================================================
-- 便捷函数：确保下个月分区存在
-- 功能：自动计算下个月的年份和月份，创建对应分区
-- 用途：定时任务调用
-- ====================================================================
CREATE OR REPLACE FUNCTION maintenance.ensure_next_month_partition()
RETURNS void 
LANGUAGE plpgsql AS $$
DECLARE
    next_date date := (CURRENT_DATE + INTERVAL '1 month')::date;
    y int := EXTRACT(YEAR FROM next_date)::int;
    m int := EXTRACT(MONTH FROM next_date)::int;
BEGIN
    RAISE NOTICE 'Creating next month partition for: %-%', y, m;
    PERFORM maintenance.ensure_month_partition(y, m);
    RAISE NOTICE 'Next month partition creation completed';
END $$;

-- ====================================================================
-- 便捷函数：确保未来N个月的分区存在
-- 功能：批量创建未来几个月的分区
-- 参数：months_ahead (提前创建的月数，默认3个月)
-- 用途：初始化或批量准备分区
-- ====================================================================
CREATE OR REPLACE FUNCTION maintenance.ensure_future_partitions(months_ahead int DEFAULT 3)
RETURNS void 
LANGUAGE plpgsql AS $$
DECLARE
    i int;
    target_date date;
    y int;
    m int;
BEGIN
    -- 参数验证
    IF months_ahead IS NULL OR months_ahead < 1 OR months_ahead > 24 THEN
        RAISE EXCEPTION 'months_ahead must be between 1 and 24, got: %', months_ahead;
    END IF;

    RAISE NOTICE 'Creating partitions for next % months', months_ahead;
    
    FOR i IN 1..months_ahead LOOP
        target_date := (CURRENT_DATE + (i || ' months')::interval)::date;
        y := EXTRACT(YEAR FROM target_date)::int;
        m := EXTRACT(MONTH FROM target_date)::int;
        
        PERFORM maintenance.ensure_month_partition(y, m);
    END LOOP;
    
    RAISE NOTICE 'Future partitions creation completed';
END $$;

-- ====================================================================
-- 清理函数：删除历史分区
-- 功能：保留最近N个月的分区，删除更早的分区
-- 参数：retention_months (保留月数，默认6个月)
-- 安全特性：不会删除 default 分区，包含多重安全检查
-- ====================================================================
CREATE OR REPLACE FUNCTION maintenance.purge_old_partitions(retention_months int DEFAULT 6)
RETURNS void 
LANGUAGE plpgsql AS $$
DECLARE
    keep_from_date date;
    r RECORD;
    part_year int;
    part_month int;
    part_start_date date;
    part_end_date date;
    deleted_count int := 0;
    total_rows bigint;
BEGIN
    -- 参数验证
    IF retention_months IS NULL OR retention_months < 1 THEN
        RAISE EXCEPTION 'retention_months must be >= 1, got: %', retention_months;
    END IF;
    
    IF retention_months > 60 THEN
        RAISE EXCEPTION 'retention_months cannot exceed 60 (5 years), got: %', retention_months;
    END IF;

    -- 计算保留的起始日期（当前月份往前推N个月）
    keep_from_date := date_trunc('month', CURRENT_DATE)::date - 
                      (INTERVAL '1 month' * (retention_months - 1));
    
    RAISE NOTICE 'Purging partitions older than: % (keeping % months)', keep_from_date, retention_months;

    -- 查找所有符合命名规则的分区（排除 default 分区）
    FOR r IN
        SELECT c.relname AS partition_name
        FROM pg_class c
        JOIN pg_inherits i ON c.oid = i.inhrelid
        JOIN pg_class p ON p.oid = i.inhparent
        WHERE p.relname = 'translation_jobs'
          AND c.relname ~ '^translation_jobs_[0-9]{4}_[0-9]{2}$'
          AND c.relname != 'translation_jobs_default'
        ORDER BY c.relname
    LOOP
        -- 从分区名称中提取年份和月份
        part_year  := substring(r.partition_name from 'translation_jobs_([0-9]{4})_')::int;
        part_month := substring(r.partition_name from 'translation_jobs_[0-9]{4}_([0-9]{2})')::int;

        -- 计算该分区的时间范围
        SELECT start_date, end_date INTO part_start_date, part_end_date
        FROM maintenance.calculate_partition_range(part_year, part_month);

        -- 如果分区结束时间早于保留起始时间，则删除该分区
        IF part_end_date < keep_from_date THEN
            -- 安全检查：确认分区不是当前月份或未来月份
            IF part_end_date <= CURRENT_DATE THEN
                -- 记录分区中的数据量（用于日志）
                EXECUTE format('SELECT COUNT(*) FROM %I', r.partition_name) INTO total_rows;
                
                -- 删除分区
                EXECUTE format('DROP TABLE %I CASCADE', r.partition_name);
                deleted_count := deleted_count + 1;
                
                RAISE NOTICE 'Dropped partition: % (contained % rows, period: % to %)', 
                    r.partition_name, total_rows, part_start_date, part_end_date;
            ELSE
                RAISE WARNING 'Skipped partition % (end date % is in the future)', 
                    r.partition_name, part_end_date;
            END IF;
        ELSE
            RAISE NOTICE 'Keeping partition: % (period: % to %)', 
                r.partition_name, part_start_date, part_end_date;
        END IF;
    END LOOP;

    IF deleted_count = 0 THEN
        RAISE NOTICE 'No partitions were deleted (all within retention period)';
    ELSE
        RAISE NOTICE 'Purge completed: deleted % old partitions', deleted_count;
    END IF;
END $$;

-- ====================================================================
-- 信息函数：列出所有分区的详细信息
-- 功能：显示分区名称、时间范围、数据量等统计信息
-- 用途：监控和诊断
-- ====================================================================
CREATE OR REPLACE FUNCTION maintenance.list_partition_info()
RETURNS TABLE(
    partition_name text,
    start_date date,
    end_date date,
    row_count bigint,
    size_pretty text,
    is_default boolean
) 
LANGUAGE plpgsql AS $$
DECLARE
    r RECORD;
    part_year int;
    part_month int;
BEGIN
    -- 遍历所有 translation_jobs 的分区
    FOR r IN
        SELECT c.relname AS pname
        FROM pg_class c
        JOIN pg_inherits i ON c.oid = i.inhrelid
        JOIN pg_class p ON p.oid = i.inhparent
        WHERE p.relname = 'translation_jobs'
        ORDER BY c.relname
    LOOP
        partition_name := r.pname;
        is_default := (r.pname = 'translation_jobs_default');
        
        IF NOT is_default AND r.pname ~ '^translation_jobs_[0-9]{4}_[0-9]{2}$' THEN
            -- 常规月份分区：解析时间范围
            part_year  := substring(r.pname from 'translation_jobs_([0-9]{4})_')::int;
            part_month := substring(r.pname from 'translation_jobs_[0-9]{4}_([0-9]{2})')::int;
            
            SELECT pr.start_date, pr.end_date 
            INTO start_date, end_date
            FROM maintenance.calculate_partition_range(part_year, part_month) pr;
        ELSE
            -- default 分区或其他特殊分区
            start_date := NULL;
            end_date := NULL;
        END IF;
        
        -- 获取分区数据量和大小
        EXECUTE format('SELECT COUNT(*) FROM %I', r.pname) INTO row_count;
        EXECUTE format('SELECT pg_size_pretty(pg_total_relation_size(%L))', r.pname) INTO size_pretty;
        
        RETURN NEXT;
    END LOOP;
END $$;

-- ====================================================================
-- 验证函数：检查分区索引完整性
-- 功能：验证每个分区是否具有所有必要的索引
-- 返回：缺失索引的分区列表
-- ====================================================================
CREATE OR REPLACE FUNCTION maintenance.verify_partition_indexes()
RETURNS TABLE(
    partition_name text,
    missing_indexes text[]
) 
LANGUAGE plpgsql AS $$
DECLARE
    r RECORD;
    expected_indexes text[] := ARRAY[
        'status', 'submitted_by', 'submitted_at', 'completed_at', 
        'placeholder_map', 'service_mention_state'
    ];
    existing_indexes text[];
    missing text[];
    idx text;
BEGIN
    -- 遍历所有非 default 分区
    FOR r IN
        SELECT c.relname AS pname
        FROM pg_class c
        JOIN pg_inherits i ON c.oid = i.inhrelid
        JOIN pg_class p ON p.oid = i.inhparent
        WHERE p.relname = 'translation_jobs'
          AND c.relname != 'translation_jobs_default'
        ORDER BY c.relname
    LOOP
        partition_name := r.pname;
        missing := '{}';
        
        -- 获取该分区上的所有索引涉及的列
        SELECT array_agg(DISTINCT a.attname)
        INTO existing_indexes
        FROM pg_index idx
        JOIN pg_class t ON t.oid = idx.indrelid
        JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(idx.indkey)
        WHERE t.relname = r.pname
          AND a.attname = ANY(expected_indexes);
        
        -- 检查缺失的索引
        FOREACH idx IN ARRAY expected_indexes LOOP
            IF existing_indexes IS NULL OR NOT (idx = ANY(existing_indexes)) THEN
                missing := array_append(missing, idx);
            END IF;
        END LOOP;
        
        -- 只返回有缺失索引的分区
        IF array_length(missing, 1) > 0 THEN
            missing_indexes := missing;
            RETURN NEXT;
        END IF;
    END LOOP;
END $$;

-- ====================================================================
-- 初始化完成提示
-- ====================================================================
DO $$
BEGIN
    RAISE NOTICE '=== Partition Management Functions Initialized ===';
    RAISE NOTICE 'Schema: maintenance';
    RAISE NOTICE 'Functions created:';
    RAISE NOTICE '  - generate_partition_name(year, month)';
    RAISE NOTICE '  - calculate_partition_range(year, month)';
    RAISE NOTICE '  - ensure_month_partition(year, month)';
    RAISE NOTICE '  - ensure_next_month_partition()';
    RAISE NOTICE '  - ensure_future_partitions(months_ahead)';
    RAISE NOTICE '  - purge_old_partitions(retention_months)';
    RAISE NOTICE '  - list_partition_info()';
    RAISE NOTICE '  - verify_partition_indexes()';
    RAISE NOTICE '================================================';
END $$;

-- ====================================================================
-- 使用示例和测试语句
-- ====================================================================

-- 示例1：创建下个月分区
-- SELECT maintenance.ensure_next_month_partition();

-- 示例2：创建未来3个月的分区
-- SELECT maintenance.ensure_future_partitions(3);

-- 示例3：清理6个月前的历史分区
-- SELECT maintenance.purge_old_partitions(6);

-- 示例4：查看所有分区信息
-- SELECT * FROM maintenance.list_partition_info() ORDER BY partition_name;

-- 示例5：验证分区索引完整性
-- SELECT * FROM maintenance.verify_partition_indexes();

-- 示例6：手动创建特定月份分区
-- SELECT maintenance.ensure_month_partition(2025, 4);


-- ====================================================================
-- translation_jobs 表分区监控和验证脚本
-- 用途：提供完整的分区状态监控、验证和统计查询
-- 版本：v1.0
-- ====================================================================

-- ====================================================================
-- 1. 基础分区信息查询
-- ====================================================================

-- 查询1：列出所有分区的基本信息
SELECT 
    c.relname AS partition_name,
    CASE 
        WHEN c.relname = 'translation_jobs_default' THEN 'DEFAULT'
        WHEN c.relname ~ '^translation_jobs_[0-9]{4}_[0-9]{2}$' THEN 'MONTHLY'
        ELSE 'OTHER'
    END AS partition_type,
    pg_size_pretty(pg_total_relation_size(c.oid)) AS size,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = c.relname) AS exists_check
FROM pg_class c
JOIN pg_inherits i ON c.oid = i.inhrelid
JOIN pg_class p ON p.oid = i.inhparent
WHERE p.relname = 'translation_jobs'
ORDER BY c.relname;

-- 查询2：使用管理函数获取详细分区信息
SELECT 
    partition_name,
    CASE 
        WHEN is_default THEN 'DEFAULT'
        ELSE start_date::text || ' to ' || end_date::text
    END AS date_range,
    row_count,
    size_pretty,
    is_default
FROM maintenance.list_partition_info()
ORDER BY 
    is_default DESC,
    partition_name;

-- ====================================================================
-- 2. 分区索引完整性验证
-- ====================================================================

-- 查询3：检查缺失索引的分区
SELECT 
    partition_name,
    array_to_string(missing_indexes, ', ') AS missing_indexes_list
FROM maintenance.verify_partition_indexes()
ORDER BY partition_name;

-- 查询4：详细的索引统计（每个分区的索引数量）
SELECT 
    t.relname AS partition_name,
    COUNT(i.indexrelid) AS index_count,
    array_agg(ic.relname ORDER BY ic.relname) AS index_names
FROM pg_class t
JOIN pg_inherits inh ON t.oid = inh.inhrelid
JOIN pg_class p ON p.oid = inh.inhparent
LEFT JOIN pg_index i ON i.indrelid = t.oid
LEFT JOIN pg_class ic ON ic.oid = i.indexrelid
WHERE p.relname = 'translation_jobs'
  AND t.relname != 'translation_jobs_default'
GROUP BY t.relname
ORDER BY t.relname;

-- ====================================================================
-- 3. 数据分布统计
-- ====================================================================

-- 查询5：按月统计数据分布（需要实际执行动态SQL）
DO $$
DECLARE
    r RECORD;
    sql_text TEXT;
    result_text TEXT := '';
BEGIN
    RAISE NOTICE 'Data distribution by partition:';
    RAISE NOTICE '================================';
    
    FOR r IN
        SELECT c.relname AS partition_name
        FROM pg_class c
        JOIN pg_inherits i ON c.oid = i.inhrelid
        JOIN pg_class p ON p.oid = i.inhparent
        WHERE p.relname = 'translation_jobs'
        ORDER BY c.relname
    LOOP
        EXECUTE format('SELECT COUNT(*) FROM %I', r.partition_name) INTO sql_text;
        RAISE NOTICE '%: % rows', r.partition_name, sql_text;
    END LOOP;
    
    RAISE NOTICE '================================';
END $$;

-- 查询6：分区大小排序
SELECT 
    schemaname,
    tablename AS partition_name,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size,
    pg_total_relation_size(schemaname||'.'||tablename) AS size_bytes
FROM pg_tables 
WHERE tablename LIKE 'translation_jobs_%'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- ====================================================================
-- 4. 分区健康检查
-- ====================================================================

-- 查询7：检查分区约束和范围
SELECT 
    schemaname,
    tablename AS partition_name,
    pg_get_expr(c.relpartbound, c.oid) AS partition_bounds
FROM pg_tables pt
JOIN pg_class c ON c.relname = pt.tablename
WHERE pt.tablename LIKE 'translation_jobs_%'
  AND pt.tablename != 'translation_jobs_default'
ORDER BY pt.tablename;

-- 查询8：检查是否有数据落入 default 分区（应该为空或很少）
SELECT 
    'translation_jobs_default' AS partition_name,
    COUNT(*) AS row_count,
    CASE 
        WHEN COUNT(*) = 0 THEN 'GOOD: No data in default partition'
        WHEN COUNT(*) < 100 THEN 'WARNING: Some data in default partition'
        ELSE 'ALERT: Too much data in default partition'
    END AS status
FROM translation_jobs_default;

-- ====================================================================
-- 5. 性能监控查询
-- ====================================================================

-- 查询9：分区访问统计（需要 pg_stat_user_tables）
SELECT 
    schemaname,
    relname AS partition_name,
    seq_scan,
    seq_tup_read,
    idx_scan,
    idx_tup_fetch,
    n_tup_ins AS inserts,
    n_tup_upd AS updates,
    n_tup_del AS deletes
FROM pg_stat_user_tables 
WHERE relname LIKE 'translation_jobs_%'
ORDER BY relname;

-- 查询10：分区索引使用统计
SELECT 
    schemaname,
    relname AS partition_name,
    indexrelname AS index_name,
    idx_scan AS index_scans,
    idx_tup_read AS tuples_read,
    idx_tup_fetch AS tuples_fetched
FROM pg_stat_user_indexes 
WHERE relname LIKE 'translation_jobs_%'
ORDER BY relname, indexrelname;

-- ====================================================================
-- 6. 分区维护建议
-- ====================================================================

-- 查询11：生成分区维护建议
DO $$
DECLARE
    current_partitions INT;
    future_partitions INT;
    old_partitions INT;
    next_month_date DATE;
    cutoff_date DATE;
BEGIN
    -- 统计当前分区数量
    SELECT COUNT(*) INTO current_partitions
    FROM pg_class c
    JOIN pg_inherits i ON c.oid = i.inhrelid
    JOIN pg_class p ON p.oid = i.inhparent
    WHERE p.relname = 'translation_jobs'
      AND c.relname ~ '^translation_jobs_[0-9]{4}_[0-9]{2}$';
    
    -- 检查下个月分区是否存在
    next_month_date := (CURRENT_DATE + INTERVAL '1 month')::DATE;
    SELECT COUNT(*) INTO future_partitions
    FROM pg_class c
    JOIN pg_inherits i ON c.oid = i.inhrelid
    JOIN pg_class p ON p.oid = i.inhparent
    WHERE p.relname = 'translation_jobs'
      AND c.relname = maintenance.generate_partition_name(
          EXTRACT(YEAR FROM next_month_date)::INT,
          EXTRACT(MONTH FROM next_month_date)::INT
      );
    
    -- 检查是否有超过6个月的旧分区
    cutoff_date := date_trunc('month', CURRENT_DATE - INTERVAL '6 months')::DATE;
    
    RAISE NOTICE '=== Partition Maintenance Recommendations ===';
    RAISE NOTICE 'Current date: %', CURRENT_DATE;
    RAISE NOTICE 'Total monthly partitions: %', current_partitions;
    RAISE NOTICE '';
    
    IF future_partitions = 0 THEN
        RAISE NOTICE 'RECOMMENDATION: Create next month partition';
        RAISE NOTICE 'Command: SELECT maintenance.ensure_next_month_partition();';
    ELSE
        RAISE NOTICE 'OK: Next month partition exists';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE 'To create future partitions: SELECT maintenance.ensure_future_partitions(3);';
    RAISE NOTICE 'To cleanup old partitions: SELECT maintenance.purge_old_partitions(6);';
    RAISE NOTICE '============================================';
END $$;

-- ====================================================================
-- 7. 快速健康检查汇总
-- ====================================================================

-- 查询12：一键健康检查汇总
SELECT 
    'Partition Count' AS metric,
    COUNT(*)::TEXT AS value,
    'Total partitions including default' AS description
FROM pg_class c
JOIN pg_inherits i ON c.oid = i.inhrelid
JOIN pg_class p ON p.oid = i.inhparent
WHERE p.relname = 'translation_jobs'

UNION ALL

SELECT 
    'Monthly Partitions' AS metric,
    COUNT(*)::TEXT AS value,
    'Regular monthly partitions' AS description
FROM pg_class c
JOIN pg_inherits i ON c.oid = i.inhrelid
JOIN pg_class p ON p.oid = i.inhparent
WHERE p.relname = 'translation_jobs'
  AND c.relname ~ '^translation_jobs_[0-9]{4}_[0-9]{2}$'

UNION ALL

SELECT 
    'Total Rows' AS metric,
    (SELECT SUM(row_count)::TEXT FROM maintenance.list_partition_info()) AS value,
    'Total rows across all partitions' AS description

UNION ALL

SELECT 
    'Total Size' AS metric,
    pg_size_pretty(SUM(pg_total_relation_size(c.oid))) AS value,
    'Total size of all partitions' AS description
FROM pg_class c
JOIN pg_inherits i ON c.oid = i.inhrelid
JOIN pg_class p ON p.oid = i.inhparent
WHERE p.relname = 'translation_jobs'

ORDER BY metric;




-- ====================================================================
-- translation_jobs 表分区自动化 - pg_cron 定时任务配置
-- 用途：设置自动分区创建和清理的定时任务
-- 前提：需要先安装 pg_cron 扩展
-- ====================================================================

-- ====================================================================
-- 1. 安装 pg_cron 扩展（需要超级用户权限）
-- ====================================================================
-- 注意：在 RDS 环境中，pg_cron 可能需要在参数组中启用
-- CREATE EXTENSION IF NOT EXISTS pg_cron;

-- ====================================================================
-- 2. 配置定时任务
-- ====================================================================

-- 任务1：每日凌晨2:10创建下个月分区（确保提前准备）
SELECT cron.schedule(
    'partition_ensure_next_month',           -- 任务名称
    '10 2 * * *',                           -- 每日凌晨2:10执行
    $$SELECT maintenance.ensure_next_month_partition();$$  -- 执行的SQL
);

-- 任务2：每周日凌晨3:20清理历史分区（保留6个月）
SELECT cron.schedule(
    'partition_cleanup_old',                 -- 任务名称
    '20 3 * * 0',                           -- 每周日凌晨3:20执行
    $$SELECT maintenance.purge_old_partitions(6);$$  -- 执行的SQL
);

-- 任务3：每月1日凌晨4:00创建未来3个月的分区（保险措施）
SELECT cron.schedule(
    'partition_ensure_future',               -- 任务名称
    '0 4 1 * *',                            -- 每月1日凌晨4:00执行
    $$SELECT maintenance.ensure_future_partitions(3);$$  -- 执行的SQL
);

-- ====================================================================
-- 3. 验证定时任务配置
-- ====================================================================

-- 查看所有已配置的定时任务
SELECT 
    jobid,
    schedule,
    command,
    nodename,
    nodeport,
    database,
    username,
    active,
    jobname
FROM cron.job 
WHERE jobname LIKE 'partition_%'
ORDER BY jobname;

-- ====================================================================
-- 4. 任务管理命令
-- ====================================================================

-- 手动执行特定任务（测试用）
-- SELECT cron.run_job((SELECT jobid FROM cron.job WHERE jobname = 'partition_ensure_next_month'));

-- 暂停任务
-- UPDATE cron.job SET active = false WHERE jobname = 'partition_cleanup_old';

-- 恢复任务
-- UPDATE cron.job SET active = true WHERE jobname = 'partition_cleanup_old';

-- 删除任务
-- SELECT cron.unschedule('partition_ensure_next_month');

-- ====================================================================
-- 5. 监控定时任务执行历史
-- ====================================================================

-- 查看任务执行历史（最近10次）
SELECT 
    jobid,
    runid,
    job_pid,
    database,
    username,
    command,
    status,
    return_message,
    start_time,
    end_time,
    end_time - start_time AS duration
FROM cron.job_run_details 
WHERE jobid IN (SELECT jobid FROM cron.job WHERE jobname LIKE 'partition_%')
ORDER BY start_time DESC 
LIMIT 10;

-- 查看失败的任务执行
SELECT 
    j.jobname,
    jrd.command,
    jrd.return_message,
    jrd.start_time,
    jrd.end_time
FROM cron.job_run_details jrd
JOIN cron.job j ON j.jobid = jrd.jobid
WHERE j.jobname LIKE 'partition_%'
  AND jrd.status = 'failed'
ORDER BY jrd.start_time DESC;

-- ====================================================================
-- 6. 定时任务配置建议
-- ====================================================================

DO $$
BEGIN
    RAISE NOTICE '=== Partition Cron Jobs Configuration ===';
    RAISE NOTICE 'Configured jobs:';
    RAISE NOTICE '1. partition_ensure_next_month: Daily at 02:10';
    RAISE NOTICE '2. partition_cleanup_old: Weekly Sunday at 03:20';
    RAISE NOTICE '3. partition_ensure_future: Monthly 1st at 04:00';
    RAISE NOTICE '';
    RAISE NOTICE 'Monitoring commands:';
    RAISE NOTICE '- View jobs: SELECT * FROM cron.job WHERE jobname LIKE ''partition_%'';';
    RAISE NOTICE '- View history: SELECT * FROM cron.job_run_details ORDER BY start_time DESC LIMIT 5;';
    RAISE NOTICE '- Manual run: SELECT cron.run_job(jobid) FROM cron.job WHERE jobname = ''job_name'';';
    RAISE NOTICE '';
    RAISE NOTICE 'Time zone note: Cron jobs run in database server timezone';
    RAISE NOTICE 'Current server time: %', NOW();
    RAISE NOTICE '=====================================';
END $$;



-- 1. 测试创建下个月分区
SELECT maintenance.ensure_next_month_partition();

-- 2. 测试创建未来分区
SELECT maintenance.ensure_future_partitions(2);

-- 3. 查看分区状态
SELECT * FROM maintenance.list_partition_info() ORDER BY partition_name;

-- 4. 验证索引完整性
SELECT * FROM maintenance.verify_partition_indexes();

-- 5. 测试清理功能（谨慎使用）
-- SELECT maintenance.purge_old_partitions(12); -- 保留12个月，测试用







-- 回滚策略
-- 如需回滚分区管理功能：
-- 1. 删除定时任务
SELECT cron.unschedule(jobname) FROM cron.job WHERE jobname LIKE 'partition_%';

-- 2. 删除管理函数
DROP SCHEMA maintenance CASCADE;

-- 注意：已创建的分区和数据不会被删除