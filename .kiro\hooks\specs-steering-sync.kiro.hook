{"enabled": true, "name": "Specs-Steering Sync", "description": "Monitors changes to any project specification files and triggers review and update of related steering documents to ensure specs and steering remain synchronized", "version": "1", "when": {"type": "fileCreated", "patterns": [".kiro/specs/**/*"]}, "then": {"type": "askAgent", "prompt": "项目specs文件已更新。请检查并更新steering目录中的相关部分，确保specs和steering文档保持同步。具体任务：\n\n1. 分析更新的specs文件内容和变更\n2. 识别需要同步更新的steering文档\n3. 更新相关的steering文档以反映specs的变更\n4. 确保技术规范、开发指导和系统上下文的一致性\n\n请重点关注：\n- mass-email-system-context.md 的系统概述部分\n- database-development-guide.md 的数据库设计指导\n- development-workflow-guide.md 的开发流程\n- 其他相关的steering文档\n\n确保所有steering文档与最新的specs保持一致。"}}