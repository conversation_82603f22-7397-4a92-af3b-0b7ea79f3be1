# 数据库设计需求文档

## 项目概述

本文档定义了AWS中国区Mass Email批量邮件翻译和用户反馈收集系统的数据库设计需求。系统基于"逻辑外化"设计哲学，采用PostgreSQL 13+作为核心数据存储引擎，支持ACID事务特性和高并发访问。

数据库架构需要支持四大核心功能模块：
1. **翻译自动化模块** - 4阶段翻译流水线处理，支持占位符保护和服务名称首次/后续提及追踪
2. **反馈收集模块** - 用户反馈数据收集和S3集成，支持满意度统计和质量分析
3. **服务名称同步模块** - AWS服务名称的多数据源同步（web_scrape、pdf_parse、manual），支持v2架构的权威数据源管理
4. **正则表达式模式管理** - 8种核心模式类型的智能匹配系统，支持复合后缀处理和边界保护机制

核心设计原则包括：
- **逻辑外化** - 将业务规则存储在数据库中，支持动态更新而无需重新部署代码
- **过程可追溯** - translation_jobs表记录翻译任务的4阶段完整生命周期
- **原子化与规范化** - 不同类型规则分离到独立表，采用ENUM类型提供类型安全
- **JSONB优化** - 半结构化数据使用JSONB字段，配合GIN索引实现高效查询
- **高性能策略** - 部分索引、Aho-Corasick预扫描、组件化架构等v2.0核心优化

## 需求规格

### 需求 1

**用户故事：** 作为一个系统架构师，我希望数据库能够支持4阶段翻译流水线的完整处理，以便实现高效的翻译任务管理和状态跟踪。

#### 验收标准

1. WHEN 系统创建翻译任务 THEN 数据库 SHALL 使用UUID主键存储任务的完整生命周期信息
2. WHEN 翻译任务进入不同阶段 THEN 数据库 SHALL 记录Stage 0-3的处理状态和时间戳
3. WHEN 系统生成占位符 THEN 数据库 SHALL 使用JSONB字段存储placeholder_map映射关系
4. WHEN 翻译任务完成 THEN 数据库 SHALL 保存stage1_standardized_en、stage2_llm_input、stage2_llm_output、stage3_final_cn的完整数据
5. WHEN 系统追踪服务提及状态 THEN 数据库 SHALL 使用JSONB字段存储service_mention_state信息

### 需求 2

**用户故事：** 作为一个数据管理员，我希望数据库能够管理AWS服务名称的v2架构权威数据源，以便支持多数据源同步和准确的服务名称标准化。

#### 验收标准

1. WHEN 系统同步服务名称 THEN 数据库 SHALL 使用authoritative_full_name作为业务主键存储权威服务全称
2. WHEN 处理多数据源 THEN 数据库 SHALL 区分web_scrape、pdf_parse、manual三种数据来源
3. WHEN 系统需要翻译逻辑 THEN 数据库 SHALL 提供base_name、full_name_en、short_name_en字段支持首次/后续提及
4. WHEN 同步任务执行 THEN 数据库 SHALL 记录last_synced_at时间戳和source来源信息
5. WHEN 系统处理PDF数据 THEN 数据库 SHALL 存储internal_name和service_code映射关系

### 需求 3

**用户故事：** 作为一个正则表达式管理员，我希望数据库能够支持8种核心模式类型的智能匹配系统，以便实现高质量的服务名称识别和复合后缀处理。

#### 验收标准

1. WHEN 系统生成正则模式 THEN 数据库 SHALL 支持8种核心模式类型（全称复合后缀120、全称标准115、简称复合后缀110、简称标准105、缩写复合后缀100、缩写标准95、特殊变体125、上下文保护90）
2. WHEN 处理复合后缀模式 THEN 数据库 SHALL 在metadata JSONB字段存储{"isCompoundWithSuffix": true, "suffixGroup": 1, "patternCategory": "full_complex_suffix"}结构化元数据
3. WHEN 系统匹配服务名称 THEN 数据库 SHALL 支持优先级90-130的分层匹配机制，数字越大优先级越高
4. WHEN 验证正则模式 THEN 数据库 SHALL 记录validation_status状态（pending、valid、invalid）并支持长度约束检查（<10000字符）
5. WHEN 关联服务信息 THEN 数据库 SHALL 通过related_service_id和service_code建立外键关联，支持按服务分组管理
6. WHEN 使用ENUM类型 THEN 数据库 SHALL 通过regex_pattern_type枚举提供类型安全（SERVICE_NAME、TIMEZONE、CLI_COMMAND、URL、GENERAL、CONTEXT_PROTECTED）
7. WHEN 存储验证状态 THEN 数据库 SHALL 使用枚举类型 `validation_status_type`（pending、valid、invalid）定义 `regex_patterns.validation_status`

### 需求 4

**用户故事：** 作为一个产品经理，我希望数据库能够完整记录用户反馈信息并与S3集成，以便进行质量分析和持续改进。

#### 验收标准

1. WHEN 用户提交反馈 THEN 数据库 SHALL 使用UUID主键存储反馈的详细内容和元数据信息
2. WHEN 反馈存储到S3 THEN 数据库 SHALL 记录s3_object_key确保数据完整性
3. WHEN 反馈关联翻译任务 THEN 数据库 SHALL 通过translation_job_id建立外键关联
4. WHEN 系统分析反馈趋势 THEN 数据库 SHALL 支持satisfaction枚举（satisfied、unsatisfied、neutral）的统计查询
5. WHEN 收集用户环境信息 THEN 数据库 SHALL 存储page_url和user_agent元数据

### 需求 5

**用户故事：** 作为一个品牌术语管理员，我希望数据库能够支持品牌术语和URL映射的动态管理，以便实现AWS中国区本地化规则的灵活配置。

#### 验收标准

1. WHEN 系统处理品牌术语 THEN 数据库 SHALL 存储term_en到term_cn的强制替换规则
2. WHEN 管理URL本地化 THEN 数据库 SHALL 支持source_pattern到target_pattern的映射规则
3. WHEN 处理正则表达式URL THEN 数据库 SHALL 通过is_regex字段区分普通字符串和正则表达式模式
4. WHEN 应用替换规则 THEN 数据库 SHALL 支持priority优先级控制和is_active启用状态管理
5. WHEN 维护规则库 THEN 数据库 SHALL 提供notes字段存储规则说明和维护信息

### 需求 6

**用户故事：** 作为一个系统运维人员，我希望数据库能够提供全面的业务层面的监控和审计功能，以便确保系统的稳定性和安全性。

#### 验收标准

1. WHEN 翻译任务执行异常 THEN translation_jobs表 SHALL 通过error_message和error_stage字段记录故障详情和失败阶段，支持精确故障定位
2. WHEN 监控翻译任务性能 THEN translation_jobs表 SHALL 通过processing_time_ms、submitted_by、submitted_at、completed_at字段提供完整的任务执行审计轨迹
3. WHEN 监控正则表达式规则质量 THEN regex_patterns表 SHALL 通过validation_status字段支持规则健康状态监控（pending积压告警、invalid质量告警、验证通过率统计）
4. WHEN 追踪AWS服务数据同步状态 THEN service_names表 SHALL 通过source、last_synced_at、is_active字段支持多数据源同步监控和数据新鲜度检查
5. WHEN 分析用户反馈趋势 THEN feedback_submissions表 SHALL 通过satisfaction、submitted_at、page_url字段支持用户满意度统计和问题页面识别
6. WHEN 监控规则库使用状态 THEN brand_term_mappings和url_mappings表 SHALL 通过is_active、priority、updated_at字段支持规则启用状态监控和变更审计
7. WHEN 进行系统性能分析 THEN 数据库 SHALL 通过GIN索引优化JSONB字段查询性能；并仅对 `service_names`、`brand_term_mappings`、`url_mappings`、`regex_patterns` 通过触发器维护 `updated_at`；`translation_jobs`、`feedback_submissions` 不维护 `updated_at`（其审计字段为 `submitted_at`/`completed_at` 等）

### 需求 7

**用户故事：** 作为一个开发人员，我希望数据库设计能够支持高并发访问和优化的索引策略，以便应对业务增长的需求。

#### 验收标准

1. WHEN 系统并发访问增加 THEN 数据库 SHALL 通过优化的索引结构保证查询性能（priority DESC, id ASC）
2. WHEN 查询JSONB字段 THEN 数据库 SHALL 使用GIN索引优化placeholder_map和service_mention_state查询
3. WHEN 需要复合查询 THEN 数据库 SHALL 提供pattern_type和is_active的复合索引
4. 可选项（非当前必需）：当数据量达到阈值时，数据库 SHOULD 支持按时间对 `translation_jobs` 表进行分区（按月）；该项作为未来扩展，不影响当前验收
5. WHEN 进行性能分析 THEN 数据库 SHALL 支持EXPLAIN ANALYZE查询执行计划分析

### 需求 8

**用户故事：** 作为一个数据分析师，我希望数据库能够支持复杂的分析查询和边界保护机制，以便提供准确的业务洞察。

#### 验收标准

1. WHEN 分析翻译任务执行效率 THEN translation_jobs表 SHALL 通过status、processing_time_ms、submitted_at、completed_at字段支持任务耗时分布分析、阶段性能瓶颈识别和处理速度趋势统计
2. WHEN 评估翻译质量和占位符使用 THEN translation_jobs表 SHALL 通过placeholder_map和service_mention_state的JSONB字段支持占位符类型统计、服务提及频率分析和翻译复杂度评估
3. WHEN 分析用户满意度和反馈模式 THEN feedback_submissions表 SHALL 通过satisfaction、page_url、submitted_at字段支持满意度时间序列分析、问题页面热力图生成和用户反馈趋势预测
4. WHEN 评估正则表达式规则效果 THEN regex_patterns表 SHALL 通过validation_status、priority、pattern_type、service_code字段支持规则验证成功率统计、优先级分布分析和模式类型效果评估
5. WHEN 分析AWS服务覆盖度和同步质量 THEN service_names表 SHALL 通过source、service_code、last_synced_at、is_active字段支持多数据源覆盖度分析、服务同步新鲜度统计和数据源质量对比
6. WHEN 评估本地化规则使用效果 THEN brand_term_mappings和url_mappings表 SHALL 通过is_active、priority、updated_at字段支持规则启用率统计、优先级冲突检测和规则变更频率分析
7. WHEN 进行跨表关联业务分析 THEN 数据库 SHALL 支持translation_jobs与feedback_submissions的翻译质量关联分析、regex_patterns与service_names的服务覆盖度分析、以及所有表的综合业务健康度评估

### 需求 9

**用户故事：** 作为一个数据库管理员，我希望数据库设计能够支持便捷的维护和管理操作，以便降低运维成本。

#### 验收标准

1. WHEN 需要数据清理 THEN 数据库 SHALL 提供按外键依赖顺序的DELETE FROM语句和ALTER SEQUENCE RESTART的可重复执行脚本，确保数据完全清理且序列重置
2. WHEN 进行性能调优 THEN 数据库 SHALL 提供EXPLAIN ANALYZE查询执行计划和性能分析工具
3. WHEN 管理数据库对象 THEN 数据库 SHALL 使用标准化的命名约定和COMMENT ON注释
4. WHEN 执行维护任务 THEN 数据库 SHALL 支持在线DDL操作和触发器管理
5. WHEN 监控数据库健康 THEN 数据库 SHALL 提供validation_status健康检查和预警机制

### 需求 10

**用户故事：** 作为一个业务用户，我希望数据库能够支持复合后缀模式的智能处理和实时数据处理，以便快速响应业务需求。

#### 验收标准

1. WHEN 系统识别复合服务名称（如"Amazon EC2 P3 instances"） THEN regex_patterns表 SHALL 通过metadata JSONB字段中isCompoundWithSuffix=true的模式匹配基础服务名称，并通过suffixGroup指定的捕获组保留后缀"P3 instances"，确保翻译后重组为"Amazon Elastic Compute Cloud (EC2) P3 instances"
2. WHEN 翻译任务状态从pending变更为processing_stage1/2/3 THEN translation_jobs表 SHALL 在50毫秒内更新status字段和completed_at时间戳，支持实时状态追踪和进度监控
3. WHEN AWS服务同步系统更新服务名称 THEN service_names表 SHALL 基于authoritative_full_name业务主键执行UPSERT操作，并在事务提交后立即更新last_synced_at时间戳，确保数据实时性
4. WHEN 系统需要实时查询正则表达式优先级分布 THEN regex_patterns表 SHALL 支持按priority字段（90-130范围）的实时GROUP BY聚合查询，响应时间不超过100毫秒


### 需求 11

**用户故事：** 作为一个性能工程师，我希望数据库能够有高性能策略和部分索引优化，实现查询性能提升。

#### 验收标准

1. WHEN PatternLoader组件加载正则模式 THEN 数据库 SHALL 通过部分索引idx_regex_patterns_active_valid（WHERE is_active = TRUE AND validation_status = 'valid'）实现60-80%的索引大小减少和10-50倍的查询速度提升
2. WHEN 系统查询活跃服务代码 THEN 数据库 SHALL 通过部分索引idx_service_names_active_code（WHERE is_active = TRUE）优化service_code字段查询性能
3. WHEN SuffixAssembler组件查询复合后缀模式 THEN 数据库 SHALL 通过GIN索引idx_regex_patterns_metadata支持metadata JSONB字段的高效查询（metadata->>'isCompoundWithSuffix' = 'true'）
4. WHEN MentionTracker组件查询服务提及状态 THEN 数据库 SHALL 通过GIN索引idx_translation_jobs_service_mention_state支持service_mention_state JSONB字段的高效查询
5. 可选项（非当前必需）：当历史任务量巨大时，可引入按时间分区的 `translation_jobs`（例如基于 `submitted_at` 按月分区）；该项作为未来扩展
6. WHEN 定义模式类型 THEN 数据库 SHALL 使用regex_pattern_type ENUM类型替代VARCHAR，提供类型安全和数据一致性
7. WHEN 约束检查 THEN 数据库 SHALL 添加regex_string长度约束（CHECK length(regex_string) < 10000），防止过长模式导致性能问题
8. WHEN 性能监控 THEN 数据库 SHALL 监控索引使用情况和查询性能，确保优化效果并支持动态调优

### 需求 12

**用户故事：** 作为一个系统集成工程师，我希望数据库能够支持组件化架构的各个核心组件，以便实现Aho-Corasick预扫描和分段正则匹配的高性能算法。

#### 验收标准

1. WHEN PatternLoader组件构建Aho-Corasick自动机 THEN 数据库 SHALL 提供按priority DESC, id ASC排序的正则模式查询，支持O(n)复杂度的候选词预扫描
2. WHEN ServiceMatcher组件执行分段正则匹配 THEN 数据库 SHALL 通过service_code字段支持按服务分组的模式查询，实现只对候选区域应用正则表达式的优化策略
3. WHEN BoundaryGuard组件执行边界保护 THEN 数据库 SHALL 支持pattern_type='CONTEXT_PROTECTED'的上下文保护模式查询，避免ARN、URL等上下文中的误匹配
4. WHEN SuffixAssembler组件处理复合后缀 THEN 数据库 SHALL 通过metadata JSONB字段支持suffixGroup、patternCategory等结构化元数据的存储和查询
5. WHEN MentionTracker组件追踪服务提及状态 THEN 数据库 SHALL 支持service_mention_state JSONB字段的原子性更新操作，记录首次/后续提及的完整状态信息

### 需求 13

**用户故事：** 作为一个边界保护专家，我希望数据库能够支持完整的边界保护机制，以便避免在AWS ARN、URL、代码标识符等上下文中的误匹配。

#### 验收标准

1. WHEN 系统处理ARN字符串 THEN 数据库 SHALL 通过 `pattern_type='CONTEXT_PROTECTED'` 的基线规则提供 ARN 保护（例如 `arn:(?:aws|aws-cn):\S+`），匹配后的剔除由应用层 BoundaryGuard 完成；业务模式本体不得使用可变宽度负向后行
2. WHEN 系统处理URL路径 THEN 数据库 SHALL 通过 `pattern_type='CONTEXT_PROTECTED'` 的基线规则提供 URL 保护（例如 `https?://\S+`），匹配后的剔除由应用层 BoundaryGuard 完成；业务模式本体不得使用可变宽度负向后行
3. WHEN 系统处理代码标识符 THEN 业务模式本体仅保留固定宽度的轻量边界保护（如 `(?<![:_-])` 与 `(?![:_-])`），不使用可变宽度负向后行
4. WHEN 系统处理特殊服务Aurora THEN regex_patterns表 SHALL 支持特殊变体模式（优先级125），区分通用Aurora和特定引擎变体（PostgreSQL/MySQL）
5. WHEN 系统处理Health服务族 THEN regex_patterns表 SHALL 区分Health Dashboard和通用Health服务，避免与Health Lake、Health Imaging等服务混淆

### 需求 14

**用户故事：** 作为一个数据库架构师，我希望数据库设计能够支持未来扩展和维护需求，以便系统能够持续演进和优化。

#### 验收标准

1. WHEN 系统需要添加新的模式类型 THEN regex_pattern_type枚举 SHALL 支持ALTER TYPE语句的在线扩展，无需停机维护
2. WHEN 系统需要扩展元数据结构 THEN metadata JSONB字段 SHALL 支持向后兼容的结构扩展，新增字段不影响现有功能
3. WHEN 系统需要处理更多AWS服务 THEN service_names表 SHALL 通过authoritative_full_name业务主键支持新服务的无冲突添加
4. WHEN 系统需要支持更多翻译阶段 THEN translation_jobs表 SHALL 通过translation_job_status枚举支持新阶段的添加（如processing_stage4）
5. WHEN 系统需要国际化支持 THEN 数据库设计 SHALL 预留多语言字段（如full_name_cn、short_name_cn），支持未来的多语言扩展需求