好的，收到。基于您提供的所有项目上下文文档和代码，我将为您设计一套全面、可维护且符合最佳实践的 PostgreSQL 数据库表结构。

这套设计旨在将 `chat-aitao_massemail_code.html` 中的硬编码逻辑（如服务名称匹配规则、品牌术语替换等）外部化到数据库中，从而极大地提升系统的可维护性和灵活性，使得未来更新规则和术语时，只需修改数据库记录，而无需重新部署代码。

---

### 数据库设计哲学

1.  **逻辑外化 (Externalization of Logic)**: 核心目标是将业务规则（服务名、品牌词、URL映射、正则表达式）从应用代码中分离出来，存入数据库。这使得非开发人员（如语言专家或项目经理）也可以通过管理界面（未来可开发）来更新翻译规则，降低了维护成本。
2.  **过程可追溯 (Traceability)**: `translation_jobs` 表作为核心，详细记录了翻译任务从原始输入到最终输出的每一步结果。这对于调试、质量审计和未来算法优化至关重要。
3.  **原子化与规范化 (Atomicity & Normalization)**: 将不同类型的规则拆分到独立的表中（如`brand_term_mappings`, `url_mappings`, `regex_patterns`），确保了数据的一致性和管理的便捷性。
4.  **拥抱JSONB (Embrace JSONB)**: 对于半结构化的数据，如占位符映射表和多变的服务提及状态，使用 PostgreSQL 强大的 `JSONB` 类型是最佳选择。它提供了灵活性，同时支持高性能的查询和索引。
5.  **面向未来 (Future-Proofing)**: 设计中包含了 `is_active` 字段、优先级字段和备注字段，允许平滑地启用/禁用规则、控制规则应用顺序，并为未来的维护者留下清晰的文档。

---

### 数据库DDL完整脚本

以下是完整的 PostgreSQL DDL 脚本，包含了所有表、索引、枚举类型、外键和触发器。

```sql
-- 开启 pgcrypto 扩展，用于生成 UUID
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ====================================================================
-- 1. 自定义枚举类型 (ENUM Types)
-- ====================================================================
-- 定义翻译任务的状态
CREATE TYPE translation_job_status AS ENUM (
    'pending',
    'processing_stage1', -- JS标准化
    'processing_stage2', -- LLM翻译
    'processing_stage3', -- 占位符恢复
    'completed',
    'failed'
);

-- 定义规则来源
CREATE TYPE rule_source AS ENUM (
    'web_scrape',
    'pdf_parse',
    'manual'
);

-- 定义用户反馈满意度
CREATE TYPE feedback_satisfaction AS ENUM (
    'satisfied',
    'unsatisfied',
    'neutral'
);

-- 定义正则表达式模式的类型
CREATE TYPE regex_pattern_type AS ENUM (
    'SERVICE_NAME',
    'TIMEZONE',
    'CLI_COMMAND',
    'URL',
    'GENERAL'
);

-- ====================================================================
-- 2. 自动更新 updated_at 时间戳的触发器函数
-- ====================================================================
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ====================================================================
-- 3. 核心业务逻辑表 (Core Business Logic Tables)
-- ====================================================================

-- ---------------------------------
-- 表: service_names (服务名称)
-- 描述: 存储所有AWS服务的标准中英文名称、缩写等信息。
-- ---------------------------------
CREATE TABLE service_names (
    id BIGSERIAL PRIMARY KEY,
    base_name VARCHAR(255) NOT NULL UNIQUE, -- 服务的唯一基础名称, 如 "Amazon Elastic Compute Cloud"
    full_name_en VARCHAR(255) NOT NULL,    -- 英文全称, 如 "Amazon Elastic Compute Cloud (EC2)"
    short_name_en VARCHAR(100) NOT NULL,   -- 英文缩写, 如 "Amazon EC2"
    full_name_cn VARCHAR(255),             -- 中文全称
    short_name_cn VARCHAR(100),            -- 中文缩写
    service_code VARCHAR(50),              -- AWS官方服务代码, 如 "ec2"
    source rule_source NOT NULL DEFAULT 'manual', -- 数据来源
    is_active BOOLEAN NOT NULL DEFAULT TRUE,       -- 是否启用
    notes TEXT,                                    -- 备注
    last_synced_at TIMESTAMPTZ,                     -- 上次同步时间
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_service_names_service_code ON service_names(service_code);
CREATE INDEX idx_service_names_is_active ON service_names(is_active);

-- 触发器
CREATE TRIGGER set_timestamp
BEFORE UPDATE ON service_names
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- ---------------------------------
-- 表: brand_term_mappings (品牌术语映射)
-- 描述: 存储需要强制替换的品牌术语和通用词汇。
-- ---------------------------------
CREATE TABLE brand_term_mappings (
    id BIGSERIAL PRIMARY KEY,
    term_en VARCHAR(255) NOT NULL UNIQUE, -- 英文术语, 如 "Amazon Web Services Support"
    term_cn VARCHAR(255) NOT NULL,        -- 中文替换, 如 "亚马逊云科技中国支持团队"
    is_active BOOLEAN NOT NULL DEFAULT TRUE, -- 是否启用
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 触发器
CREATE TRIGGER set_timestamp
BEFORE UPDATE ON brand_term_mappings
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- ---------------------------------
-- 表: url_mappings (URL 映射规则)
-- 描述: 存储URL本地化的替换规则。
-- ---------------------------------
CREATE TABLE url_mappings (
    id BIGSERIAL PRIMARY KEY,
    source_pattern TEXT NOT NULL,       -- 源URL匹配模式
    target_pattern TEXT NOT NULL,       -- 目标URL替换模式
    is_regex BOOLEAN NOT NULL DEFAULT FALSE, -- source_pattern是否为正则表达式
    priority INT NOT NULL DEFAULT 0,    -- 应用优先级，数字越大优先级越高
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_url_mappings_priority ON url_mappings(priority DESC);
CREATE INDEX idx_url_mappings_is_active ON url_mappings(is_active);

-- 触发器
CREATE TRIGGER set_timestamp
BEFORE UPDATE ON url_mappings
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();


-- ---------------------------------
-- 表: regex_patterns (正则表达式模式)
-- 描述: 存储用于识别服务名称、时间等实体的正则表达式。
-- ---------------------------------
CREATE TABLE regex_patterns (
    id BIGSERIAL PRIMARY KEY,
    pattern_name VARCHAR(255) NOT NULL UNIQUE, -- 模式的易读名称, e.g., "EC2_INSTANCE_WITH_SUFFIX"
    pattern_type regex_pattern_type NOT NULL,  -- 模式类型
    regex_string TEXT NOT NULL,                -- 正则表达式本身
    related_service_id BIGINT,                 -- 关联的服务ID (外键)
    priority INT NOT NULL DEFAULT 0,           -- 匹配优先级，数字越大优先级越高
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    notes TEXT, -- 可以描述捕获组的含义等
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_related_service
        FOREIGN KEY(related_service_id)
        REFERENCES service_names(id)
        ON DELETE SET NULL
);

-- 索引
CREATE INDEX idx_regex_patterns_type ON regex_patterns(pattern_type);
CREATE INDEX idx_regex_patterns_priority ON regex_patterns(priority DESC);
CREATE INDEX idx_regex_patterns_is_active ON regex_patterns(is_active);

-- 触发器
CREATE TRIGGER set_timestamp
BEFORE UPDATE ON regex_patterns
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- ====================================================================
-- 4. 核心工作流与数据表 (Core Workflow & Data Tables)
-- ====================================================================

-- ---------------------------------
-- 表: translation_jobs (翻译任务)
-- 描述: 记录每一次翻译请求的完整生命周期。
-- ---------------------------------
CREATE TABLE translation_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    status translation_job_status NOT NULL DEFAULT 'pending', -- 当前任务状态
    original_text TEXT NOT NULL,                           -- 用户提交的原始文本
    stage1_standardized_en TEXT,                           -- JS标准化后的英文文本
    stage2_llm_input TEXT,                                 -- 发送给LLM的带占位符的文本
    stage2_llm_output TEXT,                                -- LLM返回的带占位符的译文
    stage3_final_cn TEXT,                                  -- 最终恢复占位符后的中文文本
    placeholder_map JSONB,                                 -- 占位符与原文的映射, e.g., {"__SRVCNM_0__": "Amazon EC2"}
    service_mention_state JSONB,                           -- 服务首次/后续提及状态, e.g., {"Amazon EC2": {"mentioned": true, "usedFullName": true}}
    error_message TEXT,                                    -- 如果失败，记录错误信息
    error_stage VARCHAR(50),                               -- 失败发生的阶段
    processing_time_ms INT,                                -- 总处理耗时（毫秒）
    submitted_by VARCHAR(255),                             -- 提交者标识
    submitted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);

-- 索引
CREATE INDEX idx_translation_jobs_status ON translation_jobs(status);
CREATE INDEX idx_translation_jobs_submitted_by ON translation_jobs(submitted_by);
CREATE INDEX idx_translation_jobs_submitted_at ON translation_jobs(submitted_at);
-- GIN索引用于高效查询JSONB数据
CREATE INDEX idx_translation_jobs_placeholder_map ON translation_jobs USING GIN (placeholder_map);


-- ---------------------------------
-- 表: feedback_submissions (用户反馈)
-- 描述: 存储通过Tampermonkey脚本收集的用户反馈。
-- ---------------------------------
CREATE TABLE feedback_submissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    translation_job_id UUID,                                -- 关联的翻译任务ID (可能为空)
    page_url TEXT NOT NULL,                                  -- 反馈提交页面URL
    satisfaction feedback_satisfaction NOT NULL,             -- 满意度
    comments TEXT,                                           -- 用户评论
    user_agent TEXT,                                         -- 用户的User Agent
    s3_object_key VARCHAR(1024) NOT NULL UNIQUE,             -- 在S3中存储的原始JSON文件Key
    submitted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_translation_job
        FOREIGN KEY(translation_job_id)
        REFERENCES translation_jobs(id)
        ON DELETE SET NULL
);

-- 索引
CREATE INDEX idx_feedback_submissions_satisfaction ON feedback_submissions(satisfaction);

```

---

### 表结构设计思路详解

#### 1. `service_names` (服务名称表)
*   **设计目的**: 这是“逻辑外化”的核心。它将 `chat-aitao_massemail_code.html` 中巨大的 `servicePatterns` 数组所代表的知识，结构化地存入数据库。
*   **字段详解**:
    *   `id` (BIGSERIAL): 自增主键，简单高效。
    *   `base_name` (VARCHAR, UNIQUE): **关键字段**。这是服务的“根本”名称，如 "Amazon Elastic Compute Cloud"。它应该是唯一的，作为识别一个服务的核心标识符，所有别名和缩写都围绕它。
    *   `full_name_en`, `short_name_en`: 分别存储标准化的全称和简称，对应代码中的 `fullName` 和 `shortName`。应用在处理时，可以根据 `service_mention_state` 决定使用哪个。
    *   `full_name_cn`, `short_name_cn`: 预留字段，用于存储官方的中文名。未来可以直接用于翻译或校验，无需每次都通过LLM。
    *   `service_code`: AWS官方服务代码（如`s3`, `lambda`），便于与AWS其他API或文档系统集成。
    *   `source`: 记录数据来源（网页抓取、PDF解析、手动添加），对数据溯源和可信度判断非常重要。
    *   `is_active`, `notes`, `created_at`, `updated_at`: 标准的管理与审计字段。

#### 2. `brand_term_mappings` (品牌术语映射表)
*   **设计目的**: 将所有需要硬性替换的品牌词、技术术语（如 `Amazon Web Services Support` -> `亚马逊云科技中国支持团队`）从代码中解放出来。
*   **字段详解**:
    *   `term_en` (VARCHAR, UNIQUE): 需要被替换的英文术语，唯一约束确保不会有重复规则。
    *   `term_cn` (VARCHAR): 对应的中文。
    *   `is_active`: 可以方便地启用或禁用某条规则，而无需删除。

#### 3. `url_mappings` (URL映射规则表)
*   **设计目的**: 将URL本地化逻辑（如 `aws.amazon.com` -> `amazonaws.cn`）配置化。
*   **字段详解**:
    *   `source_pattern`, `target_pattern`: 源和目标的模式。
    *   `is_regex` (BOOLEAN): 明确指出源模式是普通字符串还是正则表达式，使应用层处理更清晰。
    *   `priority` (INT): **关键字段**。处理可能重叠的URL规则（例如，一个通用规则和一个更具体的规则），优先级高的先应用。

#### 4. `regex_patterns` (正则表达式模式表)
*   **设计目的**: 存储用于在文本中识别各类实体（特别是服务名）的正则表达式，完全替代 `servicePatterns` 数组。
*   **字段详解**:
    *   `pattern_name` (VARCHAR, UNIQUE): 一个人类可读的名称，便于管理和调试。
    *   `pattern_type` (ENUM): 对正则进行分类，应用可以按类型加载不同用途的正则。
    *   `regex_string` (TEXT): 正则表达式本身。
    *   `related_service_id` (FK): **关键外键**。将一个正则表达式与其匹配到的 `service_names` 记录关联起来。例如，匹配 "EC2" 的正则可以关联到 "Amazon Elastic Compute Cloud" 这条记录。这使得匹配和标准化流程高度结构化。
    *   `priority`: 用于控制当多个正则都能匹配同一段文本时的应用顺序（通常是长模式优先）。

#### 5. `translation_jobs` (翻译任务表)
*   **设计目的**: 系统的中枢。记录每一次翻译任务的完整上下文和产出，是整个工作流的快照。
*   **字段详解**:
    *   `id` (UUID): 使用UUID作为主键，因为它可以在应用层生成，便于构建解耦的分布式系统，并且在日志和外部系统中引用时不会冲突。
    *   `status` (ENUM): 清晰地追踪任务当前所处阶段。
    *   `original_text`, `stage1_...`, `stage2_...`, `stage3_final_cn` (TEXT): 保存了处理流水线中每个关键阶段的输入和输出，提供了无与伦比的可追溯性。
    *   `placeholder_map` (JSONB): **核心字段**。以JSON格式存储占位符和其原始值的映射。例如：`{"__SRVCNM_0__": "Amazon EC2", "__URL_1__": "https://..."}`。使用 `JSONB` 性能高，且可以被索引。
    *   `service_mention_state` (JSONB): 同样使用 `JSONB` 存储服务提及状态，完美适应其动态变化的结构。
    *   `error_message`, `error_stage`: 精确定位任务失败的原因和位置。
    *   `submitted_by`, `submitted_at`, `completed_at`: 完整的审计和性能追踪信息。

#### 6. `feedback_submissions` (用户反馈表)
*   **设计目的**: 严格按照 `feedback-system-guide.md` 的规范来存储用户反馈数据。
*   **字段详解**:
    *   `translation_job_id` (FK): 将用户反馈与具体的翻译任务关联起来，形成闭环。允许为空，以支持用户提交与特定任务无关的通用反馈。
    *   `satisfaction` (ENUM): 使用枚举类型比字符串更高效，数据更规整。
    *   `s3_object_key`: 记录了S3中原始文件的位置，确保了原始数据的完整性和可追溯性。

这套数据库设计将为您的CN Mass Email Translator项目提供一个坚实、灵活且易于维护的数据基础。