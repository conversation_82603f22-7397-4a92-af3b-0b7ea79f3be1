# `service_names` 表 v2 设计深度解析

`service-name-sync-module-update-summary.md` 中对 `service_names` 表的优化设计，其核心思想是**“职责分离” (Separation of Concerns)**。它将一个字段原本模糊的、承担多种角色的职责，清晰地拆分到多个字段中，使得每个字段都有一个单一、明确的用途。

这种设计之所以完美贴合“服务名称全称/简称替换”的业务逻辑，原因在于它精准地映射了该逻辑的每一个环节。

让我们先回顾一下这个核心业务逻辑，然后再分析新设计是如何完美支持它的。

### 核心业务逻辑：首次与后续提及 (First vs. Subsequent Mention)

这个业务逻辑的本质是一个小型的**状态机**：

1.  **识别 (Identification)**：在一段文本中，系统需要识别出一个AWS服务，无论它是以全称、简称还是缩写形式出现。例如，`Amazon Elastic Compute Cloud (EC2)` 和 `Amazon EC2` 都必须被识别为同一个服务。
2.  **状态检查 (State Check)**：系统需要记录这个被识别出的服务在本轮翻译中**是否已经被提及过**。
3.  **替换 (Replacement)**：
    *   如果**从未被提及**（首次），则将其替换为**官方全称**，如 `Amazon Elastic Compute Cloud (EC2)`。
    *   如果**已经被提及过**（后续），则将其替换为**官方简称**，如 `Amazon EC2`。

### `v2` 表结构如何完美支撑此逻辑

新的 `service_names` 表结构设计了四个关键字段来分别应对上述逻辑的每一个环节：

1.  `authoritative_full_name`
2.  `base_name`
3.  `full_name_en`
4.  `short_name_en`

下面我们通过一个完整的处理流程来分析它们各自扮演的角色：

**场景**: 假设系统需要翻译这段文本：`"You can launch an Amazon Elastic Compute Cloud (EC2) instance. After that, you can connect to the Amazon EC2 instance."`

---

#### 第1步：识别 (Identification) -> 依赖 `regex_patterns` 和外键关联

*   系统首先匹配到文本中的 `"Amazon Elastic Compute Cloud (EC2)"`。
*   这个匹配是通过 `regex_patterns` 表中的一个高优先级正则表达式完成的。
*   该正则表达式记录通过 `related_service_id` 外键，精确地指向 `service_names` 表中的**唯一一条记录**。

---

#### 第2步：获取服务“身份卡” -> 一次性加载所有信息

系统根据 `related_service_id` 从 `service_names` 表中获取了该服务的所有信息，我们称之为“身份卡”：

```json
{
  "id": 101,
  "authoritative_full_name": "Amazon Elastic Compute Cloud (EC2)", 
  "base_name": "Amazon Elastic Compute Cloud",                  
  "full_name_en": "Amazon Elastic Compute Cloud (EC2)",             
  "short_name_en": "Amazon EC2",                                  
  "internal_name": "Amazon Elastic Compute Cloud"
}
```
*   `authoritative_full_name`：用于数据同步的键
*   `base_name`：逻辑核心，状态跟踪的键
*   `full_name_en`：首次替换用的值
*   `short_name_en`：后续替换用的值

---

#### 第3步：状态检查 (State Check) -> `base_name` 的关键作用

这是整个设计的精髓所在。系统需要一个**稳定、一致的键**来查询内部的状态记录（即 `translation_jobs.service_mention_state` JSON对象）。

*   **它使用哪个字段作为这个键？** -> **`base_name`** (`"Amazon Elastic Compute Cloud"`)。

为什么 `base_name` 是最完美的键？

*   **一致性**: 无论系统最开始匹配到的是 `"Amazon EC2"` 还是 `"Amazon Elastic Compute Cloud (EC2)"`，它们通过正则表达式都将关联到**同一条** `service_names` 记录，因此都会得到**同一个 `base_name`**。这确保了它们被视为同一个服务。
*   **规范性**: `base_name` 被定义为“不含括号的规范化名称”。这使得它非常适合作为程序逻辑中的 key——简洁、可预测且稳定。

在处理第一个句子时，系统用 `"Amazon Elastic Compute Cloud"` 作为 key 查询状态记录，发现它不存在。

---

#### 第4步：执行替换 (Replacement) -> `full_name_en` 与 `short_name_en` 各司其职

1.  **处理首次提及**:
    *   因为状态检查结果为“首次提及”，系统从“身份卡”中取出 `full_name_en` 的值 (`"Amazon Elastic Compute Cloud (EC2)"`) 进行替换。
    *   **然后，更新状态**: 系统将 `{"Amazon Elastic Compute Cloud": {"mentioned": true}}` 记录到内部状态中。

2.  **处理后续提及**:
    *   系统继续处理文本，匹配到了 `"Amazon EC2"`。
    *   它重复第1、2步，再次获取到了**完全相同**的服务“身份卡”。
    *   它重复第3步，再次使用 `base_name` (`"Amazon Elastic Compute Cloud"`) 去查询状态。
    *   **这一次，状态检查结果为“已被提及”**。
    *   系统从“身份卡”中取出 `short_name_en` 的值 (`"Amazon EC2"`) 进行替换。

### 总结：为什么这个设计是优越的？

1.  **逻辑与数据解耦**: `base_name` 完美地充当了**业务逻辑的“锚点”**。它将“如何识别服务”（交给`regex_patterns`）、“如何唯一标识一条数据库记录”（交给`authoritative_full_name`）和“如何处理首次/后续提及”（交给`full_name_en`/`short_name_en`）这几个问题完全解耦。
2.  **单一职责原则**:
    *   `authoritative_full_name`: **只负责数据同步**。它不参与翻译时的替换逻辑。
    *   `base_name`: **只负责状态跟踪**。它是翻译引擎内部用来识别“是不是同一个东西”的逻辑键。
    *   `full_name_en`: **只负责提供首次替换的值**。
    *   `short_name_en`: **只负责提供后续替换的值**。
3.  **极高的可维护性**: 如果未来出现了一个新的服务别名，比如 "Amazon's EC2 Cloud"，开发者需要做的仅仅是在 `regex_patterns` 表里增加一个新规则，并将其 `related_service_id` 指向现有的 `EC2` 记录。**核心的 `service_names` 表和替换逻辑代码完全不需要改动**，因为新别名最终还是会通过 `base_name` 解析为同一个服务身份。

因此，这个经过优化的表结构，通过清晰的职责划分，为服务名称全称和简称的替换逻辑提供了**一个极其健壮、清晰且可扩展的数据模型**。 