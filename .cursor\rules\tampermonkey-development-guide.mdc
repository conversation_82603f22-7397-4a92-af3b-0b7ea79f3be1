---
inclusion: fileMatch  
fileMatchPattern: '*tampermonkey*|*userscript*|*油猴*'
---

# 油猴脚本开发指南

## 核心设计原则

### 页面内容检测优先
- **内容驱动激活**: 所有脚本必须首先检测页面是否包含目标内容，再决定是否激活功能
- **精确定位**: 使用多种检测策略确保准确识别相关页面
- **资源效率**: 避免在不相关页面上浪费资源或干扰用户体验
- **动态适应**: 支持处理动态加载的内容和页面变化

### 模块化设计
- **关注点分离**: 将功能划分为独立模块，每个模块专注于单一职责
- **可组合性**: 支持模块的灵活组合和重用
- **依赖管理**: 明确模块间的依赖关系，避免循环依赖
- **接口一致**: 保持模块接口的一致性和稳定性

### 性能优先
- **轻量级实现**: 使用高效的原生API，避免不必要的依赖
- **资源管理**: 及时清理不再需要的资源，避免内存泄漏
- **延迟加载**: 按需加载功能模块，避免一次性加载所有代码
- **批量处理**: 合并多次DOM操作，减少重排和重绘

### 错误处理与恢复
- **优雅降级**: 在出现错误时保持基本功能可用
- **自动恢复**: 实现错误自动恢复机制，减少用户干扰
- **详细日志**: 记录详细的错误信息和上下文，便于调试
- **用户反馈**: 提供清晰的错误提示和解决建议

## 通用框架使用指南

基于 `.kiro/specs/tampermonkey-framework/` 的设计，我们提供了一个统一的油猴脚本开发框架，支持智能页面内容检测、模块化架构和性能优化。

### 框架架构概览

```mermaid
graph TB
    A[页面加载] --> B[框架初始化]
    B --> C[配置管理器]
    B --> D[内容检测引擎]
    
    C --> D
    D --> E{检测结果}
    
    E -->|成功| F[脚本激活器]
    E -->|失败| G[资源清理]
    
    F --> H[功能模块1]
    F --> I[功能模块2]
    F --> J[功能模块N]
    
    H --> K[事件总线]
    I --> K
    J --> K
    
    K --> L[性能监控器]
    K --> M[错误处理器]
```

### 初始化与配置

```javascript
// 初始化框架
const tampermonkey = new TampermonkeyFramework({
    // 内容检测配置
    contentDetection: {
        // 检测目标 - 可以是字符串或正则表达式
        targets: ['CN Mass Email'],
        
        // 检测策略 - 可选 'textContent', 'innerText', 'visibleText', 'selector', 'regex'
        strategies: ['textContent', 'innerText', 'visibleText'],
        
        // 检测参数
        maxAttempts: 10,           // 最大检测次数
        checkInterval: 1000,       // 检测间隔（毫秒）
        initialDelay: 2000,        // 初始延迟（毫秒）
        timeout: 30000,            // 检测超时（毫秒）
        
        // 动态检测配置
        dynamicDetection: true,    // 是否启用动态内容检测
        observerConfig: {          // MutationObserver配置
            childList: true,
            subtree: true,
            characterData: true,
            attributes: false
        }
    },
    
    // 性能配置
    performance: {
        debounceDelay: 500,        // 防抖延迟（毫秒）
        throttleDelay: 300,        // 节流延迟（毫秒）
        maxObserverNodes: 1000,    // 最大观察节点数
        useThrottling: true        // 是否使用节流
    },
    
    // 调试配置
    debug: {
        enabled: false,            // 是否启用调试模式
        logLevel: 'info',          // 日志级别: 'debug', 'info', 'warn', 'error'
        consoleOutput: true,       // 是否输出到控制台
        performanceMetrics: false  // 是否收集性能指标
    },
    
    // 多脚本协调配置
    coordination: {
        namespace: 'awsMassEmailScript',  // 命名空间
        shareDetectionResults: true,      // 是否共享检测结果
        isolationLevel: 'medium'          // 隔离级别
    }
});
```

### 模块注册

```javascript
// 注册功能模块
tampermonkey.registerModule('feedback', {
    // 模块激活时调用
    activate: function(detectionResult) {
        console.log('Feedback module activated');
        console.log('Detection result:', detectionResult);
        
        // 实现模块功能
        this.createFeedbackButton();
        
        // 返回Promise表示激活完成
        return Promise.resolve();
    },
    
    // 模块方法
    createFeedbackButton: function() {
        // 创建反馈按钮
        const button = document.createElement('button');
        button.textContent = 'Feedback';
        // 设置样式和事件...
        document.body.appendChild(button);
    },
    
    // 模块清理方法（可选）
    cleanup: function() {
        // 清理资源
        const button = document.getElementById('feedback-button');
        if (button) button.remove();
        
        return Promise.resolve();
    }
});
```

### 启动框架

```javascript
// 启动框架
tampermonkey.start().then(result => {
    if (result) {
        console.log('Framework activated successfully');
    } else {
        console.log('Framework not activated (not on a relevant page)');
    }
}).catch(error => {
    console.error('Framework activation error:', error);
});
```

## 页面内容检测最佳实践

### 检测策略选择

1. **基本文本检测**
   ```javascript
   // 使用 textContent 检测 (最快但不考虑可见性)
   function checkWithTextContent() {
       return document.body.textContent.includes('CN Mass Email');
   }
   
   // 使用 innerText 检测 (仅检测可见文本)
   function checkWithInnerText() {
       return document.body.innerText.includes('CN Mass Email');
   }
   ```

2. **可见元素检测**
   ```javascript
   // 检测可见元素中的文本
   function checkVisibleElements() {
       const elements = document.querySelectorAll('*');
       return Array.from(elements).some(el => {
           const style = window.getComputedStyle(el);
           return style.display !== 'none' && 
                  style.visibility !== 'hidden' &&
                  el.textContent.includes('CN Mass Email');
       });
   }
   ```

3. **选择器检测**
   ```javascript
   // 使用 CSS 选择器检测
   function checkWithSelector() {
       // 查找包含特定文本的元素
       const elements = document.querySelectorAll('h1, h2, h3, p, div');
       return Array.from(elements).some(el => 
           el.textContent.includes('CN Mass Email')
       );
   }
   ```

4. **正则表达式检测**
   ```javascript
   // 使用正则表达式检测
   function checkWithRegex() {
       const pageText = document.body.textContent;
       return /CN\s+Mass\s+Email/i.test(pageText);
   }
   ```

### 动态内容处理

1. **MutationObserver**
   ```javascript
   // 使用 MutationObserver 监听动态内容
   function watchForDynamicContent(callback) {
       const observer = new MutationObserver((mutations) => {
           for (const mutation of mutations) {
               if (mutation.type === 'childList' || mutation.type === 'characterData') {
                   if (checkPageContent()) {
                       observer.disconnect();
                       callback(true);
                       return;
                   }
               }
           }
       });
       
       observer.observe(document.body, {
           childList: true,
           subtree: true,
           characterData: true
       });
       
       // 返回清理函数
       return () => observer.disconnect();
   }
   ```

2. **定时检测**
   ```javascript
   // 使用定时器定期检测
   function pollForContent(callback, maxAttempts = 10, interval = 1000) {
       let attempts = 0;
       
       const checkContent = () => {
           attempts++;
           
           if (checkPageContent()) {
               callback(true);
               return true;
           }
           
           if (attempts >= maxAttempts) {
               callback(false);
               return false;
           }
           
           setTimeout(checkContent, interval);
           return null;
       };
       
       return checkContent();
   }
   ```

3. **事件监听**
   ```javascript
   // 监听可能触发内容变化的事件
   function listenForContentChanges(callback) {
       const events = ['DOMContentLoaded', 'load', 'ajaxComplete', 'fetch'];
       const handlers = [];
       
       const checkAndCallback = () => {
           if (checkPageContent()) {
               cleanup();
               callback(true);
           }
       };
       
       const cleanup = () => {
           handlers.forEach(([event, handler]) => {
               window.removeEventListener(event, handler);
           });
       };
       
       events.forEach(event => {
           const handler = () => setTimeout(checkAndCallback, 100);
           window.addEventListener(event, handler);
           handlers.push([event, handler]);
       });
       
       return cleanup;
   }
   ```

### 性能优化技巧

1. **防抖处理**
   ```javascript
   // 防抖函数
   function debounce(func, wait) {
       let timeout;
       return function(...args) {
           clearTimeout(timeout);
           timeout = setTimeout(() => func.apply(this, args), wait);
       };
   }
   
   // 使用防抖处理检测
   const debouncedCheck = debounce(() => {
       if (checkPageContent()) {
           activateFeatures();
       }
   }, 300);
   ```

2. **节流处理**
   ```javascript
   // 节流函数
   function throttle(func, limit) {
       let inThrottle;
       return function(...args) {
           if (!inThrottle) {
               func.apply(this, args);
               inThrottle = true;
               setTimeout(() => inThrottle = false, limit);
           }
       };
   }
   
   // 使用节流处理检测
   const throttledCheck = throttle(() => {
       if (checkPageContent()) {
           activateFeatures();
       }
   }, 500);
   ```

3. **资源限制**
   ```javascript
   // 限制检测时间
   function timeboxedCheck(maxTime = 5000) {
       const startTime = performance.now();
       
       return function() {
           if (performance.now() - startTime > maxTime) {
               return false; // 超时停止检测
           }
           return checkPageContent();
       };
   }
   ```

## 常见应用场景

### 反馈收集脚本

```javascript
tampermonkey.registerModule('feedback', {
    activate: function() {
        // 创建反馈按钮
        const button = document.createElement('button');
        button.id = 'feedback-button';
        button.textContent = 'Feedback';
        button.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 9999;
            background-color: #ff9900;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            cursor: pointer;
        `;
        
        button.addEventListener('click', this.showFeedbackForm.bind(this));
        document.body.appendChild(button);
        
        return Promise.resolve();
    },
    
    showFeedbackForm: function() {
        // 实现反馈表单
        // ...
    },
    
    submitFeedback: function(data) {
        // 提交反馈数据
        // ...
    }
});
```

### 翻译辅助脚本

```javascript
tampermonkey.registerModule('translation', {
    activate: function() {
        // 查找需要翻译的元素
        const elements = document.querySelectorAll('.translatable');
        
        // 为每个元素添加翻译辅助功能
        elements.forEach(element => {
            this.enhanceElement(element);
        });
        
        // 添加翻译工具栏
        this.addTranslationToolbar();
        
        return Promise.resolve();
    },
    
    enhanceElement: function(element) {
        // 为元素添加翻译辅助功能
        // ...
    },
    
    addTranslationToolbar: function() {
        // 添加翻译工具栏
        // ...
    }
});
```

### UI增强脚本

```javascript
tampermonkey.registerModule('uiEnhancement', {
    activate: function() {
        // 添加快捷操作按钮
        this.addQuickActionButtons();
        
        // 增强表单功能
        this.enhanceForms();
        
        // 添加键盘快捷键
        this.setupKeyboardShortcuts();
        
        return Promise.resolve();
    },
    
    addQuickActionButtons: function() {
        // 添加快捷操作按钮
        // ...
    },
    
    enhanceForms: function() {
        // 增强表单功能
        // ...
    },
    
    setupKeyboardShortcuts: function() {
        // 设置键盘快捷键
        // ...
    }
});
```

### 数据同步脚本

```javascript
tampermonkey.registerModule('dataSync', {
    activate: function() {
        // 初始化数据存储
        this.initializeStorage();
        
        // 设置数据收集
        this.setupDataCollection();
        
        // 设置同步定时器
        this.setupSyncTimer();
        
        return Promise.resolve();
    },
    
    initializeStorage: function() {
        // 初始化数据存储
        // ...
    },
    
    setupDataCollection: function() {
        // 设置数据收集
        // ...
    },
    
    setupSyncTimer: function() {
        // 设置同步定时器
        // ...
    },
    
    syncData: function() {
        // 同步数据
        // ...
    }
});
```

## 调试与故障排除

### 调试工具

1. **启用调试模式**
   ```javascript
   const tampermonkey = new TampermonkeyFramework({
       debug: {
           enabled: true,
           logLevel: 'debug',
           consoleOutput: true
       }
   });
   ```

2. **检查检测状态**
   ```javascript
   tampermonkey.eventBus.subscribe('detection:started', () => {
       console.log('Content detection started');
   });
   
   tampermonkey.eventBus.subscribe('detection:success', (result) => {
       console.log('Content detected:', result);
   });
   
   tampermonkey.eventBus.subscribe('detection:failed', (result) => {
       console.log('Content detection failed after', result.attempts, 'attempts');
   });
   ```

3. **性能监控**
   ```javascript
   tampermonkey.eventBus.subscribe('performance:metrics', (metrics) => {
       console.log('Performance metrics:', metrics);
   });
   ```

### 常见问题与解决方案

1. **检测不到目标内容**
   - 检查目标文本是否正确
   - 尝试不同的检测策略
   - 增加检测次数和超时时间
   - 检查页面是否使用了动态加载

2. **脚本影响页面性能**
   - 减少检测频率
   - 优化DOM操作
   - 使用防抖和节流
   - 限制观察节点数量

3. **多脚本冲突**
   - 使用命名空间隔离
   - 协调资源使用
   - 共享检测结果
   - 实现消息传递

4. **浏览器兼容性问题**
   - 使用特性检测
   - 提供降级方案
   - 测试不同浏览器
   - 添加polyfill支持

## 最佳实践总结

1. **始终检测页面内容**
   - 所有脚本必须首先检测页面是否包含目标内容
   - 使用多种检测策略确保准确性
   - 处理动态加载的内容
   - 在不相关页面上不激活功能

2. **优化性能和资源使用**
   - 使用高效的检测算法
   - 实施防抖和节流机制
   - 及时清理不再需要的资源
   - 避免不必要的DOM操作

3. **实现健壮的错误处理**
   - 捕获和记录所有错误
   - 提供优雅降级策略
   - 实现自动恢复机制
   - 提供详细的调试信息

4. **保持模块化和可维护性**
   - 将功能划分为独立模块
   - 使用清晰的接口和文档
   - 遵循一致的编码风格
   - 编写全面的测试