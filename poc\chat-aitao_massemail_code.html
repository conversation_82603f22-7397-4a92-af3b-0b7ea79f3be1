<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Mass_Email_AI_Assistant (v11.21.5 - HTML Escape Display)</title>
<style>
/* CSS Styles remain the same */
body {
font-family: Arial, sans-serif;
max-width: 800px;
margin: 0 auto;
padding: 20px;
background-color: #f5f5f5;
}
.chat-container {
background-color: white;
border-radius: 10px;
padding: 20px;
box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
.chat-box {
height: 400px;
overflow-y: auto;
border: 1px solid #ddd;
padding: 10px;
margin-bottom: 20px;
border-radius: 5px;
}
.message {
margin: 10px 0;
padding: 10px;
border-radius: 5px;
white-space: pre-wrap; /* 保持 pre-wrap 以处理空格和换行 */
}
.user-message {
background-color: #e3f2fd;
margin-left: 20%;
}
.ai-message {
background-color: #f5f5f5;
margin-right: 20%;
}
.error-message {
background-color: #ffebee;
color: #c62828;
}
.debug-info {
background-color: #fff3e0;
padding: 10px;
margin-top: 10px;
border-radius: 5px;
font-family: monospace;
font-size: 12px;
max-height: 500px;
overflow-y: auto;
}
.debug-entry {
margin-bottom: 10px;
padding-bottom: 10px;
border-bottom: 1px solid #e0e0e0;
}
.debug-timestamp {
color: #757575;
font-size: 11px;
}
.debug-message {
font-weight: bold;
color: #333;
}
.debug-data {
margin-top: 5px;
padding-left: 10px;
border-left: 3px solid #ffa726;
}
.input-container {
display: flex;
gap: 10px;
}
#user-input {
flex: 1;
padding: 10px;
border: 1px solid #ddd;
border-radius: 5px;
resize: none;
overflow-y: hidden;
min-height: 40px;
line-height: 1.5;
box-sizing: border-box;
}
button {
padding: 10px 20px;
background-color: #2196f3;
color: white;
border: none;
border-radius: 5px;
cursor: pointer;
}
button:hover {
background-color: #1976d2;
}
.loading {
color: #666;
font-style: italic;
}
#debug-toggle {
margin-top: 10px;
background-color: #757575;
}
.debug-controls {
display: flex;
gap: 10px;
margin-top: 10px;
}
.debug-controls button {
flex: 1;
font-size: 12px;
padding: 8px;
}
#debug-clear {
background-color: #f44336;
}
#debug-clear:hover {
background-color: #d32f2f;
}
#debug-export {
background-color: #4caf50;
}
#debug-export:hover {
background-color: #388e3c;
}
.debug-filter {
margin-top: 10px;
padding: 8px;
border: 1px solid #ddd;
border-radius: 5px;
width: 100%;
}
.debug-stats {
background-color: #e8f5e9;
padding: 10px;
margin-top: 10px;
border-radius: 5px;
font-size: 12px;
}
</style>
</head>
<body>
    <div class="chat-container">
        <h2>Mass_Email_AI_Assistant (v11.21.5 - HTML Escape Display)</h2>
        <div class="chat-box" id="chat-box"></div>
        <div class="input-container">
            <textarea id="user-input" placeholder="请输入您的待翻译语段... (Enter 换行, Shift+Enter 发送)" rows="1" oninput="adjustTextareaHeight(this)" onkeydown="handleKeyDown(event)"></textarea>
            <button id="send-button" onclick="sendMessage()">发送</button>
        </div>
        <button id="debug-toggle" onclick="toggleDebug()">显示/隐藏调试信息</button>
        <div id="debug-container" style="display: none;">
            <div class="debug-controls">
                <button id="debug-mode-toggle" onclick="toggleDebugMode()">开启调试模式</button>
                <button id="debug-clear" onclick="clearDebug()">清除调试信息</button>
                <button id="debug-export" onclick="exportDebug()">导出调试日志</button>
            </div>
            <input type="text" class="debug-filter" id="debug-filter" placeholder="输入关键词过滤调试信息..." onkeyup="filterDebugInfo()">
            <div id="debug-panel" class="debug-info"></div>
            <div class="debug-stats" id="debug-stats">调试统计: 0条日志</div>
        </div>
    </div>

    <script>
        const chatBox = document.getElementById('chat-box');
        const userInput = document.getElementById('user-input');
        const sendButton = document.getElementById('send-button');
        const debugPanel = document.getElementById('debug-panel');
        const debugStats = document.getElementById('debug-stats');
        const debugContainer = document.getElementById('debug-container');
        const debugFilter = document.getElementById('debug-filter');

        let debugLogCount = 0;
        let debugStartTime = new Date();
        let debugMode = false;

        // ARN Regex v7: Structure-focused, simplified resource part, stronger lookahead.
        const robustArnRegexGlobal = /arn:([^:]*):([^:]*):([^:]*):([^:]*):(.+?)(?=["',\]})]|\\s|$)/g;
        const robustArnRegexSourceForBoundary = "arn:([^:]*):([^:]*):([^:]*):([^:]*):(.+?)(?=[\\\"',\\\]\\}\\)]|\\\\s|$)";


        function updateDebugStats() {
            const duration = Math.round((new Date() - debugStartTime) / 1000);
            debugStats.textContent = `调试统计: ${debugLogCount}条日志 | 运行时间: ${duration}秒 | 平均: ${debugLogCount > 0 ? (duration / debugLogCount).toFixed(3) : 0}秒/条`;
        }

        function toggleDebug() {
            debugContainer.style.display = debugContainer.style.display === 'none' ? 'block' : 'none';
            if (debugContainer.style.display === 'block') updateDebugStats();
        }

        function clearDebug() {
            if (confirm('确定要清除所有调试信息吗？')) {
                debugPanel.innerHTML = '';
                debugLogCount = 0;
                debugStartTime = new Date();
                updateDebugStats();
                console.clear();
            }
        }

        function exportDebug() {
            const debugText = Array.from(debugPanel.children).map(entry => {
                return Array.from(entry.childNodes).map(node => node.textContent).join('\n');
            }).join('\n\n---\n\n');
            const blob = new Blob([debugText], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `debug-log-${new Date().toISOString().replace(/[:.]/g, '-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function filterDebugInfo() {
            const filterText = debugFilter.value.toLowerCase();
            const entries = debugPanel.getElementsByClassName('debug-entry');
            for (let i = 0; i < entries.length; i++) {
                entries[i].style.display = entries[i].textContent.toLowerCase().includes(filterText) ? 'block' : 'none';
            }
        }

        function logDebug(message, data) {
            debugLogCount++;
            if (debugContainer.style.display === 'block') updateDebugStats();

            const timestamp = new Date().toISOString();
            const entry = document.createElement('div');
            entry.className = 'debug-entry';

            const timestampEl = document.createElement('div');
            timestampEl.className = 'debug-timestamp';
            timestampEl.textContent = timestamp;

            const messageEl = document.createElement('div');
            messageEl.className = 'debug-message';
            messageEl.textContent = message;

            const dataEl = document.createElement('pre');
            dataEl.className = 'debug-data';
            let seen = new Set();
            try {
                dataEl.textContent = JSON.stringify(data, (key, value) => {
                    if (typeof value === 'object' && value !== null) {
                        if (seen.has(value)) {
                            return '[Circular]';
                        }
                        seen.add(value);
                    }
                    return value;
                }, 2);
            } catch (e) {
                try {
                    dataEl.textContent = JSON.stringify(String(data), null, 2);
                } catch (e2){
                    dataEl.textContent = "[Circular Structure or other error in stringifying data]";
                }
            }
            seen = null;

            entry.appendChild(timestampEl);
            entry.appendChild(messageEl);
            entry.appendChild(dataEl);

            debugPanel.appendChild(entry);
            if(debugPanel.children.length > 500) { // Limit debug panel entries
                debugPanel.removeChild(debugPanel.firstChild);
            }
            debugPanel.scrollTop = debugPanel.scrollHeight;

            // Also log to browser console for direct comparison
            console.log(`[DEBUG PANEL LOG] ${timestamp} ${message}`, data);
            if (debugFilter.value) filterDebugInfo();
        }


        function handleKeyDown(event) {
            if (event.key === 'Enter' && event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
             setTimeout(() => adjustTextareaHeight(event.target), 0);
        }

        function adjustTextareaHeight(textarea) {
            if (textarea) {
                textarea.style.height = 'auto';
                textarea.style.height = textarea.scrollHeight + 'px';
            }
        }

        function escapeHTML(str) {
            if (typeof str !== 'string') return str;
            return str.replace(/&/g, '&amp;')
                      .replace(/</g, '&lt;')
                      .replace(/>/g, '&gt;')
                      .replace(/"/g, '&quot;')
                      .replace(/'/g, '&#039;');
        }

        function appendMessage(content, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            let rawStringContent = String(content);

            if (typeof content === 'object') {
                 try {
                    rawStringContent = JSON.stringify(content, null, 2);
                } catch (e) {
                    rawStringContent = "[Could not stringify object]";
                }
            }

            let escapedContent = escapeHTML(rawStringContent);

            if (type === 'ai-message') {
                messageDiv.innerHTML = escapedContent
                                           .replace(/\n/g, '<br>')
                                           .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                                        // Single asterisk for italics is intentionally removed to prevent conflict with ARNs containing '*'
            } else {
                 // For user messages, also escape and then handle newlines if they are to be displayed as <br>
                messageDiv.innerHTML = escapedContent.replace(/\n/g, '<br>');
                // Alternatively, if user messages should be pure text without any HTML interpretation:
                // messageDiv.textContent = rawStringContent; // This would not convert \n to <br>
            }
            chatBox.appendChild(messageDiv);
            chatBox.scrollTop = chatBox.scrollHeight;
        }

        function combinePromptAndContent(systemPrompt, userContent) {
            return `${systemPrompt}\n\n==== 用户输入 ====\n${userContent}`;
        }

        function createLoadingMessage(text) {
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'message loading';
            loadingDiv.textContent = text;
            chatBox.appendChild(loadingDiv);
            chatBox.scrollTop = chatBox.scrollHeight;
            return loadingDiv;
        }

        function updateLoadingMessage(loadingDiv, text) {
            if (loadingDiv && chatBox.contains(loadingDiv)) {
                loadingDiv.textContent = text;
            }
        }

        function removeLoadingMessage(loadingDiv) {
            if (loadingDiv && chatBox.contains(loadingDiv)) {
                chatBox.removeChild(loadingDiv);
            }
        }

        function handleError(error, stage = "General") {
            logDebug(`处理流程出错 (${stage})`, {
                message: error.message,
                stack: error.stack,
                name: error.name,
                cause: error.cause,
                error_object: error
            });
            appendMessage(`处理出错 (${stage}): ${error.message}${error.cause ? ` (Cause: ${error.cause})` : ''}`, 'error');
        }

        function toggleDebugMode() {
            debugMode = !debugMode;
            const toggleButton = document.getElementById('debug-mode-toggle');
            if (toggleButton) {
                toggleButton.textContent = debugMode ? '关闭调试模式 (显示步骤)' : '开启调试模式 (显示步骤)';
                toggleButton.style.backgroundColor = debugMode ? '#e74c3c' : '#2ecc71';
            }
            logDebug("调试模式切换", { debugMode });
        }

        let placeholderMap = {};
        let placeholderCounter = 0;

        const servicePatterns = [
            // === AWS Health (Highest Priority) ===
            { regex: /Amazon\s+Health\s+Dashboard/ig, fullName: 'Amazon Health Dashboard', shortName: 'Amazon Health', baseName: 'Amazon Health Dashboard' },
            { regex: /AWS\s+Health\s+Dashboard/ig, fullName: 'Amazon Health Dashboard', shortName: 'Amazon Health', baseName: 'Amazon Health Dashboard' },
            { regex: /Amazon\s+Health\b(?!Lake|Imaging)/ig, fullName: 'Amazon Health Dashboard', shortName: 'Amazon Health', baseName: 'Amazon Health Dashboard' },
            { regex: /AWS\s+Health\b(?!Lake|Imaging|\s+Dashboard)/ig, fullName: 'Amazon Health Dashboard', shortName: 'Amazon Health', baseName: 'Amazon Health Dashboard' },

            // === Amazon Elastic Compute Cloud (EC2) ===
            { regex: /Amazon\s+Elastic\s+Compute\s+Cloud\s*\(EC2\)\s*((?:[A-Za-z0-9][a-zA-Z0-9.-]*\s+)?(?:instance|instances|instance\s+family|instance\s+type))/ig, fullName: 'Amazon Elastic Compute Cloud (EC2)', shortName: 'Amazon EC2', baseName: 'Amazon Elastic Compute Cloud', isCompoundWithSuffix: true, suffixGroup: 1 },
            { regex: /Amazon\s+Elastic\s+Compute\s+Cloud\s*\(EC2\)/ig, fullName: 'Amazon Elastic Compute Cloud (EC2)', shortName: 'Amazon EC2', baseName: 'Amazon Elastic Compute Cloud' },
            { regex: /Amazon\s+EC2\s+((?:[A-Za-z0-9][a-zA-Z0-9.-]*\s+)?(?:instance|instances|instance\s+family|instance\s+type))/ig, fullName: 'Amazon Elastic Compute Cloud (EC2)', shortName: 'Amazon EC2', baseName: 'Amazon Elastic Compute Cloud', isCompoundWithSuffix: true, suffixGroup: 1 },
            { regex: /Amazon\s+EC2\s+Auto\s+Scaling/ig, fullName: 'Amazon EC2 Auto Scaling', shortName: 'Amazon EC2 Auto Scaling', baseName: 'Amazon EC2 Auto Scaling' },
            { regex: /Amazon\s+EC2\b/ig, fullName: 'Amazon Elastic Compute Cloud (EC2)', shortName: 'Amazon EC2', baseName: 'Amazon Elastic Compute Cloud' },
            { regex: /(?<![:_-])\bEC2\s+((?:[A-Za-z0-9][a-zA-Z0-9.-]*\s+)?(?:instance|instances|instance\s+family|instance\s+type))/ig, fullName: 'Amazon Elastic Compute Cloud (EC2)', shortName: 'Amazon EC2', baseName: 'Amazon Elastic Compute Cloud', isCompoundWithSuffix: true, suffixGroup: 1 }, // This one can match "EC2 P3 instances"
            { regex: /(?<![:_-])\bEC2\b(?![:_-])/ig, fullName: 'Amazon Elastic Compute Cloud (EC2)', shortName: 'Amazon EC2', baseName: 'Amazon Elastic Compute Cloud' },

            // === Amazon Elastic Block Store (EBS) ===
            { regex: /Amazon\s+Elastic\s+Block\s+Store\s*\(EBS\)\s*(volume\(s\)?|volumes|volume)/ig, fullName: 'Amazon Elastic Block Store (EBS)', shortName: 'Amazon EBS', baseName: 'Amazon Elastic Block Store', isCompoundWithSuffix: true, suffixGroup: 1},
            { regex: /Amazon\s+Elastic\s+Block\s+Store\s*\(EBS\)/ig, fullName: 'Amazon Elastic Block Store (EBS)', shortName: 'Amazon EBS', baseName: 'Amazon Elastic Block Store' },
            { regex: /Amazon\s+EBS\s*(volume\(s\)?|volumes|volume)/ig, fullName: 'Amazon Elastic Block Store (EBS)', shortName: 'Amazon EBS', baseName: 'Amazon Elastic Block Store', isCompoundWithSuffix: true, suffixGroup: 1},
            { regex: /Amazon\s+EBS\b/ig, fullName: 'Amazon Elastic Block Store (EBS)', shortName: 'Amazon EBS', baseName: 'Amazon Elastic Block Store' },
            { regex: /\bEBS\s*(volume\(s\)?|volumes|volume)/ig, fullName: 'Amazon Elastic Block Store (EBS)', shortName: 'Amazon EBS', baseName: 'Amazon Elastic Block Store', isCompoundWithSuffix: true, suffixGroup: 1 },
            { regex: /\bEBS\b/ig, fullName: 'Amazon Elastic Block Store (EBS)', shortName: 'Amazon EBS', baseName: 'Amazon Elastic Block Store' },

            // === Amazon Identity and Access Management (IAM) ===
            {
                regex: /(?<!Amazon\s|AWS\s)Identity\s+and\s+Access\s+Management\s*\(IAM\)/ig,
                fullName: 'Amazon Identity and Access Management (IAM)',
                shortName: 'IAM',
                baseName: 'Amazon Identity and Access Management'
            },
            { regex: /\bAWS\s+IAM\s+(policy|policies|role|roles|user|users)/ig, fullName: 'Amazon Identity and Access Management (IAM)', shortName: 'IAM', baseName: 'Amazon Identity and Access Management', isCompoundWithSuffix: true, suffixGroup: 1 },
            { regex: /Amazon\s+Identity\s+and\s+Access\s+Management\s*\(IAM\)\s*(policy|policies|role|roles|user|users)/ig, fullName: 'Amazon Identity and Access Management (IAM)', shortName: 'IAM', baseName: 'Amazon Identity and Access Management', isCompoundWithSuffix: true, suffixGroup: 1 },
            { regex: /Amazon\s+Identity\s+and\s+Access\s+Management\s*\(IAM\)/ig, fullName: 'Amazon Identity and Access Management (IAM)', shortName: 'IAM', baseName: 'Amazon Identity and Access Management' },
            { regex: /AWS\s+Identity\s+and\s+Access\s+Management\s*\(IAM\)/ig, fullName: 'Amazon Identity and Access Management (IAM)', shortName: 'IAM', baseName: 'Amazon Identity and Access Management' },
            { regex: /AWS\s+Identity\s+and\s+Access\s+Management\b(?!.*Analyzer)/ig, fullName: 'Amazon Identity and Access Management (IAM)', shortName: 'IAM', baseName: 'Amazon Identity and Access Management' },
            { regex: /\bIAM\s+(policy|policies|role|roles|user|users)/ig, fullName: 'Amazon Identity and Access Management (IAM)', shortName: 'IAM', baseName: 'Amazon Identity and Access Management', isCompoundWithSuffix: true, suffixGroup: 1 },
            { regex: /\bAWS\s+IAM\b(?!.*Access\s+Analyzer|\s+(?:policy|policies|role|roles|user|users))/ig, fullName: 'Amazon Identity and Access Management (IAM)', shortName: 'IAM', baseName: 'Amazon Identity and Access Management' },
            { regex: /\bIAM\b(?!.*Access\s+Analyzer|\s+Identity\s+Center|\s+(?:policy|policies|role|roles|user|users))/ig, fullName: 'Amazon Identity and Access Management (IAM)', shortName: 'IAM', baseName: 'Amazon Identity and Access Management' },
            { regex: /Amazon\s+IAM\s+Identity\s+Center/ig, fullName: 'Amazon IAM Identity Center', shortName: 'IAM Identity Center', baseName: 'Amazon IAM Identity Center' },

            // === Amazon Virtual Private Cloud (VPC) ===
            { regex: /Amazon\s+Virtual\s+Private\s+Cloud\s*\(VPC\)(s)?/ig, fullName: 'Amazon Virtual Private Cloud (VPC)', shortName: 'Amazon VPC', baseName: 'Amazon Virtual Private Cloud' },
            { regex: /Amazon\s+VPC(s)?\b/ig, fullName: 'Amazon Virtual Private Cloud (VPC)', shortName: 'Amazon VPC', baseName: 'Amazon Virtual Private Cloud' },
            { regex: /\bVPC(s)?\b/ig, fullName: 'Amazon Virtual Private Cloud (VPC)', shortName: 'Amazon VPC', baseName: 'Amazon Virtual Private Cloud' },

            // === Amazon Relational Database Service (RDS) ===
            { regex: /Amazon\s+Relational\s+Database\s+Service\s*\(RDS\)\s*(for\s+[\w\s().-]+)/ig, fullName: 'Amazon Relational Database Service (RDS)', shortName: 'Amazon RDS', baseName: 'Amazon Relational Database Service', isCompoundWithSuffix: true, suffixGroup: 1 },
            { regex: /Amazon\s+RDS\s*(for\s+[\w\s().-]+)/ig, fullName: 'Amazon Relational Database Service (RDS)', shortName: 'Amazon RDS', baseName: 'Amazon Relational Database Service', isCompoundWithSuffix: true, suffixGroup: 1 },
            { regex: /(?<![:_-])\bRDS\s*(for\s+[\w\s().-]+)/ig, fullName: 'Amazon Relational Database Service (RDS)', shortName: 'Amazon RDS', baseName: 'Amazon Relational Database Service', isCompoundWithSuffix: true, suffixGroup: 1 },
            { regex: /AWS\s+RDS\s*(for\s+[\w\s().-]+)/ig, fullName: 'Amazon Relational Database Service (RDS)', shortName: 'Amazon RDS', baseName: 'Amazon Relational Database Service', isCompoundWithSuffix: true, suffixGroup: 1 },
            { regex: /AWS\s+Relational\s+Database\s+Service\s*\(RDS\)/ig, fullName: 'Amazon Relational Database Service (RDS)', shortName: 'Amazon RDS', baseName: 'Amazon Relational Database Service' },
            { regex: /Amazon\s+Relational\s+Database\s+Service\s*\(Amazon\s+RDS\)/ig, fullName: 'Amazon Relational Database Service (RDS)', shortName: 'Amazon RDS', baseName: 'Amazon Relational Database Service' },
            { regex: /Amazon\s+Relational\s+Database\s+Service\s*\(RDS\)/ig, fullName: 'Amazon Relational Database Service (RDS)', shortName: 'Amazon RDS', baseName: 'Amazon Relational Database Service' },
            { regex: /Amazon\s+RDS\b(?!(:[\w-]+|\s*Proxy|\s*Optimized\s+Read|\s*Optimized\s+Write))/ig, fullName: 'Amazon Relational Database Service (RDS)', shortName: 'Amazon RDS', baseName: 'Amazon Relational Database Service' },
            { regex: /(?<![:_-])\bRDS\b(?!(:[\w-]+|\s*Proxy|\s*Optimized\s+Read|\s*Optimized\s+Write|\s*for\s+))/ig, fullName: 'Amazon Relational Database Service (RDS)', shortName: 'Amazon RDS', baseName: 'Amazon Relational Database Service' },

            // === Amazon Aurora (Order MATTERS: Specific before Generic) ===
            { regex: /Amazon\s+Aurora\s+PostgreSQL\b/ig, fullName: 'Amazon Aurora - PostgreSQL-compatible', shortName: 'Aurora', baseName: 'Amazon Aurora' },
            { regex: /AWS\s+Aurora\s+PostgreSQL\b/ig, fullName: 'Amazon Aurora - PostgreSQL-compatible', shortName: 'Aurora', baseName: 'Amazon Aurora' },
            { regex: /\bAurora\s+PostgreSQL\b/ig, fullName: 'Amazon Aurora - PostgreSQL-compatible', shortName: 'Aurora', baseName: 'Amazon Aurora' },
            { regex: /Amazon\s+Aurora\s+MySQL\b/ig, fullName: 'Amazon Aurora - MySQL-compatible', shortName: 'Aurora', baseName: 'Amazon Aurora' },
            { regex: /AWS\s+Aurora\s+MySQL\b/ig, fullName: 'Amazon Aurora - MySQL-compatible', shortName: 'Aurora', baseName: 'Amazon Aurora' },
            { regex: /\bAurora\s+MySQL\b/ig, fullName: 'Amazon Aurora - MySQL-compatible', shortName: 'Aurora', baseName: 'Amazon Aurora' },
            { regex: /Amazon\s+Aurora\b/ig, fullName: 'Amazon Aurora', shortName: 'Aurora', baseName: 'Amazon Aurora' },
            { regex: /AWS\s+Aurora\b/ig, fullName: 'Amazon Aurora', shortName: 'Aurora', baseName: 'Amazon Aurora' },
            { regex: /\bAurora\b(?![\s-](?:PostgreSQL|MySQL)\b)/ig, fullName: 'Amazon Aurora', shortName: 'Aurora', baseName: 'Amazon Aurora' },

            // === Amazon Security Token Service (STS) ===
            { regex: /Amazon\s+Security\s+Token\s+Service\s*\(Amazon\s+STS\)/ig, fullName: 'Amazon Security Token Service (Amazon STS)', shortName: 'Amazon STS', baseName: 'Amazon Security Token Service' },
            { regex: /Amazon\s+Security\s+Token\s+Service\s*\(STS\)/ig, fullName: 'Amazon Security Token Service (Amazon STS)', shortName: 'Amazon STS', baseName: 'Amazon Security Token Service' },
            { regex: /AWS\s+Security\s+Token\s+Service\s*\(STS\)/ig, fullName: 'Amazon Security Token Service (Amazon STS)', shortName: 'Amazon STS', baseName: 'Amazon Security Token Service' },
            { regex: /Amazon\s+Security\s+Token\s+Service\b(?!.*\s*\(STS\))/ig, fullName: 'Amazon Security Token Service (Amazon STS)', shortName: 'Amazon STS', baseName: 'Amazon Security Token Service' },
            { regex: /AWS\s+Security\s+Token\s+Service\b(?!.*\s*\(STS\))/ig, fullName: 'Amazon Security Token Service (Amazon STS)', shortName: 'Amazon STS', baseName: 'Amazon Security Token Service' },
            { regex: /Amazon\s+STS\b/ig, fullName: 'Amazon Security Token Service (Amazon STS)', shortName: 'Amazon STS', baseName: 'Amazon Security Token Service' },
            { regex: /AWS\s+STS\b/ig, fullName: 'Amazon Security Token Service (Amazon STS)', shortName: 'Amazon STS', baseName: 'Amazon Security Token Service' },
            { regex: /\bSTS\b(?![:-])/ig, fullName: 'Amazon Security Token Service (Amazon STS)', shortName: 'Amazon STS', baseName: 'Amazon Security Token Service' },

            // === Amazon Elastic Beanstalk ===
            { regex: /Amazon\s+Elastic\s+Beanstalk/ig, fullName: 'Amazon Elastic Beanstalk', shortName: 'Elastic Beanstalk', baseName: 'Amazon Elastic Beanstalk' },
            { regex: /AWS\s+Elastic\s+Beanstalk/ig, fullName: 'Amazon Elastic Beanstalk', shortName: 'Elastic Beanstalk', baseName: 'Amazon Elastic Beanstalk' },
            { regex: /\bElastic\s+Beanstalk\b/ig, fullName: 'Amazon Elastic Beanstalk', shortName: 'Elastic Beanstalk', baseName: 'Amazon Elastic Beanstalk' },

            // === Amazon Elastic Kubernetes Service (EKS) ===
            { regex: /Amazon\s+Elastic\s+Kubernetes\s+Service\s*\(EKS\)/ig, fullName: 'Amazon Elastic Kubernetes Service (EKS)', shortName: 'Amazon EKS', baseName: 'Amazon Elastic Kubernetes Service' },
            { regex: /Amazon\s+Elastic\s+Kubernetes\s+Service\b(?!.*\s*\(EKS\))/ig, fullName: 'Amazon Elastic Kubernetes Service (EKS)', shortName: 'Amazon EKS', baseName: 'Amazon Elastic Kubernetes Service' },
            { regex: /AWS\s+Elastic\s+Kubernetes\s+Service\s*\(EKS\)/ig, fullName: 'Amazon Elastic Kubernetes Service (EKS)', shortName: 'Amazon EKS', baseName: 'Amazon Elastic Kubernetes Service' },
            { regex: /AWS\s+Elastic\s+Kubernetes\s+Service\b(?!.*\s*\(EKS\))/ig, fullName: 'Amazon Elastic Kubernetes Service (EKS)', shortName: 'Amazon EKS', baseName: 'Amazon Elastic Kubernetes Service' },
            { regex: /Amazon\s+EKS\b/ig, fullName: 'Amazon Elastic Kubernetes Service (EKS)', shortName: 'Amazon EKS', baseName: 'Amazon Elastic Kubernetes Service' },
            { regex: /AWS\s+EKS\b/ig, fullName: 'Amazon Elastic Kubernetes Service (EKS)', shortName: 'Amazon EKS', baseName: 'Amazon Elastic Kubernetes Service' },
            { regex: /\bEKS\b(?![:\-])/ig, fullName: 'Amazon Elastic Kubernetes Service (EKS)', shortName: 'Amazon EKS', baseName: 'Amazon Elastic Kubernetes Service' },

            // === Elastic Load Balancing (ELB) ===
            { regex: /Elastic\s+Load\s+Balancing\s*\(ELB\)/ig, fullName: 'Elastic Load Balancing (ELB)', shortName: 'ELB', baseName: 'Elastic Load Balancing' },
            { regex: /\bElastic\s+Load\s+Balancing\b(?!\s*\(ELB\))/ig, fullName: 'Elastic Load Balancing (ELB)', shortName: 'ELB', baseName: 'Elastic Load Balancing' },
            { regex: /\bELB\b(?![:\-])/ig, fullName: 'Elastic Load Balancing (ELB)', shortName: 'ELB', baseName: 'Elastic Load Balancing' },

            // === Amazon Linux 2023 ===
            { regex: /Amazon\s+Linux\s+2023/ig, fullName: 'Amazon Linux 2023', shortName: 'Amazon Linux 2023', baseName: 'Amazon Linux 2023' },
            { regex: /AL2023/ig, fullName: 'Amazon Linux 2023', shortName: 'AL2023', baseName: 'Amazon Linux 2023' },

            // === Amazon Machine Image (AMI) ===
            { regex: /Amazon\s+Machine\s+Image(?:s)?\s*\(AMI(?:s)?\)/ig, fullName: 'Amazon Machine Image (AMI)', shortName: 'AMI', baseName: 'Amazon Machine Image'},
            { regex: /Amazon\s+Machine\s+Image(?:s)?\b/ig, fullName: 'Amazon Machine Image (AMI)', shortName: 'AMI', baseName: 'Amazon Machine Image'},
            { regex: /\bAMI(?:s)?\b/ig, fullName: 'Amazon Machine Image (AMI)', shortName: 'AMI', baseName: 'Amazon Machine Image'},

            // === Amazon CloudFormation ===
            { regex: /Amazon\s+CloudFormation\b/ig, fullName: 'Amazon CloudFormation', shortName: 'CloudFormation', baseName: 'Amazon CloudFormation' },
            { regex: /\bCloudFormation\b/ig, fullName: 'Amazon CloudFormation', shortName: 'CloudFormation', baseName: 'Amazon CloudFormation' },

            // === AWS Glue ===
            { regex: /AWS\s+Glue\b/ig, fullName: 'Amazon Glue', shortName: 'Glue', baseName: 'Amazon Glue' },
            { regex: /Amazon\s+Glue\b/ig, fullName: 'Amazon Glue', shortName: 'Glue', baseName: 'Amazon Glue' },
            { regex: /(?<![:_-])\bGlue\b(?![:_-])/ig, fullName: 'Amazon Glue', shortName: 'Glue', baseName: 'Amazon Glue' },

            // === AWS Lambda ===
            { regex: /AWS\s+Lambda/ig, fullName: 'Amazon Lambda', shortName: 'Lambda', baseName: 'Amazon Lambda' },
            { regex: /Amazon\s+Lambda/ig, fullName: 'Amazon Lambda', shortName: 'Lambda', baseName: 'Amazon Lambda' },
            { regex: /(?<![:_-])\bLambda\b(?![:_-])/ig, fullName: 'Amazon Lambda', shortName: 'Lambda', baseName: 'Amazon Lambda' },

            // === Amazon Serverless Application Model (SAM) ===
            { regex: /Amazon\s+Serverless\s+Application\s+Model\s*\(Amazon\s+SAM\)/ig, fullName: 'Amazon Serverless Application Model (Amazon SAM)', shortName: 'Amazon SAM', baseName: 'Amazon Serverless Application Model' },
            { regex: /Amazon\s+Serverless\s+Application\s+Model\s*\(SAM\)/ig, fullName: 'Amazon Serverless Application Model (Amazon SAM)', shortName: 'Amazon SAM', baseName: 'Amazon Serverless Application Model' },
            { regex: /AWS\s+Serverless\s+Application\s+Model\s*\(SAM\)/ig, fullName: 'Amazon Serverless Application Model (Amazon SAM)', shortName: 'Amazon SAM', baseName: 'Amazon Serverless Application Model' },
            { regex: /Amazon\s+Serverless\s+Application\s+Model\b(?!.*\s*\((?:Amazon\s+)?SAM\))/ig, fullName: 'Amazon Serverless Application Model (Amazon SAM)', shortName: 'Amazon SAM', baseName: 'Amazon Serverless Application Model' },
            { regex: /AWS\s+Serverless\s+Application\s+Model\b(?!.*\s*\((?:Amazon\s+)?SAM\))/ig, fullName: 'Amazon Serverless Application Model (Amazon SAM)', shortName: 'Amazon SAM', baseName: 'Amazon Serverless Application Model' },
            { regex: /Serverless\s+Application\s+Model\s*\(SAM\)/ig, fullName: 'Amazon Serverless Application Model (Amazon SAM)', shortName: 'Amazon SAM', baseName: 'Amazon Serverless Application Model' },
            { regex: /Serverless\s+Application\s+Model\b(?!.*\s*\((?:Amazon\s+)?SAM\))/ig, fullName: 'Amazon Serverless Application Model (Amazon SAM)', shortName: 'Amazon SAM', baseName: 'Amazon Serverless Application Model' },
            { regex: /Amazon\s+SAM\b/ig, fullName: 'Amazon Serverless Application Model (Amazon SAM)', shortName: 'Amazon SAM', baseName: 'Amazon Serverless Application Model' },
            { regex: /AWS\s+SAM\b/ig, fullName: 'Amazon Serverless Application Model (Amazon SAM)', shortName: 'Amazon SAM', baseName: 'Amazon Serverless Application Model' },
            { regex: /\bSAM\b(?![:\-])/ig, fullName: 'Amazon Serverless Application Model (Amazon SAM)', shortName: 'Amazon SAM', baseName: 'Amazon Serverless Application Model' },


            // === S3, DynamoDB, CloudWatch, SNS, SQS (Relatively simpler) ===
            { regex: /Amazon\s+Simple\s+Storage\s+Service\s*\(S3\)/ig, fullName: 'Amazon Simple Storage Service (S3)', shortName: 'Amazon S3', baseName: 'Amazon Simple Storage Service' },
            { regex: /Amazon\s+Simple\s+Storage\s+Service\s+Glacier\s*\(Amazon\s+S3\s+Glacier\)/ig, fullName: 'Amazon Simple Storage Service Glacier (Amazon S3 Glacier)', shortName: 'Amazon S3 Glacier', baseName: 'Amazon Simple Storage Service Glacier' },
            { regex: /Amazon\s+S3\s+Glacier/ig, fullName: 'Amazon Simple Storage Service Glacier (Amazon S3 Glacier)', shortName: 'Amazon S3 Glacier', baseName: 'Amazon Simple Storage Service Glacier' },
            { regex: /Amazon\s+S3\b/ig, fullName: 'Amazon Simple Storage Service (S3)', shortName: 'Amazon S3', baseName: 'Amazon Simple Storage Service' },
            { regex: /(?<![:_-])\bS3\b(?![:_-])/ig, fullName: 'Amazon Simple Storage Service (S3)', shortName: 'Amazon S3', baseName: 'Amazon Simple Storage Service' },

            { regex: /Amazon\s+DynamoDB/ig, fullName: 'Amazon DynamoDB', shortName: 'DynamoDB', baseName: 'Amazon DynamoDB' },
            { regex: /(?<![:_-])\bDynamoDB\b(?![:_-])/ig, fullName: 'Amazon DynamoDB', shortName: 'DynamoDB', baseName: 'Amazon DynamoDB' },

            { regex: /Amazon\s+CloudWatch\s+Events/ig, fullName: 'Amazon CloudWatch Events', shortName: 'CloudWatch Events', baseName: 'Amazon CloudWatch Events' },
            { regex: /Amazon\s+CloudWatch\s+Logs/ig, fullName: 'Amazon CloudWatch Logs', shortName: 'CloudWatch Logs', baseName: 'Amazon CloudWatch Logs' },
            { regex: /Amazon\s+CloudWatch(?!\s+Events|\s+Logs)/ig, fullName: 'Amazon CloudWatch', shortName: 'CloudWatch', baseName: 'Amazon CloudWatch' },
            { regex: /\bCloudWatch\b(?!\s+Events|\s+Logs)/ig, fullName: 'Amazon CloudWatch', shortName: 'CloudWatch', baseName: 'Amazon CloudWatch' },

            { regex: /Amazon\s+Simple\s+Notification\s+Service\s*\(SNS\)/ig, fullName: 'Amazon Simple Notification Service (SNS)', shortName: 'Amazon SNS', baseName: 'Amazon Simple Notification Service' },
            { regex: /Amazon\s+SNS\b/ig, fullName: 'Amazon Simple Notification Service (SNS)', shortName: 'Amazon SNS', baseName: 'Amazon Simple Notification Service' },
            { regex: /\bSNS\b/ig, fullName: 'Amazon Simple Notification Service (SNS)', shortName: 'Amazon SNS', baseName: 'Amazon Simple Notification Service' },

            { regex: /Amazon\s+Simple\s+Queue\s+Service\s*\(SQS\)/ig, fullName: 'Amazon Simple Queue Service (SQS)', shortName: 'Amazon SQS', baseName: 'Amazon Simple Queue Service' },
            { regex: /Amazon\s+SQS\b/ig, fullName: 'Amazon Simple Queue Service (SQS)', shortName: 'Amazon SQS', baseName: 'Amazon Simple Queue Service' },
            { regex: /\bSQS\b/ig, fullName: 'Amazon Simple Queue Service (SQS)', shortName: 'Amazon SQS', baseName: 'Amazon Simple Queue Service' },

            // === Amazon Systems Manager ===
            { regex: /AWS\s+Systems\s+Manager\s*\(SSM\)/ig, fullName: 'Amazon Systems Manager', shortName: 'Systems Manager', baseName: 'Amazon Systems Manager' },
            { regex: /AWS\s+SSM\b/ig, fullName: 'Amazon Systems Manager', shortName: 'Systems Manager', baseName: 'Amazon Systems Manager' },
            { regex: /Amazon\s+Systems\s+Manager/ig, fullName: 'Amazon Systems Manager', shortName: 'Systems Manager', baseName: 'Amazon Systems Manager' },
            { regex: /AWS\s+Systems\s+Manager\b/ig, fullName: 'Amazon Systems Manager', shortName: 'Systems Manager', baseName: 'Amazon Systems Manager' },
            { regex: /\bSystems\s+Manager\b/ig, fullName: 'Amazon Systems Manager', shortName: 'Systems Manager', baseName: 'Amazon Systems Manager' },
            { regex: /\bSSM\b(?!-)/ig, fullName: 'Amazon Systems Manager', shortName: 'Systems Manager', baseName: 'Amazon Systems Manager' },

            // === Amazon Batch ===
            { regex: /AWS\s+Batch/ig, fullName: 'Amazon Batch', shortName: 'Amazon Batch', baseName: 'Amazon Batch' },
            { regex: /Amazon\s+Batch/ig, fullName: 'Amazon Batch', shortName: 'Amazon Batch', baseName: 'Amazon Batch' },
            { regex: /(?<![:_-])\bBatch\b(?![:_-])/ig, fullName: 'Amazon Batch', shortName: 'Amazon Batch', baseName: 'Amazon Batch' },


            // === CLI & SCP (Corrected order and added Amazon CLI pattern) ===
            { regex: /Amazon\s+Command\s+Line\s+Interface\s*\(Amazon\s+CLI\)/ig, fullName: 'Amazon Command Line Interface (Amazon CLI)', shortName: 'Amazon CLI', baseName: 'Amazon Command Line Interface' },
            { regex: /Amazon\s+CLI\b/ig, fullName: 'Amazon Command Line Interface (Amazon CLI)', shortName: 'Amazon CLI', baseName: 'Amazon Command Line Interface' },
            { regex: /AWS\s+CLI\b/ig, fullName: 'Amazon Command Line Interface (Amazon CLI)', shortName: 'Amazon CLI', baseName: 'Amazon Command Line Interface' },
            { regex: /\bCLI\b/ig, fullName: 'Amazon Command Line Interface (Amazon CLI)', shortName: 'Amazon CLI', baseName: 'Amazon Command Line Interface' },


            { regex: /Service\s+Control\s+Policies\s*\(SCPs\)/ig, fullName: 'Service Control Policies (SCPs)', shortName: 'SCPs', baseName: 'Service Control Policies' },
            { regex: /AWS\s+SCP\s*\(Service\s+control\s+policies\)/ig, fullName: 'Service Control Policies (SCPs)', shortName: 'SCPs', baseName: 'Service Control Policies' },
            { regex: /AWS\s+SCP\b/ig, fullName: 'Service Control Policies (SCPs)', shortName: 'SCPs', baseName: 'Service Control Policies' },
            { regex: /\bSCPs\b/ig, fullName: 'Service Control Policies (SCPs)', shortName: 'SCPs', baseName: 'Service Control Policies' },
            { regex: /\bSCP\b(?!s)/ig, fullName: 'Service Control Policies (SCPs)', shortName: 'SCPs', baseName: 'Service Control Policies' },
        ];

        function standardizeTextByLines(text) {
            const lines = text.split('\n');
            const processedLines = [];
            let globalServiceMentionState = {};
            window.finalGlobalServiceMentionState = globalServiceMentionState; // Used by replaceTermsWithPlaceholders

            for (const line of lines) {
                // Match specific metadata keys whose *values* should NOT go through standardizeEnglishChunk
                const metaLineNoValueProcessingMatch = line.match(/^((?:Service|Region|Failure mode(?: \d+)?|TypeCode|Event Type Code))\s*:(.*)$/i);
                // Match Wording/First Post Wording lines to separate key from content
                const wordingLineMatch = line.match(/^(.*(?:First Post Wording|Wording)\s*:\s*)(.*)$/i); // Catches "Wording: " or "First Post Wording: "

                if (metaLineNoValueProcessingMatch) {
                    // For Service, Region, Failure mode, TypeCode, Event Type Code:
                    // Push the entire line. The key will be protected later by replaceTermsWithPlaceholders.
                    // The value part will also be processed by replaceTermsWithPlaceholders for any standardizable terms it might contain.
                    processedLines.push(line);
                } else if (wordingLineMatch) {
                    // For Wording:, First Post Wording:
                    const prefix = wordingLineMatch[1]; // e.g., "Wording: " (includes the key)
                    let content = wordingLineMatch[2];  // Content after the colon

                    // Apply standardizeEnglishChunk to the *content* part (Phase 1 for Wording content)
                    let lineScopedServiceMentionState = JSON.parse(JSON.stringify(globalServiceMentionState));
                    const standardizedContent = standardizeEnglishChunk(content, lineScopedServiceMentionState, 'line');

                    // Update global state based on line-scoped changes if a full name was used for the first time
                    for (const service in lineScopedServiceMentionState) {
                        if (lineScopedServiceMentionState[service].usedFullName && !globalServiceMentionState[service]?.usedFullName) {
                            if (!globalServiceMentionState[service]) {
                                 globalServiceMentionState[service] = JSON.parse(JSON.stringify(lineScopedServiceMentionState[service]));
                            } else {
                                 globalServiceMentionState[service].usedFullName = true;
                                 globalServiceMentionState[service].mentioned = true;
                            }
                        }
                    }
                    processedLines.push(prefix + standardizedContent); // Recombine original prefix with standardized content
                } else {
                    // General content lines (not matching specific metadata keys or Wording patterns)
                    processedLines.push(standardizeEnglishChunk(line, globalServiceMentionState, 'global'));
                }
            }
            return processedLines.join('\n');
        }


        function standardizeEnglishChunk(textChunk, currentServiceMentionState, scopeType) {
            logDebug(`JS标准化块 (${scopeType}) - 输入`, { text: textChunk.substring(0,200), scope: scopeType, stateBefore: JSON.parse(JSON.stringify(currentServiceMentionState)) });
            let standardizedText = textChunk;
            const originalTextChunkForContext = textChunk;

            let tempUrlMap = {};            let internalUrlCounter = 0;
            let tempPolicyMap = {};         let internalPolicyCounter = 0;
            let tempJsonBlockMap = {};      let internalJsonCounter = 0;
            // Removed tempArnMap and internalArnCounter as ARN protection is moved to replaceTermsWithPlaceholders
            let tempCliCommandMap = {};     let cliPlaceholderCounter = 0;
            let tempAwsCodeMap = {};        let awsCodePlaceholderCounter = 0;


            // Protect AWS_SPECIFIC_ERROR_CODES or TYPE_CODES first
            standardizedText = standardizedText.replace(/\bAWS_([A-Z0-9]+_)*[A-Z0-9]+\b/g, (match) => {
                const placeholder = `__AWS_KEYCODE_${awsCodePlaceholderCounter++}__`;
                tempAwsCodeMap[placeholder] = match;
                return placeholder;
            });
            // No logDebug here for AWS_KEYCODE to reduce noise, will be seen in next step if present

            standardizedText = standardizedText.replace(/(^\s*aws\s+[^\n]+)/gm, (fullCliCommand) => {
                let processedCliCommand = fullCliCommand;
                processedCliCommand = processedCliCommand.replace(/--region\s+(?!\{\{region\}\})([a-z0-9-]+)\b/gi, (match, regionCode) => {
                    if (regionCode.startsWith('cn-')) return match;
                    let targetRegion = 'cn-north-1';
                    if (originalTextChunkForContext.toLowerCase().includes('zhy') || originalTextChunkForContext.toLowerCase().includes('宁夏')) {
                        targetRegion = 'cn-northwest-1';
                    } else if (originalTextChunkForContext.toLowerCase().includes('bjs') || originalTextChunkForContext.toLowerCase().includes('北京')) {
                        targetRegion = 'cn-north-1';
                    }
                    return `--region ${targetRegion}`;
                });
                const placeholder = `__CLI_CMD_PLACEHOLDER_${cliPlaceholderCounter++}__`;
                tempCliCommandMap[placeholder] = processedCliCommand;
                return placeholder;
            });
            // No logDebug here for CLI to reduce noise

            // ARN Processing REMOVED from standardizeEnglishChunk. It will be handled by replaceTermsWithPlaceholders.

            // MODIFIED Regex for JSON block in standardizeEnglishChunk
            standardizedText = standardizedText.replace(/\{\s*"Version":[\s\S]*?\s*\n\s*\}/g, (match) => {
                const jsonPlaceholder = `__TEMP_JSON_BLOCK_${internalJsonCounter++}__`;
                tempJsonBlockMap[jsonPlaceholder] = match; return jsonPlaceholder;
            });

            standardizedText = standardizedText.replace(/https?:\/\/[^\s"'<>`{}|\\^\[\]]+/gi, (match) => {
                let cleanMatch = match;
                const trailingPunct = /[.,;:!?\)\]\}]$/;
                if (trailingPunct.test(cleanMatch)) { cleanMatch = cleanMatch.slice(0, -1); }
                const urlPlaceholder = `__TEMP_RAW_URL_${internalUrlCounter++}__`;
                tempUrlMap[urlPlaceholder] = cleanMatch;
                return urlPlaceholder + (match.endsWith(cleanMatch) ? "" : match.substring(cleanMatch.length));
            });
            standardizedText = standardizedText.replace(/'([a-zA-Z0-9_-]+:[A-Za-z0-9*_-]+)'/g, (match, policyString) => {
                const policyPlaceholder = `__TEMP_POLICY_STRING_${internalPolicyCounter++}__`;
                tempPolicyMap[policyPlaceholder] = match; return policyPlaceholder;
            });

            if(awsCodePlaceholderCounter > 0 || cliPlaceholderCounter > 0 || internalJsonCounter > 0 || internalUrlCounter > 0 || internalPolicyCounter > 0) {
                 logDebug("JS标准化：特殊结构初步保护后 (KEYCODE, CLI, JSON, URL, Policy)", {textPreview: standardizedText.substring(0,200)});
            }


            logDebug("JS标准化：开始服务名称专门替换 (servicePatterns)", {textPreview: standardizedText.substring(0,200), stateBeforeServices: JSON.parse(JSON.stringify(currentServiceMentionState))});
            const sortedServicePatterns = [...servicePatterns].sort((a, b) => {
                const lenA = a.regex.source.length; const lenB = b.regex.source.length;
                if (lenA !== lenB) return lenB - lenA;
                if (a.isCompoundWithSuffix && !b.isCompoundWithSuffix) return -1;
                if (!a.isCompoundWithSuffix && b.isCompoundWithSuffix) return 1;
                return 0;
            });

            let serviceAnalysisText = standardizedText;
            const serviceOccurrences = [];

            sortedServicePatterns.forEach(patternInfo => {
                const currentRegex = new RegExp(patternInfo.regex.source, patternInfo.regex.flags.includes('g') ? patternInfo.regex.flags : patternInfo.regex.flags + 'g');
                let match;
                while ((match = currentRegex.exec(serviceAnalysisText)) !== null) {
                    let isOverlapped = serviceOccurrences.some(occ => (match.index < occ.index + occ.text.length && match.index + match[0].length > occ.index) && (occ.text.length > match[0].length || (occ.text.length === match[0].length && occ.index < match.index)));
                    if (isOverlapped) continue;
                    for (let i = serviceOccurrences.length - 1; i >= 0; i--) {
                        const existingOcc = serviceOccurrences[i];
                        if (existingOcc.index >= match.index && (existingOcc.index + existingOcc.text.length) <= (match.index + match[0].length)) {
                            if (existingOcc.text.length < match[0].length) { serviceOccurrences.splice(i, 1); }
                        }
                    }
                    serviceOccurrences.push({ index: match.index, text: match[0], pattern: patternInfo, matchArray: [...match] });
                }
            });
            serviceOccurrences.sort((a, b) => a.index - b.index);

            const healthRelatedOccurrences = serviceOccurrences.filter(occ => occ.pattern.baseName === 'Amazon Health Dashboard');
            if (healthRelatedOccurrences.length > 0 || serviceAnalysisText.toLowerCase().includes('health')) {
                logDebug('AWS_HEALTH_DEBUG: Health-related occurrences before processing loop', {
                    healthMatchesCount: healthRelatedOccurrences.length,
                    allOccurrencesCount: serviceOccurrences.length,
                    healthOccurrencesDetails: JSON.parse(JSON.stringify(healthRelatedOccurrences.map(o => ({ text: o.text, index: o.index, patternFullName: o.pattern.fullName, patternShortName: o.pattern.shortName, patternRegex: o.pattern.regex.source })))),
                    textBeingAnalyzedForOccurrences: serviceAnalysisText.substring(0,500)
                });
            }

            let offsetAdjustment = 0;
            serviceOccurrences.forEach(occurrence => {
                const { index, text, pattern, matchArray } = occurrence;
                let replacementText = "";
                let determinedSuffix = ""; // Holds the suffix string, e.g., " instances" or " P3 instances"

                if (pattern.isCompoundWithSuffix && pattern.suffixGroup && matchArray[pattern.suffixGroup]) {
                    let capturedSuffixText = matchArray[pattern.suffixGroup]; // This is the text captured by the suffix group
                    let trimmedCapturedSuffix = capturedSuffixText.trim();
                    let isValidTypedSuffix = false;

                    // Check if the captured suffix matches a *specific, typed* suffix for the service
                    if (pattern.baseName === "Amazon Elastic Compute Cloud" && /^(instance|instances|instance\s+family|instance\s+type)$/i.test(trimmedCapturedSuffix)) isValidTypedSuffix = true;
                    else if (pattern.baseName === "Amazon Elastic Block Store" && /^(volume\(s\)?|volumes|volume)$/i.test(trimmedCapturedSuffix)) isValidTypedSuffix = true;
                    else if (pattern.baseName === "Amazon Identity and Access Management" && /^(policy|policies|role|roles|user|users)$/i.test(trimmedCapturedSuffix)) isValidTypedSuffix = true;
                    else if (pattern.baseName === "Amazon Relational Database Service" && /^for\s+[\w\s().-]+$/i.test(trimmedCapturedSuffix)) isValidTypedSuffix = true;
                    // Add other baseName checks here if they have specific suffix validation rules

                    // Decision: Always preserve the captured suffix text from the regex group to avoid information loss.
                    // The distinction of isValidTypedSuffix might be used for more nuanced logic later if needed,
                    // but for now, simple preservation is key.
                    // We use the original captured suffix text (before trimming for validation) to preserve original spacing if it was part of the capture group.
                    // However, it's generally safer to trim and add a leading space.
                    determinedSuffix = " " + trimmedCapturedSuffix;
                }


                if (!currentServiceMentionState[pattern.baseName]) {
                    currentServiceMentionState[pattern.baseName] = { mentioned: false, usedFullName: false, fullName: pattern.fullName, shortName: pattern.shortName, };
                }
                const serviceState = currentServiceMentionState[pattern.baseName];
                serviceState.mentioned = true;

                if (pattern.baseName === 'Amazon Health Dashboard') {
                     logDebug('AWS_HEALTH_DEBUG: Processing occurrence IN LOOP', {
                        text: text, patternSource: pattern.regex.source, baseName: pattern.baseName,
                        currentServiceMentionState_AHD_Portion_BEFORE_IF: JSON.parse(JSON.stringify(currentServiceMentionState['Amazon Health Dashboard'] || null)),
                        serviceState_usedFullName_BEFORE_IF: serviceState.usedFullName, scopeType: scopeType
                    });
                }

                // Determine the base replacement (full name or short name)
                let baseReplacementText;
                if (!serviceState.usedFullName) {
                    baseReplacementText = pattern.fullName;
                    serviceState.usedFullName = true;
                    if (pattern.baseName === 'Amazon Health Dashboard') { // Debugging for Health
                        logDebug('AWS_HEALTH_DEBUG: Chose FULL name (usedFullName was false)', { inputText: text, replacementText: baseReplacementText + determinedSuffix, baseName: pattern.baseName, stateAfterChoice: JSON.parse(JSON.stringify(serviceState)) });
                    }
                } else {
                    // If usedFullName is true, generally prefer shortName.
                    // However, if the current `text` (match[0]) IS pattern.fullName + determinedSuffix, don't shorten it.
                    let baseOfOriginalText = text;
                    if (determinedSuffix && text.toLowerCase().endsWith(determinedSuffix.toLowerCase())) { // Check if suffix is at the end
                        baseOfOriginalText = text.substring(0, text.length - determinedSuffix.length);
                    } else if (determinedSuffix.trim() && text.toLowerCase().endsWith(determinedSuffix.trim().toLowerCase())) { // Check trimmed version too
                        baseOfOriginalText = text.substring(0, text.length - determinedSuffix.trim().length);
                    }

                    // Normalize baseOfOriginalText by trimming space that might be left if determinedSuffix had a leading space
                    baseOfOriginalText = baseOfOriginalText.trim();


                    if (baseOfOriginalText.toLowerCase() === pattern.fullName.toLowerCase()) {
                        baseReplacementText = pattern.fullName;
                         if (pattern.baseName === 'Amazon Health Dashboard') {
                            logDebug('AWS_HEALTH_DEBUG: Matched text is ALREADY FULL NAME form (usedFullName is true). KEEPING full name.', { currentMatchText: text, replacementText: baseReplacementText + determinedSuffix, baseName: pattern.baseName });
                        }
                    } else {
                        baseReplacementText = pattern.shortName;
                         if (pattern.baseName === 'Amazon Health Dashboard') {
                             logDebug('AWS_HEALTH_DEBUG: Chose SHORT name (usedFullName was true, text was an alias or short form)', { inputText: text, replacementText: baseReplacementText + determinedSuffix, baseName: pattern.baseName, stateWhenChoosingShort: JSON.parse(JSON.stringify(serviceState)) });
                        }
                    }
                }
                replacementText = baseReplacementText + determinedSuffix;


                if (scopeType === 'global') {
                    if (!window.finalGlobalServiceMentionState[pattern.baseName]) {
                        window.finalGlobalServiceMentionState[pattern.baseName] = { ...serviceState };
                    } else {
                        window.finalGlobalServiceMentionState[pattern.baseName].mentioned = true;
                        if (serviceState.usedFullName) {
                            window.finalGlobalServiceMentionState[pattern.baseName].usedFullName = true;
                        }
                    }
                }


                const currentIndexToReplace = index + offsetAdjustment;
                if (pattern.baseName === 'Amazon Health Dashboard' || text.toLowerCase().includes('health')) {
                     logDebug('AWS_HEALTH_DEBUG: About to replace in standardizedText', {
                        originalSegment: standardizedText.substring(currentIndexToReplace, currentIndexToReplace + text.length),
                        replacingWith: replacementText,
                        originalMatchText: text,
                    });
                }
                standardizedText = standardizedText.substring(0, currentIndexToReplace) + replacementText + standardizedText.substring(currentIndexToReplace + text.length);
                offsetAdjustment += replacementText.length - text.length;

                if (pattern.baseName === 'Amazon Health Dashboard' || text.toLowerCase().includes('health')) {
                    logDebug('AWS_HEALTH_DEBUG: standardizedText after this replacement', {
                        textPreview: standardizedText.substring(Math.max(0, currentIndexToReplace-20), Math.min(standardizedText.length, currentIndexToReplace + replacementText.length + 20))
                    });
                }
            });
            logDebug("JS标准化：服务名专门替换后 (after serviceOccurrences loop)", {textPreview: standardizedText.substring(0,500), stateAfterServicesLoop: JSON.parse(JSON.stringify(currentServiceMentionState))});


            standardizedText = standardizedText.replace(/Amazon\s+Relational\s+Database\s+Service\s*\(Amazon\s+RDS\)/gi, 'Amazon Relational Database Service (RDS)');
            standardizedText = standardizedText.replace(/Amazon\s+Elastic\s+Block\s+Store\s*\(Amazon\s+EBS\)/gi, 'Amazon Elastic Block Store (EBS)');
            standardizedText = standardizedText.replace(/Amazon\s+Elastic\s+Compute\s+Cloud\s*\(Amazon\s+EC2\)/gi, 'Amazon Elastic Compute Cloud (EC2)');
            standardizedText = standardizedText.replace(/Amazon\s+Identity\s+and\s+Access\s+Management\s*\(Amazon\s+IAM\)/gi, 'Amazon Identity and Access Management (IAM)');
            standardizedText = standardizedText.replace(/Amazon\s+Security\s+Token\s+Service\s*\(Amazon\s+STS\)/gi, 'Amazon Security Token Service (Amazon STS)');
            standardizedText = standardizedText.replace(/Amazon\s+Serverless\s+Application\s+Model\s*\(Amazon\s+SAM\)/gi, 'Amazon Serverless Application Model (Amazon SAM)');


            standardizedText = standardizedText.replace(/\bAWS\s+Accounts?\s+Teams?\b/gi, 'Amazon Web Services Support');
            standardizedText = standardizedText.replace(/\bAWS\s+Account\b/gi, 'Amazon Web Services Account');
            standardizedText = standardizedText.replace(/\bAWS\s+Support\s+Center\b/gi, 'Amazon Web Services Support');
            standardizedText = standardizedText.replace(/\bAWS\s+Support\s+Team\b/gi, 'Amazon Web Services Support');
            standardizedText = standardizedText.replace(/\bAWS\s+Support\b(?!([-\w]))/gi, 'Amazon Web Services Support');
            standardizedText = standardizedText.replace(/\bAWS\s+Management\s+Console\b/gi, 'Amazon Web Services Console');
            standardizedText = standardizedText.replace(/\bAWS\s+console\b/gi, 'Amazon Web Services Console');
            standardizedText = standardizedText.replace(/\bAWS\s+SDK\b/gi, 'Amazon SDK');
            standardizedText = standardizedText.replace(/\bthe\s+AWS\s+documentation\b/gi, 'the Amazon Web Services documentation');
            standardizedText = standardizedText.replace(/\bAWS\s+documentation\b/gi, 'Amazon Web Services documentation');
            standardizedText = standardizedText.replace(/\bAWS\s+docs\b/gi, 'Amazon Web Services documentation');
            standardizedText = standardizedText.replace(/\bAWS\s+Service\s+Quota(?:s)?\s+console\b/gi, 'Service Quotas console');
            standardizedText = standardizedText.replace(/\bAWS\s+Service\s+Quota(?:s)?\b(?!(\s+console))\b/gi, 'Service Quotas');
            standardizedText = standardizedText.replace(/\bAWS\s+Nitro\s+System\b/gi, 'Amazon Nitro System');
            standardizedText = standardizedText.replace(/\bAWS\s+Nitro\b/gi, 'Amazon Nitro');

            const awsServiceTermsExemptionList = [
                'S3', 'EC2', 'RDS', 'IAM', 'CLI', 'SDK', 'Support', 'Console', 'Account',
                'Documentation', 'Systems Manager', 'Certificate Manager', 'CloudFormation',
                'Service Quotas', 'Security Token Service', 'STS', 'Serverless Application Model', 'SAM',
                'Nitro', 'Elastic Beanstalk', 'Lambda', 'EKS', 'ELB', // Added ELB
                'Health Dashboard', 'Aurora', 'Glue', 'Batch'
            ].map(term => term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&').replace(/\s+/g, '\\s+'));
            const awsExemptionPattern = awsServiceTermsExemptionList.join('|');

            standardizedText = standardizedText.replace(
                /(?<![A-Za-z0-9\/.:_-])AWS(?!(?:[A-Za-z0-9-.]|\s+(?:${awsExemptionPattern})\b| Web Services\b|:[A-Za-z0-9*\/_-]+))/gi,
                (match, offset, fullString) => {
                    if (/(Amazon Web Services)/i.test(fullString.substring(Math.max(0, offset - 20), offset + match.length + 25))) { return "AWS"; }
                    return '__TEMP_AMAZON_FOR_TRANSLATION__';
                }
            );
            standardizedText = standardizedText.replace(/\bAWS\s+Web\s+Services\b/gi, 'Amazon Web Services');
            logDebug("JS标准化：通用AWS术语替换后", {textPreview: standardizedText.substring(0,200)});


            const timePatterns = [
                { regex: /(\w+\s+\d{1,2},\s+\d{4}),?\s*(\d{1,2}:\d{2}(?::\d{2})?)\s*(AM|PM)?\s*(PDT|PST|EDT|EST|UTC|GMT(?:[+-]\d{1,2}(?::\d{2})?)?)?/gi, format: "verbose_datetime" },
                { regex: /(\d{4}-\d{2}-\d{2})T(\d{2}:\d{2}:\d{2}(?:\.\d+)?Z)/gi, format: "iso_utc_datetime" },
                { regex: /(\d{4}-\d{2}-\d{2})\s+(\d{2}:\d{2}(?::\d{2})?(?:\.\d+)?)\s*(PDT|PST|EDT|EST|UTC|GMT(?:[+-]\d{1,2}(?::\d{2})?)?)/gi, format: "simple_datetime_tzd" },
                { regex: /(\d{4}-\d{2}-\d{2})\s+(\d{1,2}:\d{2}(?::\d{2})?)\s*(AM|PM)?\s*(PDT|PST|EDT|EST|UTC|GMT(?:[+-]\d{1,2}(?::\d{2})?)?)/gi, format: "date_time_ampm_tzd"},
                { regex: /(\w+\s+\d{1,2},\s+\d{4})(?!,\s*\d{1,2}:\d{2})/gi, format: "date_only_verbose"},
                { regex: /(\d{4}-\d{2}-\d{2})(?![T\s]*\d{1,2}:\d{2})/gi, format: "date_only_iso"},
            ];
            let tempTimeProcessingText = standardizedText; let finalStandardizedTextAfterTime = ""; let lastProcessedTimeIndex = 0;
            const allTimeMatchesForChunk = [];
            timePatterns.forEach(tp => {
                let match; const currentRegex = new RegExp(tp.regex.source, tp.regex.flags.includes('g') ? tp.regex.flags : tp.regex.flags + 'g');
                while((match = currentRegex.exec(tempTimeProcessingText)) !== null) { allTimeMatchesForChunk.push({ index: match.index, original: match[0], parts: match.slice(1), format: tp.format }); }
            });
            allTimeMatchesForChunk.sort((a, b) => a.index - b.index || b.original.length - a.original.length);
            const uniqueTimeMatchesForChunk = []; let currentFilterEndTime = -1;
            for (const match of allTimeMatchesForChunk) {
                if (match.index >= currentFilterEndTime) { uniqueTimeMatchesForChunk.push(match); currentFilterEndTime = match.index + match.original.length; }
            }
            uniqueTimeMatchesForChunk.forEach(matchInfo => {
                finalStandardizedTextAfterTime += tempTimeProcessingText.substring(lastProcessedTimeIndex, matchInfo.index);
                let datePartStr, timePartStr, ampmStr, tzdStr; let year, monthIndex, day, hour = 0, minute = 0, second = 0;
                let isDateOnlyMatch = matchInfo.format.startsWith("date_only"); let parsedDateSuccessful = false;
                try {
                    if (matchInfo.format === "verbose_datetime") {
                        [datePartStr, timePartStr, ampmStr, tzdStr] = matchInfo.parts;
                        let tempHour = parseInt(timePartStr.split(':')[0]); minute = parseInt(timePartStr.split(':')[1]); second = timePartStr.split(':')[2] ? parseInt(timePartStr.split(':')[2]) : 0;
                        if (ampmStr && ampmStr.toUpperCase() === 'PM' && tempHour < 12) tempHour += 12;
                        if (ampmStr && ampmStr.toUpperCase() === 'AM' && tempHour === 12) tempHour = 0;
                        hour = tempHour; const dateForParse = new Date(datePartStr);
                        if (!isNaN(dateForParse.getTime())) { year = dateForParse.getFullYear(); monthIndex = dateForParse.getMonth(); day = dateForParse.getDate(); parsedDateSuccessful = true; }
                    } else if (matchInfo.format === "iso_utc_datetime") {
                        const dateForParse = new Date(matchInfo.original);
                         if (!isNaN(dateForParse.getTime())) {
                            year = dateForParse.getUTCFullYear(); monthIndex = dateForParse.getUTCMonth(); day = dateForParse.getUTCDate();
                            hour = dateForParse.getUTCHours(); minute = dateForParse.getUTCMinutes(); second = dateForParse.getUTCSeconds();
                            tzdStr = "UTC"; parsedDateSuccessful = true; }
                    } else if (matchInfo.format === "simple_datetime_tzd" || matchInfo.format === "date_time_ampm_tzd") {
                        [datePartStr, timePartStr, ampmStrOrTzd, tzdStrIfSimple] = matchInfo.parts;
                        tzdStr = (matchInfo.format === "simple_datetime_tzd") ? ampmStrOrTzd : tzdStrIfSimple;
                        ampmStr = (matchInfo.format === "date_time_ampm_tzd") ? ampmStrOrTzd : null;
                        const dateParts = datePartStr.split('-'); year = parseInt(dateParts[0]); monthIndex = parseInt(dateParts[1]) - 1; day = parseInt(dateParts[2]);
                        const timeParts = timePartStr.split(':'); let tempHour = parseInt(timeParts[0]);
                        minute = parseInt(timeParts[1]); second = timeParts[2] ? parseInt(timeParts[2]) : 0;
                        if (ampmStr) { if (ampmStr.toUpperCase() === 'PM' && tempHour < 12) tempHour += 12; if (ampmStr.toUpperCase() === 'AM' && tempHour === 12) tempHour = 0; }
                        hour = tempHour; parsedDateSuccessful = true;
                    } else if (isDateOnlyMatch) {
                        datePartStr = matchInfo.parts[0];
                        let dateForParse;
                        if (matchInfo.format === "date_only_verbose") {
                            dateForParse = new Date(datePartStr + " 00:00:00 UTC");
                        } else {
                            dateForParse = new Date(datePartStr + "T00:00:00Z");
                        }
                         if (!isNaN(dateForParse.getTime())) {
                            year = dateForParse.getUTCFullYear(); monthIndex = dateForParse.getUTCMonth(); day = dateForParse.getUTCDate();
                            tzdStr = "UTC";
                            parsedDateSuccessful = true;
                        }
                    }

                    if (!parsedDateSuccessful) { finalStandardizedTextAfterTime += matchInfo.original;
                    } else {
                        let dateObjInUTC = new Date(Date.UTC(year, monthIndex, day, hour, minute, second));
                        if (!isDateOnlyMatch && tzdStr) {
                            let sourceOffsetHours = 0; const tzdUpper = tzdStr.toUpperCase();
                            if (tzdUpper === "PDT") sourceOffsetHours = -7; else if (tzdUpper === "PST") sourceOffsetHours = -8;
                            else if (tzdUpper === "EDT") sourceOffsetHours = -4; else if (tzdUpper === "EST") sourceOffsetHours = -5;
                            else if (tzdUpper === "UTC" || tzdUpper === "GMT") sourceOffsetHours = 0;
                            else if (tzdUpper.startsWith("GMT") || tzdUpper.startsWith("UTC")) {
                                const sign = tzdUpper.includes('+') ? 1 : (tzdUpper.includes('-') ? -1 : 0);
                                if (sign !==0 ) {
                                    const timePartVal = tzdUpper.substring(sign !== 0 ? (tzdUpper.startsWith("GMT") ? 3:3) : 3);
                                    const hm = timePartVal.split(':'); sourceOffsetHours = sign * parseInt(hm[0]);
                                    if(hm[1]) sourceOffsetHours += sign * (parseInt(hm[1])/60); }
                            } dateObjInUTC.setUTCHours(dateObjInUTC.getUTCHours() - sourceOffsetHours);
                        }
                        dateObjInUTC.setUTCHours(dateObjInUTC.getUTCHours() + 8);
                        const finalYear = dateObjInUTC.getUTCFullYear(); const finalMonth = (dateObjInUTC.getUTCMonth() + 1).toString().padStart(2, '0');
                        const finalDay = dateObjInUTC.getUTCDate().toString().padStart(2, '0');

                        if (isDateOnlyMatch) { finalStandardizedTextAfterTime += `${finalYear}-${finalMonth}-${finalDay}`;
                        } else {
                            const finalHours = dateObjInUTC.getUTCHours().toString().padStart(2, '0'); const finalMinutes = dateObjInUTC.getUTCMinutes().toString().padStart(2, '0');
                            finalStandardizedTextAfterTime += `${finalYear}-${finalMonth}-${finalDay} ${finalHours}:${finalMinutes} UTC+8`; }
                    }
                } catch (e) { finalStandardizedTextAfterTime += matchInfo.original; }
                lastProcessedTimeIndex = matchInfo.index + matchInfo.original.length;
            });
            finalStandardizedTextAfterTime += tempTimeProcessingText.substring(lastProcessedTimeIndex);
            standardizedText = finalStandardizedTextAfterTime;
            logDebug("JS标准化：时间处理后", {textPreview: standardizedText.substring(0,200)});


            // Restore AWS_KEYCODE placeholders before returning from standardizeEnglishChunk
            for (const placeholder in tempAwsCodeMap) {
                standardizedText = standardizedText.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), tempAwsCodeMap[placeholder]);
            }
            tempAwsCodeMap = {}; // Clear map

            for (const cliPlaceholder in tempCliCommandMap) {
                standardizedText = standardizedText.replace(new RegExp(cliPlaceholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), tempCliCommandMap[cliPlaceholder]);
            }
            tempCliCommandMap = {};

            // Restore temp ARN placeholders WITH their original, UNMODIFIED values -- REMOVED as ARNs are no longer temp replaced here

            for (const jsonPlaceholder in tempJsonBlockMap) { standardizedText = standardizedText.replace(jsonPlaceholder, tempJsonBlockMap[jsonPlaceholder]); } tempJsonBlockMap = {};
            for (const urlPlaceholder in tempUrlMap) {
                let originalUrl = tempUrlMap[urlPlaceholder]; let processedUrl = originalUrl;
                if (/^https?:\/\/amazonaws\.cn\/support\/?$/i.test(processedUrl)) { processedUrl = 'https://console.amazonaws.cn/support/'; }
                else if (/^https?:\/\/aws\.amazon\.com\/(support|contact-us)\/?$/i.test(processedUrl)) { processedUrl = 'https://console.amazonaws.cn/support/'; }
                else if (/^https?:\/\/aws\.amazon\.com\/ec2\/instance-types\/#Accelerated_Computing$/i.test(processedUrl)) { processedUrl = 'https://www.amazonaws.cn/ec2/instance-types/#Linux_Accelerated_Computing'; }
                else if (/^https?:\/\/docs\.aws\.amazon\.com/i.test(processedUrl)) { processedUrl = processedUrl.replace(/^https?:\/\/docs\.aws\.amazon\.com/i, 'https://docs.amazonaws.cn'); }
                else if (/^https?:\/\/([a-z0-9-]+\.)?console\.aws\.amazon\.com/i.test(processedUrl)) {
                     processedUrl = processedUrl.replace(/^https?:\/\/([a-z0-9-]+\.)?console\.aws\.amazon\.com(\/.*)?$/i, (match, subdomainGroup, pathAndQuery) => {
                        const subdomain = subdomainGroup ? subdomainGroup.slice(0, -1) : null;
                        if (subdomain) { return `https://${subdomain}.console.amazonaws.com.cn${pathAndQuery || ''}`; }
                        return `https://console.amazonaws.com.cn${pathAndQuery || ''}`; });
                }
                else if (/^https?:\/\/([^./]+)\.s3\.amazonaws\.com(?!\.cn)/i.test(processedUrl)) {  processedUrl = processedUrl.replace(/^https?:\/\/([^./]+)\.s3\.amazonaws\.com(?!\.cn)/i, (match, bucket) => `https://${bucket}.s3.cn-north-1.amazonaws.com.cn`); }
                else if (/^https?:\/\/([^./]+)\.([^./]+)\.aws\.amazon\.com(?!\.cn)/i.test(processedUrl)) {
                     processedUrl = processedUrl.replace(/^https?:\/\/([^./]+)\.([^./]+)\.aws\.amazon\.com(?!\.cn)/i, (match, sub1, sub2) => {
                        if (['s3', 'console', 'docs'].includes(sub2.toLowerCase())) return match;  return `https://${sub1}.${sub2}.amazonaws.com.cn`; });
                } else if (/^https?:\/\/([^./]+)\.aws\.amazon\.com(?!\.cn)/i.test(processedUrl)) {
                     processedUrl = processedUrl.replace(/^https?:\/\/([^./]+)\.aws\.amazon\.com(?!\.cn)/i, (match, sub) => {
                        if (['s3', 'console', 'docs'].includes(sub.toLowerCase())) return match;  return `https://${sub}.amazonaws.com.cn`; });
                } else if (/^https?:\/\/aws\.amazon\.com(?!(\.cn|\/(support|contact-us|ec2\/instance-types)))/i.test(processedUrl)) { processedUrl = processedUrl.replace(/^https?:\/\/aws\.amazon\.com/i, 'https://www.amazonaws.cn'); }
                if (processedUrl.includes('console.amazonaws.cn')) {
                    processedUrl = processedUrl.replace(/https:\/\/([a-z0-9-]+)\.console\.amazonaws\.cn(\S*)/gi, (match, subdomain, rest) => {
                        if (!subdomain.startsWith('cn-')) {  let newRest = rest.replace(/([\?&])region=[a-z0-9-]+&?/i, '$1').replace(/\?&/, '?').replace(/&$/, '').replace(/\?$/, '');
                            if (newRest === '?' || newRest === '&') newRest = ''; return `https://cn-north-1.console.amazonaws.cn${newRest}`;  }
                        return match;  });
                    processedUrl = processedUrl.replace(/(https:\/\/console\.amazonaws\.cn\S*[\?&]region=)([a-z0-9-]+)(\S*)/gi, (match, prefix, region, suffix) => {
                        if (!region.startsWith('cn-')) return `${prefix}cn-north-1${suffix}`;  return match; });
                }
                standardizedText = standardizedText.replace(urlPlaceholder, processedUrl);
            }
            tempUrlMap = {};
            for (const policyPlaceholder in tempPolicyMap) { standardizedText = standardizedText.replace(policyPlaceholder, tempPolicyMap[policyPlaceholder]); } tempPolicyMap = {};

            standardizedText = standardizedText.replace(/__TEMP_AMAZON_FOR_TRANSLATION__/g, 'Amazon');
            logDebug("JS标准化：最终输出", {text: standardizedText.substring(0,500)});
            return standardizedText;
        }

        function replaceTermsWithPlaceholders(text, currentGlobalServiceState) {
            logDebug("开始替换术语为占位符", { length: text.length, inputSample: text.substring(0,100) });
            placeholderMap = {};
            placeholderCounter = 0;

            let workingText = text;
            const termsToProtect = [];

            // STAGE 1: Identify and collect METADATA KEYS first
            const metaKeyPatterns = [
                { regex: /^(Service)\s*:/gmi, type: "METAKEY", originalForm: "Service:" },
                { regex: /^(Region)\s*:/gmi, type: "METAKEY", originalForm: "Region:" },
                { regex: /^(Failure mode(?: \d+)?)\s*:/gmi, type: "METAKEY", originalForm: null },
                { regex: /^(TypeCode)\s*:/gmi, type: "METAKEY", originalForm: "TypeCode:" },
                { regex: /^(Event Type Code)\s*:/gmi, type: "METAKEY", originalForm: "Event Type Code:" },
                { regex: /^(First Post Wording)\s*:/gmi, type: "METAKEY", originalForm: "First Post Wording:" },
                { regex: /^(Wording)\s*:/gmi, type: "METAKEY", originalForm: "Wording:" }
            ];

            metaKeyPatterns.forEach(patternDetail => {
                let match;
                patternDetail.regex.lastIndex = 0;
                while((match = patternDetail.regex.exec(workingText)) !== null) {
                    const fullMatchedString = match[0];
                    const keyPart = match[1];
                    termsToProtect.push({
                        text: fullMatchedString,
                        type: patternDetail.type,
                        originalIndex: match.index,
                        valueToStore: patternDetail.originalForm || (keyPart.trim() + ":")
                    });
                }
            });
            if (termsToProtect.filter(t => t.type === "METAKEY").length > 0) {
                logDebug("元数据键名已初步收集到 termsToProtect", { metaKeys: termsToProtect.filter(t=>t.type==="METAKEY").map(k => ({text: k.text, index: k.originalIndex, value: k.valueToStore})) });
            }

            // STAGE 2: Temporarily replace complex structures to simplify main term collection
            let textForMainTermCollection = workingText;

            let tempJsonMap = {}; let tempJsonIdx = 0;
            // MODIFIED Regex for JSON block in replaceTermsWithPlaceholders
            const jsonBlockGlobalRegex = /\{\s*"Version":[\s\S]*?\s*\n\s*\}(?=\s*($|\n))/g;
            let tempPartsForJson = [];
            let lastIndexProcessedForJson = 0;
            let matchJson;
            while ((matchJson = jsonBlockGlobalRegex.exec(textForMainTermCollection)) !== null) {
                tempPartsForJson.push(textForMainTermCollection.substring(lastIndexProcessedForJson, matchJson.index)); // Part before match
                const ph = `__TEMP_JSON_${tempJsonIdx++}__`;
                tempJsonMap[ph] = matchJson[0];
                tempPartsForJson.push(ph); // Placeholder
                lastIndexProcessedForJson = matchJson.index + matchJson[0].length;
            }
            tempPartsForJson.push(textForMainTermCollection.substring(lastIndexProcessedForJson)); // Part after the last match (or the whole string if no match)
            textForMainTermCollection = tempPartsForJson.join('');


            // ARN protection is now solely here.
            let tempArnMap = {}; let tempArnIdx = 0;
            const arnRegExpObject = new RegExp(robustArnRegexSourceForBoundary, 'g');
            logDebug("replaceTermsWithPlaceholders: ARN RegExp object", {source: arnRegExpObject.source, flags: arnRegExpObject.flags });

            textForMainTermCollection = textForMainTermCollection.replace(arnRegExpObject, (match) => {
                const ph = `__TEMP_ARN_${tempArnIdx++}__`;
                tempArnMap[ph] = match;
                logDebug("replaceTermsWithPlaceholders: ARN 匹配并暂存", { arn_match: match, placeholder: ph });
                return ph;
            });


            let tempCliMap = {}; let tempCliIdx = 0;
            textForMainTermCollection = textForMainTermCollection.replace(/(^\s*aws\s+[^\n]+)/gm, (match) => {
                const ph = `__TEMP_CLI_${tempCliIdx++}__`; tempCliMap[ph] = match; return ph;
            });

            let tempAwsCodeMap = {}; let tempAwsCodeIdx = 0;
            textForMainTermCollection = textForMainTermCollection.replace(/\bAWS_([A-Z0-9]+_)*[A-Z0-9]+\b/g, (match) => {
                const ph = `__TEMP_KEYCODE_${tempAwsCodeIdx++}__`; tempAwsCodeMap[ph] = match; return ph;
            });

            if (tempJsonIdx > 0 || tempArnIdx > 0 || tempCliIdx > 0 || tempAwsCodeIdx > 0) {
                logDebug("Temporary placeholders for JSON/ARN/CLI/KEYCODE inserted into textForMainTermCollection", {textPreview: textForMainTermCollection.substring(0,200), arnMatches: tempArnIdx, jsonMatches: tempJsonIdx});
            }

            // STAGE 3: Collect all other standard terms from `textForMainTermCollection`
            let matchCollector;
            const placeholderRegex = /\{\{.*?\}\}/g;
            placeholderRegex.lastIndex = 0;
            while((matchCollector = placeholderRegex.exec(textForMainTermCollection)) !== null) { termsToProtect.push({text: matchCollector[0], type: "MTPLHDR", originalIndex: matchCollector.index});}

            const docRefRegex = /\[(?:[a-zA-Z0-9_.-]+(?:#[a-zA-Z0-9_-]+)?|[0-9]+)\](?!\()/g;
            docRefRegex.lastIndex = 0;
            while((matchCollector = docRefRegex.exec(textForMainTermCollection)) !== null) { termsToProtect.push({text: matchCollector[0], type: "DOCREF", originalIndex: matchCollector.index});}

            const directProtectTerms = [
                { regex: /\bAmazon\s+Linux\s+2023\b/ig, type: "PRDNM" }, { regex: /\bAL2023\b/ig, type: "PRDNM" },
                { regex: /\bNode\.js\s+\d{1,2}(?:\s+AL2023)?\b/ig, type: "TECHTERM"},
                { regex: /\bService\s+Quotas\b/ig, type: "PRDNM" },
                { regex: /\bOpen\s+ID\s+Connect\s*\(OIDC\)/ig, type: "TECHTERM" },
            ];
            directProtectTerms.forEach(termDef => {
                termDef.regex.lastIndex = 0;
                let match;
                while((match = termDef.regex.exec(textForMainTermCollection)) !== null) {
                    termsToProtect.push({text: match[0], type: termDef.type, originalIndex: match.index});
                }
            });

            const allKnownServiceFormsFromState = new Set();
            if (currentGlobalServiceState) { Object.values(currentGlobalServiceState).forEach(s => { if(s.fullName) allKnownServiceFormsFromState.add(s.fullName); if(s.shortName) allKnownServiceFormsFromState.add(s.shortName); });}
            servicePatterns.forEach(p => {
                if(p.fullName) allKnownServiceFormsFromState.add(p.fullName);
                if(p.shortName && p.shortName !== p.fullName) allKnownServiceFormsFromState.add(p.shortName);
                if(p.baseName && p.baseName.includes(" (") && p.baseName.split(" (")[0] !== p.fullName) allKnownServiceFormsFromState.add(p.baseName.split(" (")[0]);
                 // Add service name + common general suffixes if not already covered by specific regexes in servicePatterns
                if (p.baseName === 'Amazon Elastic Compute Cloud') { // Example for EC2
                    ['instance', 'instances', 'instance family', 'instance type'].forEach(suffix => {
                        allKnownServiceFormsFromState.add(p.fullName + ' ' + suffix);
                        allKnownServiceFormsFromState.add(p.shortName + ' ' + suffix);
                    });
                }
            });
            Array.from(allKnownServiceFormsFromState).sort((a,b) => b.length - a.length).forEach(serviceForm => {
                if (!serviceForm || serviceForm.trim() === "") return;
                const escapedForm = serviceForm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                const regex = new RegExp(`(?<![\\w-])${escapedForm}(?![\\w-])`, 'g');
                let match;
                while((match = regex.exec(textForMainTermCollection)) !== null) {
                    termsToProtect.push({text: match[0], type: "SRVCNM", originalIndex: match.index});
                }
            });

            const acronyms = ['CLI', 'API', 'SDK', 'ENI', 'AZ', 'SCPs', 'DMS', 'SSM', 'IMDSv2', 'BaselineId', 'InstanceId', 'AMI', 'CRI', 'SPs', 'RI', 'Nitro System', 'Launch Template',
                             'rds:db-tag', 'rds:cluster-tag', 'rds:snapshot-tag', 'rds:cluster-pg-tag', 'rds:cluster-snapshot-tag', 'rds:es-tag', 'rds:og-tag', 'rds:pg-tag',
                             'rds:ri-tag', 'rds:subgrp-tag', 'rds:secgrp-tag', 'OIDC', 'SAM', 'ELB' ]; // Added ELB to general acronyms
            acronyms.forEach(acronym => {
                if (acronym === 'SAM' && allKnownServiceFormsFromState.has('Amazon SAM')) return;
                if (acronym === 'ELB' && allKnownServiceFormsFromState.has('Elastic Load Balancing (ELB)')) return; // Avoid double protection if already handled as SRVCNM

                const escapedAcronym = acronym.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                const regex = new RegExp(`(?<!arn:(?:aws|aws-cn):${escapedAcronym}:.*)(?<![\\w-])${escapedAcronym}(?![\\w-])`, 'g');
                let match;
                while((match = regex.exec(textForMainTermCollection)) !== null) {
                    termsToProtect.push({text: match[0], type: "ACRNYM", originalIndex: match.index});
                }
            });

            const timeDateRegex = /\b\d{4}-\d{2}-\d{2}(?:\s+\d{2}:\d{2}\s+UTC\+8)?\b/g;
            timeDateRegex.lastIndex = 0;
            while((matchCollector = timeDateRegex.exec(textForMainTermCollection)) !== null) { termsToProtect.push({text: matchCollector[0], type: "TIMESTAMP", originalIndex: matchCollector.index}); }

            const urlRegex =  /https?:\/\/[^\s"';{}]*?[^\s"';{},.()\[\]]/g;
            urlRegex.lastIndex = 0;
            while((matchCollector = urlRegex.exec(textForMainTermCollection)) !== null) { termsToProtect.push({text: matchCollector[0], type: "URL", originalIndex: matchCollector.index}); }

            const policyStringRegex = /'([a-zA-Z0-9_-]+:[A-Za-z0-9*_-]+)'/g;
            policyStringRegex.lastIndex = 0;
            while((matchCollector = policyStringRegex.exec(textForMainTermCollection)) !== null) { termsToProtect.push({text: matchCollector[0], type: "POLICY", originalIndex: matchCollector.index}); }

            // --- STAGE 4: Sort all collected terms and build final text with placeholders ---
            termsToProtect.sort((a,b) => {
                if (a.originalIndex !== b.originalIndex) return a.originalIndex - b.originalIndex;
                return b.text.length - a.text.length;
            });

            const uniqueTermsToProtect = [];
            let lastTermEndIndex = -1;
            for (const term of termsToProtect) {
                if (term.originalIndex >= lastTermEndIndex) {
                    // Check if this term is inside one of the temp placeholders (JSON, ARN etc.)
                    // This is a simplified check; more robust would be to check against all temp placeholder ranges.
                    // For now, we assume `textForMainTermCollection` is the base for indices.
                    uniqueTermsToProtect.push(term);
                    lastTermEndIndex = term.originalIndex + term.text.length;
                }
            }
            logDebug("uniqueTermsToProtect (includes METAKEYs and others, pre-temp-restore):", JSON.parse(JSON.stringify(uniqueTermsToProtect.map(t => ({text: t.text.substring(0,50) + (t.text.length > 50 ? "..." : ""), type: t.type, index: t.originalIndex, length: t.text.length})))));

            let resultText = "";
            let currentBuildPositionInWorkingText = 0;
            uniqueTermsToProtect.forEach(termInfo => {
                resultText += textForMainTermCollection.substring(currentBuildPositionInWorkingText, termInfo.originalIndex);

                const valueForMap = termInfo.type === "METAKEY" ? termInfo.valueToStore : termInfo.text;
                const placeholder = `__${termInfo.type}_${placeholderCounter++}__`;
                placeholderMap[placeholder] = valueForMap;
                resultText += placeholder;
                currentBuildPositionInWorkingText = termInfo.originalIndex + termInfo.text.length;
            });
            resultText += textForMainTermCollection.substring(currentBuildPositionInWorkingText);

            // --- STAGE 5: Restore temporary placeholders into the main placeholderMap and update resultText ---
            function replaceTempPlaceholders(tempMap, finalTypePrefix, currentResultText, currentPlaceholderMap, currentPlaceholderCounter) {
                let updatedText = currentResultText;
                for (const tempPh in tempMap) {
                    const finalPh = `__${finalTypePrefix}_${currentPlaceholderCounter++}__`;
                    currentPlaceholderMap[finalPh] = tempMap[tempPh];
                    updatedText = updatedText.replace(new RegExp(tempPh.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), finalPh);
                }
                return { text: updatedText, counter: currentPlaceholderCounter };
            }

            let updateResult;
            updateResult = replaceTempPlaceholders(tempJsonMap, "JSONBLOCK", resultText, placeholderMap, placeholderCounter);
            resultText = updateResult.text; placeholderCounter = updateResult.counter;

            updateResult = replaceTempPlaceholders(tempArnMap, "ARNSTR", resultText, placeholderMap, placeholderCounter);
            resultText = updateResult.text; placeholderCounter = updateResult.counter;

            updateResult = replaceTempPlaceholders(tempCliMap, "CLICMD", resultText, placeholderMap, placeholderCounter);
            resultText = updateResult.text; placeholderCounter = updateResult.counter;

            updateResult = replaceTempPlaceholders(tempAwsCodeMap, "KEYCODE", resultText, placeholderMap, placeholderCounter);
            resultText = updateResult.text; placeholderCounter = updateResult.counter;

            logDebug("术语替换为占位符完成 (final resultText)", { mapSize: Object.keys(placeholderMap).length, exampleText: resultText.substring(0, 200) });
            return resultText;
        }

        function restorePlaceholdersAndFinalizeChinese(textWithPlaceholders) {
            logDebug("开始从占位符恢复术语并完成中文格式化", {
                inputTextLength: textWithPlaceholders.length,
            });

             logDebug("Placeholder Map at START of restorePlaceholdersAndFinalizeChinese (first 20):", JSON.parse(JSON.stringify(Object.fromEntries(Object.entries(placeholderMap).slice(0, 20)))));
             if (Object.keys(placeholderMap).length > 20 || Object.keys(placeholderMap).some(k => k.startsWith("__METAKEY_"))) {
                logDebug("Placeholder Map at START (METAKEYs only):", JSON.parse(JSON.stringify(Object.fromEntries(Object.entries(placeholderMap).filter(([k]) => k.startsWith("__METAKEY_"))))));
             }


            let finalChineseText = textWithPlaceholders;
            const sortedPlaceholders = Object.keys(placeholderMap).sort((a,b) => b.length - a.length);

            sortedPlaceholders.forEach(placeholder => {
                let originalTermFromMap = placeholderMap[placeholder];
                const placeholderRegex = new RegExp(placeholder.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'), 'g');

                let termForReplacement = originalTermFromMap;

                if (placeholder.startsWith('__TIMESTAMP_')) {
                    const dateOnlyMatch = termForReplacement.match(/^(\d{4}-\d{2}-\d{2})$/);
                    const dateTimeMatch = termForReplacement.match(/^(\d{4}-\d{2}-\d{2})\s+(\d{2}):(\d{2})\s+UTC\+8$/);
                    if (dateTimeMatch) {
                        const [, datePart, hourStr, minuteStr] = dateTimeMatch; const [year, monthNum, dayNum] = datePart.split('-');
                        let hour = parseInt(hourStr); let period = "上午";
                        if (hour === 0) { period = "上午"; hour = 12; }  else if (hour === 12) { period = "下午"; } else if (hour > 12 && hour < 24) { period = "下午"; hour -= 12; }
                        termForReplacement = `${year}年${parseInt(monthNum)}月${parseInt(dayNum)}日 ${period}${hour}:${minuteStr} 北京时间`;
                    } else if (dateOnlyMatch) { const [year, monthNum, dayNum] = dateOnlyMatch[1].split('-'); termForReplacement = `${year}年${parseInt(monthNum)}月${parseInt(dayNum)}日`; }
                }
                // METAKEYs are restored to their original form which includes the colon, e.g. "Service:"
                // No special handling needed here beyond direct replacement as originalTermFromMap is correct.

                const escapedTermForReplacement = termForReplacement.replace(/\$/g, '$$$$');
                finalChineseText = finalChineseText.replace(placeholderRegex, escapedTermForReplacement);
            });

            finalChineseText = finalChineseText.replace(/Amazon Web Services Support/g, '亚马逊云科技中国支持团队');
            finalChineseText = finalChineseText.replace(/Amazon Web Services Console/g, '亚马逊云科技控制台');
            finalChineseText = finalChineseText.replace(/Amazon Web Services Account/g, '亚马逊云科技账户');
            finalChineseText = finalChineseText.replace(/\bAmazon Web Services\b/gi, '亚马逊云科技');

            let standaloneAmazonExclusionTerms = [];
            servicePatterns.forEach(patternInfo => {
                const addTerm = (term) => {
                    if (term && term.trim().length > 0) {
                        // Ensure term is not just "Amazon" itself or very short, generic words unless specifically intended
                        if (term.toLowerCase() !== "amazon" && term.length > 1) { // Added length check for robustness
                            standaloneAmazonExclusionTerms.push(term.trim());
                        }
                    }
                };

                if (patternInfo.fullName) {
                    const fnParts = patternInfo.fullName.split(' ');
                    // Get base name without parenthesized acronym, e.g., "Elastic Load Balancing" from "Elastic Load Balancing (ELB)"
                    const baseFullName = patternInfo.fullName.replace(/\s*\(.*?\)\s*$/, '').trim(); 

                    if (fnParts[0].toLowerCase() === 'amazon' && fnParts.length > 1) {
                        // Add the part after "Amazon", e.g., "Elastic Compute Cloud"
                        addTerm(baseFullName.substring(fnParts[0].length).trim()); 
                    } else if (fnParts[0].toLowerCase() !== 'amazon') {
                        // Add the full base name if it doesn't start with "Amazon", e.g., "Elastic Load Balancing"
                        addTerm(baseFullName); 
                    }
                }

                if (patternInfo.shortName) {
                    const snParts = patternInfo.shortName.split(' ');
                    const shortNameTrimmed = patternInfo.shortName.trim();

                    // Add short name if it's a single word (like "Aurora", "ELB", "S3")
                    if (snParts.length === 1 && /^[A-Za-z0-9][A-Za-z0-9-]*[A-Za-z0-9]$/.test(snParts[0]) && snParts[0].length >=2 ) {
                         addTerm(snParts[0]); 
                    } else if (snParts.length > 1 && snParts[0].toLowerCase() === 'amazon') {
                         // If short name is "Amazon <Something>", add "<Something>"
                         addTerm(shortNameTrimmed.substring(snParts[0].length).trim());
                    } else if (snParts.length > 1 && snParts[0].toLowerCase() !== 'amazon') {
                         // If short name is multi-word and not Amazon-prefixed (e.g., "Elastic Beanstalk", "CloudWatch Events")
                         addTerm(shortNameTrimmed);
                    }
                    // Ensure single-word non-Amazon short names are also added if not caught above
                    else if (snParts.length === 1 && shortNameTrimmed.toLowerCase() !== 'amazon' && shortNameTrimmed.length >=2) {
                        addTerm(shortNameTrimmed);
                    }
                }
            });
            standaloneAmazonExclusionTerms.push(
                "Web Services", "Linux", "Machine Image", "Kindle", "Prime", "Echo", "Alexa", "Go", "Music", "Photos", "Drive", "Fire", "Appstore", "Pay", "Fresh", "Flex", "re:Post",
                "Serverless Application Model"
            );


            standaloneAmazonExclusionTerms = [...new Set(standaloneAmazonExclusionTerms)]
                .filter(term => term && term.trim().length > 0) // Final filter for safety
                .sort((a, b) => b.length - a.length);

            if (standaloneAmazonExclusionTerms.length > 0) {
                const exclusionPatternString = standaloneAmazonExclusionTerms.map(term =>
                    term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // Escape special characters for regex
                ).join('|');

                // Regex: \bAmazon\b followed by a space, then NOT one of the exclusion terms (also followed by a word boundary or end of string)
                // This is to prevent "Amazon" in "Amazon S3" being translated, but allow "Amazon" in "Amazon provides services"
                const amazonStandaloneRegex = new RegExp(`\\bAmazon\\b(?!\\s+(?:${exclusionPatternString}))(?![A-Za-z0-9-])`, 'gi');

                finalChineseText = finalChineseText.replace(amazonStandaloneRegex, (match, offset, string) => {
                    // Additional check: ensure the "Amazon" is not part of a known full service name pattern that might have slipped through.
                    // This is a safeguard. The main protection should come from `exclusionPatternString`.
                    const textAfterAmazon = string.substring(offset + match.length);
                    for (const sp of servicePatterns) {
                        if (sp.fullName.startsWith("Amazon ") && textAfterAmazon.startsWith(sp.fullName.substring("Amazon ".length))) {
                            return match; // Don't replace, it's part of a full service name.
                        }
                         if (sp.shortName.startsWith("Amazon ") && textAfterAmazon.startsWith(sp.shortName.substring("Amazon ".length))) {
                            return match; // Don't replace, it's part of a short service name.
                        }
                    }
                    // Check if "Amazon" is followed by an uppercase word likely indicating a product/service not in the exclusion list yet.
                    if (/^\s[A-Z][A-Za-z0-9-]/.test(textAfterAmazon)) { // e.g. "Amazon Connect", "Amazon WorkMail"
                        // A more refined check could be to see if the following word is a known product name initial.
                        // For now, if it looks like "Amazon Product", lean towards not translating "Amazon" standalone.
                        // This needs careful tuning to avoid not translating genuinely standalone "Amazon".
                        // For now, let the main regex with exclusion list handle it.
                        // If it's not in exclusion list, it will be translated.
                    }
                    return '亚马逊云科技';
                });

                logDebug("独立 Amazon 替换", {
                    regexSource: amazonStandaloneRegex.source,
                    textPreviewAfter: finalChineseText.substring(0, 300)
                });
            } else { // Fallback if no exclusion terms (should not happen with the hardcoded ones)
                finalChineseText = finalChineseText.replace(/\bAmazon\b(?! [A-Z0-9])/g, '亚马逊云科技');
                 logDebug("独立 Amazon 替换 (回退逻辑)", {
                    textPreviewAfter: finalChineseText.substring(0, 300)
                });
            }

            finalChineseText = finalChineseText.replace(/亚马逊\s+(Amazon\s+CLI)/g, '$1');
            finalChineseText = finalChineseText.replace(/亚马逊\s+(Amazon\s+Command\s+Line\s+Interface\s*\(Amazon\s+CLI\))/g, '$1');


            const simplerSupportSentenceRegex = /(如果您有任何问题或者顾虑，请联系亚马逊云科技中国支持团队)(?:[^\[]*?)(\[\d+\])。/gi;
            finalChineseText = finalChineseText.replace(simplerSupportSentenceRegex, (match, p1Prefix, p2LinkRef) => { return `${p1Prefix} ${p2LinkRef}。`; });
            logDebug("占位符恢复和中文格式化完成", { finalTextLength: finalChineseText.length, finalTextPreview: finalChineseText.substring(0, 200) });
            return finalChineseText;
        }

        const llmTranslationPrompt = `## 中文本地化任务 (保持占位符)
**核心目标**：将以下英文文本翻译为符合亚马逊云科技官方风格和术语规范的简体中文。
**关键规则 (ABSOLUTE HIGHEST PRIORITY - NEVER VIOLATE)**：
1.  文本中包含特殊占位符，格式为 \`__TERM_PROTECTED_XXX__\`, \`__SRVCNM_XXX__\`, \`__ACRNYM_XXX__\`, \`__TIMESTAMP_XXX__\`, \`__URL_XXX__\`, \`__MTPLHDR_XXX__\`, \`__DOCREF_XXX__\`, \`__POLICY_XXX__\`, \`__PRDNM_XXX__\`, \`__TECHTERM_XXX__\`, \`__JSONBLOCK_XXX__\`, \`__ARNSTR_XXX__\`, \`__CLICMD_XXX__\`, \`__KEYCODE_XXX__\`, \`__METAKEY_XXX__\` (其中XXX是数字或特定键名)。
2.  **[占位符处理 - 最高指令] 您必须原封不动地保留所有这些占位符及其完整形式。绝对禁止以任何形式翻译、修改、删除或添加任何占位符。** 它们代表了不应被翻译的英文术语或特殊格式。特别是 \`__JSONBLOCK_XXX__\`, \`__ARNSTR_XXX__\`, \`__CLICMD_XXX__\`, \`__KEYCODE_XXX__\` 和 \`__METAKEY_XXX__\` 代表代码/ARN/CLI块、特殊代码或元数据键名，它们的内容（包括所有字符如引号、星号、斜杠、参数、双破折号、冒号等）**必须被精确地、逐字逐句地保留**，就如同它们是不可修改的图像一样。只需原样输出这些占位符。确保占位符与其周围的文本（无论是英文原文还是中文译文）之间保持适当的空格（如果原文中有空格）。
3.  **[翻译核心 - 最高指令] 务必、必须、一定要翻译所有占位符之外的、且确实是英文自然语言表述的文本。任何非占位符的英文自然语言叙述部分，无论它出现在哪里（即使在多个占位符之间），无论多短，都必须无一例外地翻译成中文。不允许遗漏任何应翻译的英文片段。**
4.  翻译应流畅、准确，符合专业技术文档风格。
5.  使用简体中文和中文标点符号。
6.  保持原始文本的段落和行结构。**确保输出的行数和换行符位置与输入文本中的占位符和被翻译文本段落结构一致。不要合并或拆分原始输入中的段落。**
7.  **最终输出仅包含翻译后的中文文本和未经修改的占位符。不包含任何解释、注释或英文原文。**
8.  **针对包含 "Amazon Web Services Support" 的句子 (特别是当它后面跟着多个联系方式的引用时):** 如果遇到类似 "If you have any questions or concerns, the Amazon Web Services Support is available on Product X [1] and via Support Center [2]." 这样的句子，请将其翻译并重写为更简洁的："如果您有任何问题或者疑虑，请联系 Amazon Web Services Support [2]。" (注意：保留Support Center的引用，移除其他产品引用。Amazon Web Services Support稍后会被代码替换为中文)。
9.  **对于方括号 \`[...]\` 包围的内容**：如果方括号内不是一个被 \`__DOCREF_XXX__\` 占位符保护的引用标记，而是普通英文文本，则**必须翻译方括号内的文本**，并在翻译结果两侧保留中文全角方括号 \`【...】\` (如果原文方括号表示强调或注释) 或直接融入句子 (如果原文方括号是语法结构的一部分)。
10. **"Amazon"的翻译**: 只有当 "Amazon" 作为独立词汇出现，并且不是任何服务名称 (如__SRVCNM_XXX__) 或产品名称 (__PRDNM_XXX__) 的一部分时，才应将其翻译为 "亚马逊云科技"。特别注意 "Amazon Serverless Application Model (SAM)" 中的 "Amazon" 是服务名的一部分，不应单独翻译。如果上下文中 "Amazon" 后面紧跟一个服务名称占位符 (如 "Amazon __SRVCNM_XXX__")，则此 "Amazon" 通常不应翻译为 "亚马逊云科技"，因为它很可能是服务名的一部分。
11. **通用技术术语翻译**: 诸如 "Fast Launch" 之类的通用技术词汇，如果未被占位符保护，应翻译成中文 (例如 "Fast Launch" -> "快速启动")。
12. **[强制翻译规则 - 最高优先级 - 再次强调] 针对代码块/ARN块之间或附近的描述性文本**：如果一段由英文自然语言构成的描述性句子或段落（其内部可能也包含其他类型的占位符，如 \`__TIMESTAMP_XXX__\` 或 \`__SRVCNM_XXX__\`）出现在主要占位符（如 \`__JSONBLOCK_XXX__\` 或 \`__ARNSTR_XXX__\` 或 \`__CLICMD_XXX__\` 或 \`__KEYCODE_XXX__\` 或 \`__METAKEY_XXX__\`）之间，或者紧接在这些主要占位符之前/之后形成一个段落，那么这段英文描述性文本本身**绝对必须、无一例外地被翻译**成简体中文。**严禁因为其上下文邻近代码或ARN占位符而将其视为代码注释或技术说明的一部分并保留英文。此规则是强制性的，必须严格遵守，忽略此规则将导致翻译不完整。每一个非占位符的英文单词都应被视为翻译对象，除非它是某个更大占位符的一部分。**
    **示例**：
    输入: \`__JSONBLOCK_0__\n\nThis English sentence describes the JSON block above and mentions __SRVCNM_1__ effective on __TIMESTAMP_2__.\n\n__CLICMD_3__\`
    期望这部分翻译为: \`__JSONBLOCK_0__\n\n此英文句子描述了上面的 JSON 块，并提及了自 __TIMESTAMP_2__ 起生效的 __SRVCNM_1__。\n\n__CLICMD_3__\`
13. **[强制翻译规则 - 最高优先级 - 再次强调] 针对自身包含占位符的描述性句子**：如果一个英文自然语言句子中既包含需要翻译的英文文本，也包含一个或多个其他类型的占位符（例如 \`To continue with the same behavior after __TIMESTAMP_XXX__, we recommend...\`），则**必须翻译**句子中所有非占位符的英文部分。**内部占位符的存在绝不能成为不翻译该句子其余英文部分的理由。所有非占位符的自然语言片段都必须翻译。再次强调，每一个独立的英文单词或短语，如果它本身不是一个占位符，就必须被翻译。**
**示例：**
输入: \`__METAKEY_SERVICE_KEY_0__ DYNAMODB\n__METAKEY_REGION_KEY_1__  BJS | ZHY   \n__METAKEY_FAILURE_MODE_KEY_2__\n__METAKEY_TYPECODE_KEY_3__ __KEYCODE_4__\n__METAKEY_WORDING_KEY_5__ We are investigating increased __ACRNYM_0__ error rates for __SRVCNM_1__ Access Control APIs in the __MTPLHDR_2__ Region.\`
期望输出: \`__METAKEY_SERVICE_KEY_0__ DYNAMODB\n__METAKEY_REGION_KEY_1__  BJS | ZHY   \n__METAKEY_FAILURE_MODE_KEY_2__\n__METAKEY_TYPECODE_KEY_3__ __KEYCODE_4__\n__METAKEY_WORDING_KEY_5__ 我们正在调查 __MTPLHDR_2__ 区域中 __SRVCNM_1__ 访问控制 API 的 __ACRNYM_0__ 错误率上升问题。\`
`;

        async function sendMessage() {
            const messageText = userInput.value.trim(); if (messageText === '') return;
            // console.log("用户输入:", messageText); // Optional: log raw user input
            appendMessage(messageText, 'user'); userInput.value = ''; adjustTextareaHeight(userInput);
            const overallLoadingDiv = createLoadingMessage('AI 正在处理...');
            try {
                updateLoadingMessage(overallLoadingDiv, '步骤 1/3: 正在通过JS标准化英文术语 (分行处理)...');
                const standardizedEnglish = standardizeTextByLines(messageText);
                logDebug("JS标准化后的英文 (分行处理后)", { text: standardizedEnglish });

                console.log("[直接打印 PRE-APPEND] JS 英文标准化结果 RAW:", standardizedEnglish);
                const displayStandardizedEnglish = `【JS 英文标准化结果】:\n${standardizedEnglish}`;
                console.log("[直接打印 PRE-APPEND] JS 英文标准化结果 FOR DISPLAY:", displayStandardizedEnglish);

                if (debugMode) { appendMessage(displayStandardizedEnglish, 'ai-message'); }

                updateLoadingMessage(overallLoadingDiv, '步骤 2/3: 正在准备翻译（生成占位符）...');
                const textWithPlaceholders = replaceTermsWithPlaceholders(standardizedEnglish, window.finalGlobalServiceMentionState);
                logDebug("带占位符的待翻译文本", { text: textWithPlaceholders });

                console.log("[直接打印 PRE-APPEND] 带占位符的待翻译文本 RAW:", textWithPlaceholders);
                const displayPlaceholderText = `【带占位符的待翻译文本】:\n${textWithPlaceholders}`;
                console.log("[直接打印 PRE-APPEND] 带占位符的待翻译文本 FOR DISPLAY:", displayPlaceholderText);

                 if (debugMode) { appendMessage(displayPlaceholderText, 'ai-message'); }

                updateLoadingMessage(overallLoadingDiv, '步骤 3/3: 正在调用LLM进行翻译...');
                const history = [{ role: "user", content: combinePromptAndContent(llmTranslationPrompt, textWithPlaceholders) }];
                const translatedTextWithPlaceholders = await callLLMApi(history, "核心翻译");
                logDebug("LLM翻译结果（带占位符）", { text: translatedTextWithPlaceholders });

                console.log("[直接打印 PRE-APPEND] LLM翻译结果（带占位符） RAW:", translatedTextWithPlaceholders);
                const displayLLMResult = `【LLM翻译结果（带占位符）】:\n${translatedTextWithPlaceholders}`;
                console.log("[直接打印 PRE-APPEND] LLM翻译结果（带占位符） FOR DISPLAY:", displayLLMResult);

                if (debugMode) { appendMessage(displayLLMResult, 'ai-message'); }

                updateLoadingMessage(overallLoadingDiv, 'AI 正在整合最终结果...');
                let finalChineseOutput = restorePlaceholdersAndFinalizeChinese(translatedTextWithPlaceholders);
                logDebug("最终中文输出", { text: finalChineseOutput });

                console.log("[直接打印 PRE-APPEND] 最终翻译结果 RAW:", finalChineseOutput);
                const displayFinalOutput = `【最终翻译结果】:\n${finalChineseOutput}`;
                console.log("[直接打印 PRE-APPEND] 最终翻译结果 FOR DISPLAY:", displayFinalOutput);

                removeLoadingMessage(overallLoadingDiv);
                appendMessage(displayFinalOutput, 'ai-message');

                const quality = checkTranslationQuality_Optimized(standardizedEnglish, finalChineseOutput, placeholderMap);
                if (quality.hasIssues) {
                    logDebug("翻译质量检查问题", { issues: quality.issues });
                    if (debugMode) {
                        const displayQualityIssues = `【质量检查警告】:\n- ${quality.issues.join('\n- ')}`;
                        console.log("[直接打印 PRE-APPEND] 质量检查警告 FOR DISPLAY:", displayQualityIssues);
                        appendMessage(displayQualityIssues, 'error-message');
                    }
                } else {
                    logDebug("翻译质量检查通过", {});
                }
            } catch (error) {
                removeLoadingMessage(overallLoadingDiv);
                if (!String(error.message).includes("API 请求失败")) { handleError(error, "sendMessage processing"); }
            }
        }

        function checkTranslationQuality_Optimized(standardizedEng, finalChinese, currentPlaceholderMap) {
            const issues = [];
            for (const pholder in currentPlaceholderMap) {
                if (finalChinese.includes(pholder)) {
                    if (pholder.startsWith("__ARNSTR_") || pholder.startsWith("__JSONBLOCK_") || pholder.startsWith("__CLICMD_") || pholder.startsWith("__KEYCODE_")) {
                        issues.push(`关键占位符 "${pholder}" (代表 "${currentPlaceholderMap[pholder].substring(0,50)}...") 未被正确替换。`);
                    } else if (pholder.startsWith("__METAKEY_")) {
                        issues.push(`元数据键占位符 "${pholder}" (代表 "${currentPlaceholderMap[pholder]}") 未被正确还原。`);
                    }
                     else if (!currentPlaceholderMap[pholder].startsWith("arn:")){ // Avoid flagging ARNs that might still be placeholders if logic changes
                        issues.push(`占位符 "${pholder}" (代表 "${currentPlaceholderMap[pholder].substring(0,50)}...") 未被正确替换。`);
                    }
                }
            }

            const metaKeyValuesFromMap = Object.entries(currentPlaceholderMap)
                                            .filter(([key, _]) => key.startsWith("__METAKEY_"))
                                            .map(([_, val]) => val.trim());

            metaKeyValuesFromMap.forEach(originalMetaKey => {
                if (standardizedEng.includes(originalMetaKey) && !finalChinese.includes(originalMetaKey)) {
                    issues.push(`元数据键 "${originalMetaKey}" 在最终输出中丢失或被翻译。`);
                }
            });


            if (standardizedEng.includes('Amazon Web Services Support') && !finalChinese.includes('亚马逊云科技中国支持团队')) { issues.push('Amazon Web Services Support 未按预期翻译为 "亚马逊云科技中国支持团队"。');}
            if (standardizedEng.includes('Amazon Web Services Console') && !finalChinese.includes('亚马逊云科技控制台')) { issues.push('Amazon Web Services Console 未按预期翻译为 "亚马逊云科技控制台"。');}
            if (standardizedEng.match(/\bAmazon Web Services\b/i) && !standardizedEng.match(/\bAmazon Web Services (Support|Console|Account)\b/i) && !finalChinese.match(/\b亚马逊云科技\b/)) {
                if (!standardizedEng.match(/Amazon Web Services (documentation|documentation|docs)/i)) { issues.push('独立的 "Amazon Web Services" 未按预期翻译为 "亚马逊云科技"。');}}
            if (finalChinese.includes("https://console.Amazon.amazon.com/servicequotas/home")) { issues.push("链接 https://console.aws.amazon.com/servicequotas/home 域名替换错误 (aws->Amazon)。");
            } else if (standardizedEng.includes("console.aws.amazon.com/servicequotas/home") && !finalChinese.includes("https://console.amazonaws.cn/servicequotas/home")){ issues.push("链接 https://console.aws.amazon.com/servicequotas/home 未正确替换为 .cn 版本。");}
            if (finalChinese.includes("https://Amazon.amazon.com/support")) { issues.push("链接 https://aws.amazon.com/support 域名替换错误 (aws->Amazon)。");
            } else if (standardizedEng.includes("aws.amazon.com/support") && !finalChinese.includes("https://console.amazonaws.cn/support/")){ issues.push("链接 https://aws.amazon.com/support 未正确替换为 console.amazonaws.cn/support/。");}
            else if (standardizedEng.includes("amazonaws.cn/support") && !finalChinese.includes("https://console.amazonaws.cn/support/")){ issues.push("链接 https://amazonaws.cn/support 未正确替换为 console.amazonaws.cn/support/。");}
            if (finalChinese.includes("https://aws.amazon.com/Amazon EC2/instance-types/#Accelerated_Computing") || finalChinese.includes("https://www.amazonaws.cn/Amazon EC2/instance-types/")) {
                issues.push("链接 [1] (EC2 instance types) 包含错误的 'Amazon EC2' 插入或未正确转换为#Linux_Accelerated_Computing。");
            } else if (standardizedEng.includes("aws.amazon.com/ec2/instance-types/#Accelerated_Computing") && !finalChinese.includes("https://www.amazonaws.cn/ec2/instance-types/#Linux_Accelerated_Computing")) {
                issues.push("链接 [1] (EC2 instance types) 未正确转换为预期的 .cn 和 #Linux... 版本。");}
            if (/\[Amazon Health Dashboard may periodically trigger/.test(finalChinese) && standardizedEng.includes("[AWS Health may periodically trigger")){ issues.push("方括号内的 'AWS Health may periodically trigger...' 未被翻译。");}
            if (finalChinese.includes("Amazon RDSPostgreSQL") && (standardizedEng.includes("RDS for PostgreSQL") || standardizedEng.includes("Amazon RDS for PostgreSQL"))) { issues.push("Amazon RDS for PostgreSQL 名称被错误地合并或未正确保留 ' for '。");}
            if (standardizedEng.includes("12:01 AM PDT")) {
                 const pdtTimeInStandardEng = "15:01 UTC+8"; const expectedChineseTime = "下午3:01 北京时间";
                 if (standardizedEng.includes(pdtTimeInStandardEng)) {
                     if (!finalChinese.includes(expectedChineseTime) && !(finalChinese.includes("北京时间") && finalChinese.includes("下午3:01"))) {
                        issues.push(`PDT 时间 (12:01 AM PDT -> ${pdtTimeInStandardEng}) 预期中文为 "${expectedChineseTime}", 实际输出不符。当前输出中时间部分: ${JSON.stringify(finalChinese.match(/北京时间.*?(上午|下午|凌晨|中午)\d{1,2}:\d{2}/g))}`); }
                 } else { issues.push(`PDT 时间 (例如 12:01 AM PDT) JS标准化阶段可能出错，未得到预期的 "${pdtTimeInStandardEng}".`); }
            }
            if (standardizedEng.includes("AWS Nitro System") && !finalChinese.includes("Amazon Nitro 系统") && !finalChinese.includes("Amazon Nitro System")) { issues.push("'AWS Nitro System' 未正确处理为 'Amazon Nitro System' 或 'Amazon Nitro 系统'.");}
            if (finalChinese.includes("您的 AWS 账户团队或 亚马逊云科技中国支持团队") || finalChinese.includes("您的Amazon Web Services账户团队或 亚马逊云科技中国支持团队")) { issues.push("支持团队联系方式句子未按预期简化 (LLM prompt rule 8)，仍包含账户团队选项。");}

            // ARN checks are more complex now that we expect full preservation
            // We need to check if ARNs from standardizedEng appear correctly in finalChinese
            const arnRegexForExtraction = new RegExp(robustArnRegexGlobal.source, 'g'); // Use the same global regex

            const arnsInStandardized = standardizedEng.match(arnRegexForExtraction) || [];
            arnsInStandardized.forEach(arn => {
                if (!finalChinese.includes(arn)) {
                    // Check if a slightly modified version (e.g. aws->aws-cn) exists, to pinpoint the error source
                    const cnVersion = arn.replace(/^arn:aws:/, 'arn:aws-cn:');
                    if (finalChinese.includes(cnVersion) && arn.startsWith('arn:aws:')) {
                        issues.push(`ARN "${arn}" 被错误地转换为中国区版本 "${cnVersion}"。`);
                    } else {
                         // More complex check: if ARN was put into placeholderMap but not restored correctly
                        let foundInMap = false;
                        for (const key in currentPlaceholderMap) {
                            if (key.startsWith("__ARNSTR_") && currentPlaceholderMap[key] === arn) {
                                foundInMap = true;
                                break;
                            }
                        }
                        if (foundInMap) {
                             const escapedArnForRegex = arn.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                             const partialArnRegex = new RegExp(escapedArnForRegex.substring(0, Math.min(30, escapedArnForRegex.length)) + ".*");
                            issues.push(`ARN "${arn}" (已进入占位符映射) 未按预期出现在最终输出中或被修改。实际输出中相关部分: ${JSON.stringify(finalChinese.match(partialArnRegex) || finalChinese.substring(0,100) )}`);
                        } else {
                             issues.push(`ARN "${arn}" 从标准化英文到最终输出的过程中丢失或被修改。`);
                        }
                    }
                }
            });


            if (standardizedEng.toLowerCase().includes("fast launch") && !finalChinese.includes("快速启动")) { issues.push("'Fast Launch' 未被翻译为 '快速启动'.");}
            if (standardizedEng.includes("Service Quotas console") && !finalChinese.includes("Service Quotas 控制台") && !finalChinese.includes("__PRDNM_")) {  issues.push("'Service Quotas console' 未被正确处理为 'Service Quotas 控制台'.");}
            if (standardizedEng.includes("aws Lambda functions") && !finalChinese.includes("Amazon Lambda 函数")) { issues.push("首次出现的 'aws Lambda functions' 未正确标准化为 'Amazon Lambda 函数'.");}
            if (standardizedEng.includes("aws Lambda team") && !finalChinese.includes("Lambda 团队")) { issues.push("后续出现的 'aws Lambda team' 未正确标准化为 'Lambda 团队'.");}
            if (finalChinese.includes("aws lambda list-functions")) { /* This is now expected */ }
            else if (standardizedEng.includes("aws lambda list-functions") && !finalChinese.includes("aws lambda list-functions")) {
                issues.push("CLI command 'aws lambda list-functions' not preserved as expected.");
            }
            if (standardizedEng.includes("Amazon Serverless Application Model (SAM)") && !finalChinese.includes("Amazon Serverless Application Model (Amazon SAM)")) {
                issues.push("SAM 全称 'Amazon Serverless Application Model (SAM)' 未正确标准化为 'Amazon Serverless Application Model (Amazon SAM)'.");
            }
            if (standardizedEng.includes("Amazon Serverless Application Model (Amazon SAM)") && finalChinese.includes("亚马逊云科技 Serverless Application Model (Amazon SAM)")) {
                 issues.push("'Amazon Serverless Application Model (Amazon SAM)' 中的 'Amazon' 被错误翻译。");
            }
             if (finalChinese.includes("亚马逊 Amazon CLI 命令参考")) {
                issues.push("错误地将 'Amazon CLI' 前的独立 'Amazon' 翻译为了 '亚马逊'");
            }


            return { hasIssues: issues.length > 0, issues };
        }

        async function callLLMApi(history, stageName) {
            const baseUrl = "http://internal-ai-tao-llm-apiserver-dev-1126944677.cn-northwest-1.elb.amazonaws.com.cn";
            const tokenizeUrl = `${baseUrl}/v1/tokenize`;
            const invokeUrl = `${baseUrl}/v1/invocations`;
            const modelsUrl = `${baseUrl}/v1/models`; // Added models URL
            const authHeader = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwcm9qZWN0IjoidGVzdCJ9.ebcS-y4S0g9qAaBh_GHfagWOgLKrySGAwvUJsJb2Ak8';
            logDebug(`调用LLM API (${stageName})`, { inputLength: history[history.length-1].content.length });
            let tokenizeFetchPromise;
            let invokeFetchPromise;
            let mainResult; // To store the result of the main invocation

            try {
                let currentModel = null;
                const tokenizeRequestBody = { messages: history };
                tokenizeFetchPromise = fetch(tokenizeUrl, { method: "POST", headers: { 'accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': authHeader }, body: JSON.stringify(tokenizeRequestBody) });
                const tokenizeResponse = await tokenizeFetchPromise;
                tokenizeFetchPromise = null;
                if (!tokenizeResponse.ok) {
                    const errorText = await tokenizeResponse.text();
                    let specificErrorMsg = `Tokenize API 请求失败: ${tokenizeResponse.status} ${errorText}`;
                    if (tokenizeResponse.status === 404 && errorText.includes("model") && errorText.includes("does not exist") && tokenizeRequestBody.model) {
                        logDebug("模型不存在，尝试无模型重试 Tokenize API", {});
                        delete tokenizeRequestBody.model;
                        tokenizeFetchPromise = fetch(tokenizeUrl, { method: "POST", headers: { 'accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': authHeader }, body: JSON.stringify(tokenizeRequestBody) });
                        const retryTokenizeResponse = await tokenizeFetchPromise;
                        tokenizeFetchPromise = null;
                        if (!retryTokenizeResponse.ok) {
                            const retryErrorText = await retryTokenizeResponse.text();
                            specificErrorMsg = `Tokenize API 重试失败: ${retryTokenizeResponse.status} ${retryErrorText}`;
                             const err = new Error(specificErrorMsg);
                             handleError(err, `callLLMApi (${stageName}) - Tokenize Retry`);
                             throw err;
                        }
                        const retryTokenizeData = await retryTokenizeResponse.json();
                        logDebug("Tokenize API 重试成功", retryTokenizeData);
                        currentModel = retryTokenizeData.model_name || null;
                        mainResult = await invokeLLM(history, retryTokenizeData.count, retryTokenizeData.max_model_len, currentModel, authHeader, invokeUrl, stageName);
                    } else {
                        const err = new Error(specificErrorMsg);
                        handleError(err, `callLLMApi (${stageName}) - Tokenize`);
                        throw err;
                    }
                } else {
                    const tokenizeData = await tokenizeResponse.json();
                    logDebug(`Tokenize API 响应 (${stageName})`, tokenizeData);
                    if (typeof tokenizeData.count !== 'number' || typeof tokenizeData.max_model_len !== 'number') {
                        const err = new Error(`无法从Tokenize响应解析count或max_model_len: ${JSON.stringify(tokenizeData)}`);
                        handleError(err, `callLLMApi (${stageName}) - Tokenize Data Parse`);
                        throw err;
                    }
                    currentModel = tokenizeData.model_name || null;
                    mainResult = await invokeLLM(history, tokenizeData.count, tokenizeData.max_model_len, currentModel, authHeader, invokeUrl, stageName);
                }

                // Fetch and log model info if debug mode is on, after main invocation
                if (debugMode) {
                    logDebug(`调试模式: 正在获取模型列表 (${stageName})`, { modelsUrl });
                    try {
                        const modelsResponse = await fetch(modelsUrl, {
                            method: "GET",
                            headers: { 'accept': 'application/json', 'Authorization': authHeader }
                        });
                        if (!modelsResponse.ok) {
                            const errorText = await modelsResponse.text();
                            logDebug(`获取模型列表API请求失败 (${stageName})`, { status: modelsResponse.status, error: errorText });
                        } else {
                            const modelsData = await modelsResponse.json();
                            logDebug(`模型列表API响应 (${stageName})`, modelsData);
                        }
                    } catch (modelsError) {
                        logDebug(`获取模型列表时发生错误 (${stageName})`, { message: modelsError.message, stack: modelsError.stack, error_object: modelsError });
                    }
                }
                return mainResult; // Return the result from the primary invocation

            } catch (error) {
                if (tokenizeFetchPromise || (invokeFetchPromise && !tokenizeFetchPromise)) {
                     const err = new Error(`Fetch API 调用失败: ${error.message}`);
                     err.cause = error;
                     handleError(err, `callLLMApi (${stageName}) - Outer Fetch`);
                     throw err;
                }
                // If the error was already handled and re-thrown from invokeLLM or tokenize logic, just re-throw
                if (!String(error.message).includes("API 请求失败")) { // Avoid double handling for API specific errors
                    handleError(error, `callLLMApi (${stageName}) - General Catch`);
                }
                throw error;
            }
        }

        async function invokeLLM(history, tokenCount, maxModelLen, modelName, authHeader, invokeUrl, stageName) {
            let invokeFetchPromise;
            try {
                let maxTokens = maxModelLen - tokenCount - 150; if (maxTokens <= 100) { maxTokens = 2000; }
                logDebug(`计算的 maxTokens (${stageName})`, { tokenCount, maxModelLen, maxTokens, modelUsed: modelName });
                const invokeRequestBody = { messages: history, temperature: 0.05, max_tokens: maxTokens };
                if (modelName) invokeRequestBody.model = modelName;
                invokeFetchPromise = fetch(invokeUrl, { method: "POST", headers: { 'accept': 'application/json', 'Content-Type': 'application/json', 'Authorization': authHeader }, body: JSON.stringify(invokeRequestBody) });
                const invokeResponse = await invokeFetchPromise; invokeFetchPromise = null;
                if (!invokeResponse.ok) { const errorText = await invokeResponse.text(); const err = new Error(`Invocations API 请求失败: ${invokeResponse.status} ${errorText}`); handleError(err, `invokeLLM (${stageName})`); throw err;  }
                const invokeData = await invokeResponse.json();
                if (!invokeData.choices || !invokeData.choices[0] || !invokeData.choices[0].message || typeof invokeData.choices[0].message.content !== 'string') {
                    const err = new Error(`无法解析Invocations API响应: ${JSON.stringify(invokeData).substring(0,500)}`); handleError(err, `invokeLLM (${stageName}) - Response Parse`); throw err; }
                const result = invokeData.choices[0].message.content.trim();
                logDebug(`Invocations API 成功 (${stageName})`, { responseLength: result.length }); return result;
            } catch (error) {
                 if (invokeFetchPromise) {  const err = new Error(`Fetch API 调用失败 (Invoke LLM): ${error.message}`); err.cause = error; handleError(err, `invokeLLM (${stageName}) - Outer Fetch`); throw err;  }
                throw error;
            }
        }

        window.addEventListener('DOMContentLoaded', function() {
            console.log('Optimized v11.21.5 UI (Debug Display) Page Loaded. Initializing...');
            logDebug("页面初始化 (Optimized v11.21.5 UI - Debug Display)", { status: "就绪" });
        });
    </script>
</body>
</html>