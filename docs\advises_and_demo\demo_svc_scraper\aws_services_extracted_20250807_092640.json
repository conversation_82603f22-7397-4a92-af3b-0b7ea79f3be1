{"success": true, "services": ["AWS Elemental MediaConvert", "Amazon API Gateway", "Amazon App Mesh", "Amazon AppSync", "Amazon Athena", "Amazon Aurora – MySQL – 兼容版", "Amazon Aurora – PostgreSQL – 兼容版", "Amazon Backup", "Amazon Batch", "Amazon Budgets", "Amazon Certificate Manager", "Amazon Cloud Control API", "Amazon Cloud Map", "Amazon CloudFormation", "Amazon CloudFront", "Amazon CloudTrail", "Amazon CloudWatch", "Amazon CloudWatch Events", "Amazon CloudWatch Logs", "Amazon CodeBuild", "Amazon CodeCommit", "Amazon CodeDeploy", "Amazon CodePipeline", "Amazon Cognito", "Amazon Compute Optimizer", "Amazon Config", "Amazon Cost & Usage Report", "Amazon Cost Explorer", "Amazon Data Firehose", "Amazon DataSync", "Amazon Database Migration Service", "Amazon Deep Learning AMI", "Amazon Direct Connect", "Amazon Directory Service", "Amazon DocumentDB（兼容MongoDB）", "Amazon DynamoDB", "Amazon EC2 Auto Scaling", "Amazon ElastiCache", "Amazon Elastic Beanstalk", "Amazon Elastic Block Store (EBS)", "Amazon Elastic Compute Cloud (EC2)", "Amazon Elastic Container Registry (ECR)", "Amazon Elastic Container Service (ECS)", "Amazon Elastic File System (EFS)", "Amazon Elastic Kubernetes Service (EKS)", "Amazon Elastic MapReduce (EMR)", "Amazon EventBridge", "Amazon FSx for Lustre", "Amazon FSx for NetApp ONTAP", "Amazon FSx for OpenZFS", "Amazon FSx for Windows File Server", "Amazon Fargate", "Amazon Firewall Manager", "Amazon FreeRTOS", "Amazon GameLift", "Amazon Glacier", "Amazon Glue", "Amazon GuardDuty", "Amazon Health Dashboard", "Amazon IAM Identity Center", "Amazon Identity and Access Management (IAM)", "Amazon Inspector", "Amazon IoT Analytics", "Amazon IoT Core", "Amazon IoT Device Defender", "Amazon IoT Device Management", "Amazon IoT Events", "Amazon IoT Greengrass", "Amazon IoT Sitewise", "Amazon IoT TwinMaker", "Amazon Key Management Service(KMS)", "Amazon Keyspaces", "Amazon Kinesis Streams", "Amazon Kinesis Video Streams", "Amazon Lake Formation", "Amazon Lambda", "Amazon Launch Wizard", "Amazon License Manager", "Amazon MQ", "Amazon Managed Service for Apache Flink", "Amazon Managed Streaming for Apache Kafka (Amazon MSK)", "Amazon Managed Workflows for Apache Airflow (Amazon MWAA)", "Amazon MemoryDB", "Amazon Neptune", "Amazon Network Firewall", "Amazon OpenSearch Service (successor to Amazon Elasticsearch Service)", "Amazon Organizations", "Amazon Personalize", "Amazon Polly", "Amazon Private Certificate Authority", "Amazon PrivateLink", "Amazon QuickSight", "Amazon Redshift", "Amazon Relational Database Service (RDS)", "Amazon Resource Access Manager", "Amazon Route 53", "Amazon SageMaker", "Amazon Secrets Manager", "Amazon Security Hub", "Amazon Serverless Application Repository", "Amazon Service Catalog", "Amazon Simple Notification Service (SNS)", "Amazon Simple Queue Service (SQS)", "Amazon Simple Storage Service (S3)", "Amazon Simple Workflow Service (SWF)", "Amazon Step Functions", "Amazon Storage Gateway", "Amazon Systems Manager", "Amazon Timestream", "Amazon Transcribe", "Amazon Transfer Family", "Amazon Transit Gateway", "Amazon Trusted Advisor", "Amazon Virtual Private Cloud (VPC)", "Amazon WAF", "Amazon Web Services Marketplace", "Amazon Web Services Support", "Amazon Workspaces", "Amazon X-Ray", "Elastic Load Balancing (ELB)"], "validation": {"total_services": 120, "is_valid": false, "validation_errors": ["缺少核心服务: ['Amazon S3', 'Amazon RDS']"], "sample_services": ["AWS Elemental MediaConvert", "Amazon API Gateway", "Amazon App Mesh", "Amazon AppSync", "Amazon Athena", "Amazon Aurora – MySQL – 兼容版", "Amazon Aurora – PostgreSQL – 兼容版", "Amazon Backup", "Amazon Batch", "Amazon Budgets"], "timestamp": "2025-08-07T09:26:40.378350"}, "metadata": {"source_url": "https://www.amazonaws.cn/about-aws/regional-product-services/", "extraction_time": "2025-08-07T09:26:40.378350", "total_services": 120}}