好的，这是您提供的PDF文档转换成的Markdown格式内容。

---

**应用系统日志规范**
**文件编号: IS-L3-063-2**

<br>

# 宁夏西云数据科技有限公司
# 应用系统日志规范
## V1.0

<br>

| | |
| :--- | :--- |
| **文件级别:** L3 | **文件编号:** IS-L3-063-2 |
| **责任部门:** 应用评审工作组 | **文件版本号:** V1.0 |
| **编制:** 曲一鸣 | **文件发布日期:** 2024-03-19 |
| **审核:** 韩晋 | **文件生效日期:** 2024-03-19 |
| **批准:** 张联华 | **文件首次生效日期:** 2024-03-19 |

<br>
<br>

---

## 目录
1. 概述
   1.1 介绍
   1.2 目的
2. 适用范围
3. 较上一版本的更新
4. 日志技术要求
   4.1 日志打印范围
      4.1.1 应用系统访问日志
      4.1.2 应用系统日志
   4.2 日志内容要求
      4.2.1 日志要素
      4.2.2 日志级别
      4.2.3 日志内容约定
5. 日志传输与存储
   5.1 日志传输
   5.2 存储位置
   5.3 数据保留期
6. 日志管理
   6.1 访问控制
   6.2 完整性和可用性
   6.3 监控和报警
7. 版本变更记录

---

### 1. 概述

#### 1.1 介绍
西云数据应用系统承担着公司日常办公和业务运营等任务,既需要安全稳定运行, 同时也需要确保企业遵守适用的法律法规,满足监管机构的要求。因此,需要具备可追溯和可审计的日志记录,西云数据应用系统日志规范的目的主要包括:
*   确保对应用系统的运行状态和操作活动进行全面记录,以便后续的故障排查、性能分析和安全审计。
*   支持实时监控和警报机制,及时发现潜在的异常行为或安全威胁。
*   提供对应用系统安全事件和主要用户行为的可追溯性和可审计性,以满足合规性要求。
*   提供对业务流程的可分析数据,以支持业务决策和优化。

#### 1.2 目的
基于以上背景和目的,制定并遵守西云数据公司范围一致的应用系统日志规范将有助于保证系统的可靠性、可审计性和合规性,同时为业务决策提供有价值的数据支持。

### 2. 适用范围

| | |
| :--- | :--- |
| **部门** | 所有部门 |
| **地区** | 所有地区 |

### 3. 较上一版本的更新
该技术要求定期(每年)根据实施反馈审查更新。
此版本是该规范的第一个版本。

### 4. 日志技术要求

#### 4.1 日志打印范围

##### 4.1.1 应用系统访问日志
应默认保存中间件的接口访问记录。

##### 4.1.2 应用系统日志
应用系统日志分为重要安全事件日志、重要用户行为日志和业务功能日志。对于评级为关键和重要的应用,需额外打印应用程序或系统业务功能日志,应用分级标准请参考《应用系统安全规范》。

*   **重要安全事件日志**: 应用系统登录、注册、登出、注销、修改密码、重置密码、系统配置变更、账户权限变更、角色授予、变更、撤销等重要安全操作,需记录日志。
*   **重要用户行为日志**: 敏感数据增删改查操作、数据批量导入导出操作、业务变更等重要用户行为需记录日志。
*   **业务功能日志**: 业务功能是指应用系统提供的一个或多个功能,以满足用户需求并支持其业务流程。关键应用和重要应用的主要业务功能需要记录日志。

应用系统需系统负责人根据实际业务系统情况确定一个或多个主要业务功能。以下是一些常见的主要业务功能示例:
*   数据管理功能: 涉及对数据的增删改查操作。记录数据创建、修改、删除等事件,以及数据查询的关键信息和结果。
*   工单处理功能: 工单系统的工单创建、工单处理、工单转发、工单关闭等操作,记录工单流转过程的关键信息和结果。
*   文件管理功能: 用于上传、下载、共享文件。记录文件的上传、下载、共享情况,以及文件版本控制等。
*   审批流程功能: 管理审批流程、处理请求和申请。记录审批进度、操作者、审批结果等。

#### 4.2 日志内容要求

##### 4.2.1 日志要素
为确保日志信息的一致性、可读性和可用性,应用系统日志内容应包括以下要素:
*   **Who**, 谁干了什么? 用户唯一标识。
*   **When**, 什么时候? 日志需记录时间。
*   **What**, 做了什么事情。
*   **Trace**, 调用链。(不强制要求)
*   **Input**, 传入参数。
*   **Output**, 输出参数。
*   **Error**, 如果有错误信息则记录。

参考样例如下:
```
2023-11-29 10:02:13.812 | //When
INFO | //日志级别
riskcontrol.source_data.app_data:get_aodun_validate_check_data:613 //生成日志的模块、类、方法、行
"basic_data":{
    "ip":"***********",
    "mail": "<EMAIL>", //Who
    "aws_account": "",
    "strategy_name": "app.aodun.validate.check",
    "strategy_type": "app_sec_strategy",
    "alert_type": "alert"
},
"errno": 0 //Output
"alert_body": [{
    "project_key": "SIEM",
    ...省略部分内容...
    "assignee": "<EMAIL>,<EMAIL>"
}], //Input
"alert_res": {
    "code": 200,
    "message":"创建成功,编号:SIEM-nel5o7aK",
    "data":{...省略部分内容...}
}, //Output
"single_info": {
    "ip":"************",
    ...省略部分内容...
    "internal_user_department": "NWCD Premium Support"
} //What
```
以上的日志样例包含了时间、事件、处理结果/成功与否、入参、出参的信息。

##### 4.2.2 日志级别
日志需标识日志的级别,如 DEBUG、INFO、WARNING、ERROR、FATAL。最低需保证记录 INFO 和 FATAL 级别日志。

| 标识 | 类型 | 场景 | 强制要求 |
| :--- | :--- | :--- | :--- |
| **INFO** | 信息 | 提供应用程序的运行状态和关键事件的信息记录 | 是 |
| **FATAL** | 错误 | 发生严重错误,意味着本次请求无法继续执行 | 是 |
| **DEBUG** | 调试 | 用于开发环境中的调试,不要把 Debug 日志带到线上环境中 | 否 |
| **WARNING** | 警告 | 发生异常,但本次请求可继续,部分结果异常或缺失,对业务流程无致命影响的错误 | 否 |
| **ERROR** | 错误 | 影响到程序正常运行、当前请求正常运行的异常情况,可使用 WARNING 代替 | 否 |

##### 4.2.3 日志内容约定
*   日志内容应格式化,例如使用 JSON 结构化日志。
*   避免敏感信息(如密码、个人身份证号码等)的明文记录,可以对其进行脱敏处理。脱敏处理的手段:
    *   如果数据无需解密,可采取对原始敏感信息进行 MD5 摘要打印到日志中。
    *   如果数据特定情况需要解密,可采取加密算法(如 AES、RSA 等)对原始敏感信息进行加密后以 Base64 方式打印到日志中。
*   谨慎记录日志。多余的日志会增加磁盘负担和分析复杂度,应保证仅在业务功能处理完成的节点记录日志。

### 5. 日志传输与存储

#### 5.1 日志传输
日志在传输过程中需保证实时性、完整性和保密性要求。建议通过 CloudWatch Log 插件、FileBeat、Logstash 等方式进行日志传输或实时传输至日志服务器。

#### 5.2 存储位置
日志应存储在安全且可靠的集中存储位置,例如专用的日志服务器或云存储服务。存储位置应具备以下特点:
*   提供高可用性和持久性,以防止数据丢失。
*   具备足够的存储容量,以容纳预期的日志数据量。
*   支持加密传输和存储,以确保数据的机密性。
结合西云数据实际情况,可选择 CloudWatch 或 S3 服务作为日志集中存储的位置。

#### 5.3 数据保留期
根据业务需求和合规性要求,需定义日志保留期,符合相应的数据保留政策,如网络安全法要求的保存不少于6个月的网络运行状态和网络安全事件日志。

### 6. 日志管理

#### 6.1 访问控制
限制对日志存储的访问权限,并使用身份验证和授权机制确保只有经过授权的人员可以查看和操作日志数据。建议实施以下措施:
*   分配适当的用户角色和权限,确保访问与职责和需求相匹配。
*   实施多层次访问控制,将敏感日志数据仅限于必要授权的人员访问。
*   定期审查和更新访问权限,以反映员工离职、转岗或角色变更等情况。

#### 6.2 完整性和可用性
应确保日志数据的完整性和可用性。需要采取以下措施:
*   **日志冗余**: 将日志数据进行冗余存储,以防止单点故障和数据丢失。
*   **备份与恢复**: 定期备份日志数据,并测试和验证恢复过程,以确保数据可靠性和灾难恢复能力。

#### 6.3 监控和报警
实施日志监控和报警机制,能够监测日志文件的异常存取活动,保证日志的完整性和可用性。

### 7. 版本变更记录
建立文档记录,包括日志存储的架构、配置信息、访问控制策略等,以便于日后的管理和审计。

| 版本 | 生效日期 | 修正背景原因 | 起草者 |
| :--- | :--- | :--- | :--- |
| V1.0 | 2024-03-19 | 初稿 | 曲一鸣 |