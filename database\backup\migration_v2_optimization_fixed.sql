-- ====================================================================
-- CN Mass Email Translator 数据库优化迁移脚本 (修复版)
-- 基于 Amazon_service_name_matching_solutio.md 的优化建议
-- 
-- 版本: v1.1 -> v2.0 (修复版)
-- 创建日期: 2025-01-08
-- 修复说明: 修复了字段添加和数据迁移的顺序问题
-- 用途: 将现有数据库升级到优化版本，保证数据完整性
-- ====================================================================

-- 开始事务以确保原子性操作
BEGIN;

-- 设置客户端编码
SET client_encoding = 'UTF8';

RAISE NOTICE '=================================================================';
RAISE NOTICE 'CN Mass Email Translator 数据库优化迁移开始';
RAISE NOTICE '迁移时间: %', NOW();
RAISE NOTICE '=================================================================';

-- ====================================================================
-- 1. 备份现有数据
-- ====================================================================

RAISE NOTICE '步骤 1: 备份现有数据...';

-- 创建临时备份表
CREATE TEMP TABLE regex_patterns_backup AS 
SELECT * FROM regex_patterns;

RAISE NOTICE '✓ 已创建 regex_patterns 数据备份 (% 条记录)', (SELECT COUNT(*) FROM regex_patterns_backup);

-- ====================================================================
-- 2. 检查现有表结构
-- ====================================================================

RAISE NOTICE '步骤 2: 检查现有表结构...';

DO $$
DECLARE
    table_exists boolean;
    metadata_exists boolean;
    pattern_type_is_enum boolean;
BEGIN
    -- 检查 regex_patterns 表是否存在
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'regex_patterns'
    ) INTO table_exists;
    
    IF NOT table_exists THEN
        RAISE EXCEPTION '错误: regex_patterns 表不存在，请先运行基础架构脚本';
    END IF;
    
    -- 检查 metadata 字段是否已存在
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'regex_patterns' AND column_name = 'metadata'
    ) INTO metadata_exists;
    
    -- 检查 pattern_type 是否为 ENUM 类型
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'regex_patterns' 
        AND column_name = 'pattern_type' 
        AND udt_name = 'regex_pattern_type'
    ) INTO pattern_type_is_enum;
    
    RAISE NOTICE '✓ 表结构检查完成:';
    RAISE NOTICE '  - regex_patterns 表: 存在';
    RAISE NOTICE '  - metadata 字段: %', CASE WHEN metadata_exists THEN '已存在' ELSE '不存在' END;
    RAISE NOTICE '  - pattern_type ENUM: %', CASE WHEN pattern_type_is_enum THEN '已是ENUM' ELSE '需要转换' END;
END $$;

-- ====================================================================
-- 3. 添加新的ENUM类型值（如果需要）
-- ====================================================================

RAISE NOTICE '步骤 3: 检查和添加ENUM类型值...';

DO $$
BEGIN
    -- 检查 regex_pattern_type ENUM 类型是否存在
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'regex_pattern_type') THEN
        -- 为 regex_pattern_type 添加新值（如果不存在）
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumlabel = 'CONTEXT_PROTECTED' 
            AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'regex_pattern_type')
        ) THEN
            ALTER TYPE regex_pattern_type ADD VALUE 'CONTEXT_PROTECTED';
            RAISE NOTICE '✓ 已添加新的ENUM值: CONTEXT_PROTECTED';
        ELSE
            RAISE NOTICE '✓ ENUM值 CONTEXT_PROTECTED 已存在，跳过添加';
        END IF;
    ELSE
        RAISE NOTICE '⚠ 警告: regex_pattern_type ENUM类型不存在，可能需要先创建基础架构';
    END IF;
END $$;

-- ====================================================================
-- 4. 表结构优化
-- ====================================================================

RAISE NOTICE '步骤 4: 表结构优化...';

-- 4.1 添加新的 metadata JSONB 字段
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'regex_patterns' AND column_name = 'metadata'
    ) THEN
        ALTER TABLE regex_patterns ADD COLUMN metadata JSONB;
        RAISE NOTICE '✓ 已添加 metadata JSONB 字段';
    ELSE
        RAISE NOTICE '✓ metadata 字段已存在，跳过添加';
    END IF;
END $$;

-- 4.2 将现有的 pattern_type 从 VARCHAR 改为 ENUM（如果当前是VARCHAR）
DO $$
BEGIN
    -- 检查当前 pattern_type 字段类型
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'regex_patterns' 
        AND column_name = 'pattern_type' 
        AND data_type = 'character varying'
    ) THEN
        RAISE NOTICE '正在将 pattern_type 从 VARCHAR 转换为 ENUM...';
        
        -- 先更新所有无效值为有效的ENUM值
        UPDATE regex_patterns 
        SET pattern_type = 'GENERAL' 
        WHERE pattern_type NOT IN ('SERVICE_NAME', 'TIMEZONE', 'CLI_COMMAND', 'URL', 'GENERAL');
        
        -- 修改字段类型为ENUM
        ALTER TABLE regex_patterns 
        ALTER COLUMN pattern_type TYPE regex_pattern_type 
        USING pattern_type::regex_pattern_type;
        
        RAISE NOTICE '✓ 已将 pattern_type 字段改为 ENUM 类型';
    ELSE
        RAISE NOTICE '✓ pattern_type 字段已是正确类型，跳过转换';
    END IF;
END $$;

-- 4.3 添加正则表达式长度约束
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'chk_regex_string_length'
    ) THEN
        ALTER TABLE regex_patterns 
        ADD CONSTRAINT chk_regex_string_length 
        CHECK (length(regex_string) < 10000);
        RAISE NOTICE '✓ 已添加 regex_string 长度约束 (< 10000 字符)';
    ELSE
        RAISE NOTICE '✓ regex_string 长度约束已存在，跳过添加';
    END IF;
END $$;

-- ====================================================================
-- 5. 数据迁移和转换
-- ====================================================================

RAISE NOTICE '步骤 5: 数据迁移和转换...';

-- 5.1 将现有的 notes 字段中的结构化信息迁移到 metadata 字段
DO $$
DECLARE
    updated_count integer;
BEGIN
    -- 确保 metadata 字段存在后再进行迁移
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'regex_patterns' AND column_name = 'metadata'
    ) THEN
        -- 将现有的 notes 字段中的结构化信息迁移到 metadata 字段
        UPDATE regex_patterns 
        SET metadata = jsonb_build_object(
            'isCompoundWithSuffix', 
            CASE 
                WHEN notes LIKE '%isCompoundWithSuffix: true%' THEN true 
                ELSE false 
            END,
            'suffixGroup',
            CASE 
                WHEN notes ~ 'suffixGroup: (\d+)' THEN 
                    (regexp_match(notes, 'suffixGroup: (\d+)'))[1]::int
                ELSE null
            END,
            'patternCategory',
            CASE 
                WHEN pattern_name LIKE '%_FULL_%' THEN 'full_name'
                WHEN pattern_name LIKE '%_SHORT_%' THEN 'short_name'
                WHEN pattern_name LIKE '%_ACRONYM_%' THEN 'acronym'
                ELSE 'general'
            END,
            'hasBoundaryProtection',
            CASE 
                WHEN notes LIKE '%boundary protection%' THEN true
                ELSE false
            END,
            'migrated_from_notes', true,
            'migration_timestamp', NOW()::text
        )
        WHERE metadata IS NULL AND notes IS NOT NULL;
        
        GET DIAGNOSTICS updated_count = ROW_COUNT;
        RAISE NOTICE '✓ 已完成 notes 到 metadata 的数据迁移 (% 条记录)', updated_count;

        -- 为没有 metadata 的记录设置默认值
        UPDATE regex_patterns 
        SET metadata = jsonb_build_object(
            'isCompoundWithSuffix', false,
            'patternCategory', 'general',
            'hasBoundaryProtection', false,
            'migration_timestamp', NOW()::text
        )
        WHERE metadata IS NULL;
        
        GET DIAGNOSTICS updated_count = ROW_COUNT;
        RAISE NOTICE '✓ 已为 % 条记录设置 metadata 默认值', updated_count;
    ELSE
        RAISE EXCEPTION '错误: metadata 字段不存在，无法进行数据迁移';
    END IF;
END $$;

-- ====================================================================
-- 6. 索引优化
-- ====================================================================

RAISE NOTICE '步骤 6: 创建优化索引...';

-- 6.1 创建部分索引 - 只索引活跃的服务
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_service_names_active_code') THEN
        CREATE INDEX idx_service_names_active_code 
        ON service_names(service_code) WHERE is_active = TRUE;
        RAISE NOTICE '✓ 已创建部分索引: idx_service_names_active_code';
    ELSE
        RAISE NOTICE '✓ 部分索引 idx_service_names_active_code 已存在';
    END IF;
END $$;

-- 6.2 创建关键性能提升索引 - 只索引活跃且有效的模式
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_regex_patterns_active_valid') THEN
        CREATE INDEX idx_regex_patterns_active_valid 
        ON regex_patterns(priority DESC, id ASC) 
        WHERE is_active = TRUE AND validation_status = 'valid';
        RAISE NOTICE '✓ 已创建部分索引: idx_regex_patterns_active_valid';
    ELSE
        RAISE NOTICE '✓ 部分索引 idx_regex_patterns_active_valid 已存在';
    END IF;
END $$;

-- 6.3 为新的 metadata JSONB 字段创建 GIN 索引
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_regex_patterns_metadata') THEN
        CREATE INDEX idx_regex_patterns_metadata 
        ON regex_patterns USING GIN (metadata);
        RAISE NOTICE '✓ 已创建 GIN 索引: idx_regex_patterns_metadata';
    ELSE
        RAISE NOTICE '✓ GIN 索引 idx_regex_patterns_metadata 已存在';
    END IF;
END $$;

-- ====================================================================
-- 7. translation_jobs 表分区优化
-- ====================================================================

RAISE NOTICE '步骤 7: translation_jobs 表分区优化检查...';

DO $$
DECLARE
    table_exists boolean;
    has_data boolean;
    is_partitioned boolean;
BEGIN
    -- 检查表是否存在
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'translation_jobs'
    ) INTO table_exists;
    
    IF table_exists THEN
        -- 检查是否有数据
        SELECT EXISTS (SELECT 1 FROM translation_jobs LIMIT 1) INTO has_data;
        
        -- 检查是否已经是分区表
        SELECT EXISTS (
            SELECT 1 FROM pg_partitioned_table pt
            JOIN pg_class c ON pt.partrelid = c.oid
            WHERE c.relname = 'translation_jobs'
        ) INTO is_partitioned;
        
        IF is_partitioned THEN
            RAISE NOTICE '✓ translation_jobs 表已经是分区表';
        ELSIF has_data THEN
            RAISE NOTICE '⚠ translation_jobs 表中有数据，跳过分区转换';
            RAISE NOTICE '  建议：在维护窗口期间手动执行分区转换';
        ELSE
            RAISE NOTICE '✓ translation_jobs 表为空，可以安全地转换为分区表';
            RAISE NOTICE '  注意：分区转换需要在维护窗口期间执行';
        END IF;
    ELSE
        RAISE NOTICE '⚠ translation_jobs 表不存在';
    END IF;
END $$;

-- ====================================================================
-- 8. 数据验证
-- ====================================================================

RAISE NOTICE '步骤 8: 数据验证...';

-- 8.1 验证 metadata 字段迁移
DO $$
DECLARE
    total_patterns integer;
    patterns_with_metadata integer;
BEGIN
    SELECT COUNT(*) INTO total_patterns FROM regex_patterns;
    SELECT COUNT(*) INTO patterns_with_metadata FROM regex_patterns WHERE metadata IS NOT NULL;
    
    IF total_patterns = patterns_with_metadata THEN
        RAISE NOTICE '✓ metadata 字段迁移验证通过: %/% 记录有 metadata', patterns_with_metadata, total_patterns;
    ELSE
        RAISE EXCEPTION '✗ metadata 字段迁移验证失败: %/% 记录有 metadata', patterns_with_metadata, total_patterns;
    END IF;
END $$;

-- 8.2 验证 ENUM 类型转换
DO $$
DECLARE
    enum_count integer;
    total_count integer;
BEGIN
    SELECT COUNT(*) INTO total_count FROM regex_patterns;
    SELECT COUNT(*) INTO enum_count 
    FROM regex_patterns 
    WHERE pattern_type::text IN ('SERVICE_NAME', 'TIMEZONE', 'CLI_COMMAND', 'URL', 'GENERAL', 'CONTEXT_PROTECTED');
    
    IF enum_count = total_count THEN
        RAISE NOTICE '✓ ENUM 类型转换验证通过: %/% 记录', enum_count, total_count;
    ELSE
        RAISE EXCEPTION '✗ ENUM 类型转换验证失败: %/% 记录有效', enum_count, total_count;
    END IF;
END $$;

-- 8.3 验证索引创建
DO $$
DECLARE
    expected_indexes text[] := ARRAY[
        'idx_service_names_active_code',
        'idx_regex_patterns_active_valid', 
        'idx_regex_patterns_metadata'
    ];
    idx text;
    missing_indexes text[] := '{}';
    created_count integer := 0;
BEGIN
    FOREACH idx IN ARRAY expected_indexes
    LOOP
        IF EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = idx) THEN
            created_count := created_count + 1;
        ELSE
            missing_indexes := array_append(missing_indexes, idx);
        END IF;
    END LOOP;
    
    IF array_length(missing_indexes, 1) IS NULL THEN
        RAISE NOTICE '✓ 所有优化索引创建验证通过 (%/% 个索引)', created_count, array_length(expected_indexes, 1);
    ELSE
        RAISE EXCEPTION '✗ 缺少索引: %', array_to_string(missing_indexes, ', ');
    END IF;
END $$;

-- ====================================================================
-- 9. 性能统计信息更新
-- ====================================================================

RAISE NOTICE '步骤 9: 更新表统计信息...';

-- 更新所有相关表的统计信息
ANALYZE service_names;
ANALYZE regex_patterns;
ANALYZE brand_term_mappings;
ANALYZE url_mappings;
ANALYZE translation_jobs;
ANALYZE feedback_submissions;

RAISE NOTICE '✓ 已更新所有表的统计信息';

-- ====================================================================
-- 10. 清理和完成
-- ====================================================================

RAISE NOTICE '步骤 10: 清理和完成...';

-- 临时备份表会在事务结束时自动删除
RAISE NOTICE '✓ 临时备份表将在事务结束时自动清理';

-- 提交事务
COMMIT;

-- ====================================================================
-- 迁移完成报告
-- ====================================================================

DO $$
DECLARE
    service_count integer;
    pattern_count integer;
    active_pattern_count integer;
    metadata_count integer;
BEGIN
    SELECT COUNT(*) INTO service_count FROM service_names WHERE is_active = TRUE;
    SELECT COUNT(*) INTO pattern_count FROM regex_patterns;
    SELECT COUNT(*) INTO active_pattern_count FROM regex_patterns WHERE is_active = TRUE AND validation_status = 'valid';
    SELECT COUNT(*) INTO metadata_count FROM regex_patterns WHERE metadata IS NOT NULL;
    
    RAISE NOTICE '=================================================================';
    RAISE NOTICE 'CN Mass Email Translator 数据库优化迁移完成！';
    RAISE NOTICE '迁移时间: %', NOW();
    RAISE NOTICE '=================================================================';
    RAISE NOTICE '数据统计:';
    RAISE NOTICE '- 活跃服务数量: %', service_count;
    RAISE NOTICE '- 正则模式总数: %', pattern_count;
    RAISE NOTICE '- 活跃有效模式数: %', active_pattern_count;
    RAISE NOTICE '- 包含metadata的模式数: %', metadata_count;
    RAISE NOTICE '=================================================================';
    RAISE NOTICE '主要优化项目:';
    RAISE NOTICE '✓ regex_patterns.pattern_type 改为 ENUM 类型';
    RAISE NOTICE '✓ 新增 regex_patterns.metadata JSONB 字段';
    RAISE NOTICE '✓ 添加 regex_string 长度约束 (< 10000 字符)';
    RAISE NOTICE '✓ 创建高性能部分索引';
    RAISE NOTICE '✓ 数据完整性验证通过';
    RAISE NOTICE '=================================================================';
    RAISE NOTICE '后续建议:';
    RAISE NOTICE '1. 在维护窗口期间考虑将 translation_jobs 转换为分区表';
    RAISE NOTICE '2. 监控新索引的性能表现';
    RAISE NOTICE '3. 定期更新表统计信息以保持查询优化';
    RAISE NOTICE '4. 使用 database/verify_migration.sql 进行详细验证';
    RAISE NOTICE '=================================================================';
END $$;