# AWS服务名称同步模块需求文档 - v2.0 优化版

## 介绍

AWS服务同步模块是一个独立的数据同步服务，专门负责定期从官方网页和PDF文档中获取、整理和同步AWS中国区的服务名称信息。该模块实现"逻辑外化"设计理念，将硬编码的服务名称列表转换为数据库驱动的动态管理系统，通过数据库为其他系统提供准确、最新的服务名称数据支持。

**模块定位**: 作为完全独立的数据同步服务，专注于从官方数据源获取、处理和存储AWS服务名称信息，通过数据库中介模式为其他系统提供标准化的数据支持。模块不提供任何对外接口，其他系统应直接从数据库获取所需数据，实现模块间的完全解耦。

**🎯 核心设计原则**: 遵循**功能精简原则**，只实现核心业务功能和基本错误处理，保持代码简洁和可维护性。

**v2.0 优化版特性**:
- 完全独立的数据同步服务，遵循单一职责原则
- 数据库中介模式，通过数据库实现模块间的完全解耦
- JSONB元数据支持，实现灵活的模式管理
- 部分索引优化，数据库查询性能提升10-50x
- 8种核心正则表达式模式类型，覆盖所有使用场景
- 完善的边界保护机制，避免ARN、URL等上下文误匹配
- 不提供任何对外接口，确保模块的完全独立性


### 新增：空表初始化与全量重置能力（v2.0 增强）

为解决并发与一致性风险，系统在“空表初始化/全量重置”场景中新增如下能力与约束：

- 并发控制：使用事务级 advisory lock（pg_try_advisory_xact_lock）防止并发全量操作
- 外部备份策略：非空全量重置前，应用层必须先完成外部备份（如 CTAS 或导出）；数据库过程内部不做备份
- 顺序写入：严格遵循“先 service_names，后 regex_patterns”的顺序，不使用 session_replication_role 绕过约束
- 孤儿硬失败：对 pattern_type='SERVICE_NAME' 且 active 的孤儿模式（related_service_id IS NULL）实施“硬失败”，触发回滚
- 初始化后优化：初始化完成后执行 ANALYZE 两表，确保后续读取性能

以上策略统一体现在新的存储过程 `perform_complete_data_resync()` 中；具体实现与使用见《数据库开发指南》与迁移文件。

## 需求

### 需求1：多数据源网页抓取系统

**用户故事：** 作为系统管理员，我希望能够自动抓取AWS中国区官方网页的服务信息，以获取最权威的服务名称数据。

#### 验收标准

1. WHEN 系统执行同步任务 THEN 系统 SHALL 访问 https://www.amazonaws.cn/about-aws/regional-product-services/ 页面
2. WHEN 访问官方页面 THEN 系统 SHALL 使用BeautifulSoup解析HTML，提取所有"提供的服务"字段的值作为服务的权威全称
3. WHEN 提取服务名称 THEN 系统 SHALL 验证数据完整性，确保提取的服务数量大于0且与历史数据相比变化幅度在合理范围内（±30%）
4. IF 网页访问失败 THEN 系统 SHALL 实施重试机制（最多3次），记录详细错误日志并触发告警
5. WHEN 数据提取完成 THEN 系统 SHALL 将网页数据标记为'web_scrape'数据源，并记录提取统计信息
6. WHEN 处理网页数据 THEN 系统 SHALL 实施速率限制和User-Agent轮换，遵守网站的robots.txt规则

### 需求2：S3 PDF文档解析系统

**用户故事：** 作为系统管理员，我希望能够解析存储在S3中的AWS官方PDF文档，以获取服务的详细命名信息和简称。

#### 验收标准

1. WHEN 系统处理PDF文档 THEN 系统 SHALL 从AWS Secrets Manager获取snpdf字段配置（s3://mass-email-translator-feedback/awsofferingnames_pdf/AWSOfferingNames05032024.pdf），解析s3://bucket/key格式
2. WHEN 下载PDF文件 THEN 系统 SHALL 使用pdfplumber库解析所有三列数据：AWS offering (internal name)、Long name (First use)、Short name (Subsequent use)
3. WHEN 解析PDF数据 THEN 系统 SHALL 将所有'AWS'和'aws'字符串替换为'Amazon'进行标准化处理
4. WHEN 处理内部名称 THEN 系统 SHALL 清理"AWS offering (internal name)"字段中的特殊字符，去除字段开头和结尾的空格（字段中间的空格保留），生成规范化的service_code
5. WHEN 数据预处理完成 THEN 系统 SHALL 临时存储处理后的PDF数据用于后续智能匹配
6. IF PDF文件损坏或无法读取 THEN 系统 SHALL 记录错误并继续使用现有数据，不中断同步流程
7. WHEN PDF解析完成 THEN 系统 SHALL 验证解析的记录数量并记录统计信息到结构化日志

### 需求3：智能数据匹配和处理系统

**用户故事：** 作为系统管理员，我希望系统能够智能匹配网页和PDF数据，生成准确的服务名称映射关系，并实现数据的标准化处理。

#### 验收标准

1. WHEN 执行匹配逻辑 THEN 系统 SHALL 使用网页抓取的"提供的服务"字段值作为匹配基准和业务主键
2. WHEN 进行智能匹配 THEN 系统 SHALL 使用高级匹配算法，包括精确匹配、模糊匹配和正则表达式匹配
3. WHEN 匹配成功 THEN 系统 SHALL 获取PDF中对应的"Short name (Subsequent use)"作为服务简称
4. IF 网页服务名称在PDF中未匹配到 THEN 系统 SHALL 将该服务的简称设置为与全称相同
5. WHEN 数据标准化 THEN 系统 SHALL 实施去重算法，确保同一服务不会重复存储
6. WHEN 匹配过程完成 THEN 系统 SHALL 记录匹配成功率和未匹配的服务列表到结构化日志

### 需求4：PostgreSQL v2架构数据存储系统

**用户故事：** 作为系统管理员，我希望同步的服务名称数据能够正确存储到PostgreSQL数据库中，支持增量更新和字段职责分离。

#### 验收标准

1. WHEN 连接数据库 THEN 系统 SHALL 从AWS Secrets Manager获取数据库连接配置（host: postgres-shared-dev.cvsmudg6yyle.rds.cn-northwest-1.amazonaws.com.cn, port: 5432, username: mass_email_translator, password: [敏感信息], database: mass_email_translator）
2. WHEN 数据匹配完成 THEN 系统 SHALL 将网页"Services Offered"值存储为authoritative_full_name字段，作为同步操作的唯一业务键
2. WHEN 存储服务数据 THEN 系统 SHALL 将网页"Services Offered"值存储为full_name_en字段，与authoritative_full_name保持一致
3. WHEN 推导base_name THEN 系统 SHALL 通过移除authoritative_full_name中的括号缩写（使用正则表达式`r'\s*\([^)]*\)'`）生成用于翻译逻辑的规范化基础名称，并存储为base_name字段
4. WHEN 匹配成功 THEN 系统 SHALL 将PDF的"Short name (Subsequent use)"字段进行标准化处理（将所有'AWS'和'aws'字符串替换为'Amazon'）后存储为short_name_en字段
5. IF PDF匹配失败 THEN 系统 SHALL 将short_name_en设置为与authoritative_full_name相同的值
6. WHEN 处理PDF数据 THEN 系统 SHALL 将"AWS offering (internal name)"存储为internal_name字段
7. WHEN 处理PDF数据 THEN 系统 SHALL 基于internal_name生成service_code字段，使用以下规则：转换为小写、移除非字母数字字符、保留中间空格，用于系统内部标识和正则表达式模式分组管理
8. WHEN 更新数据库 THEN 系统 SHALL 设置source字段为'web_scrape'标识数据来源
9. WHEN 执行存储操作 THEN 系统 SHALL 使用ON CONFLICT (authoritative_full_name) DO UPDATE实现UPSERT功能
10. WHEN 更新现有记录 THEN 系统 SHALL 更新last_synced_at时间戳并保持created_at不变
11. WHEN 字段职责分离 THEN 系统 SHALL 确保authoritative_full_name负责数据同步、base_name作为规范化基础名称、full_name_en与short_name_en作为标准化名称字段，供其他系统通过数据库直接访问与使用

#### 字段职责完整说明

**service_code字段的完整说明：**
- **数据来源**：基于PDF中的"AWS offering (internal name)"字段
- **处理规则**：
  - 转换为小写
  - 移除非字母数字字符
  - 保留中间空格（与需求2第4条保持一致）
- **用途说明**：
  - 系统内部标识
  - 正则表达式模式分组管理

**字段职责分离架构：**
- **authoritative_full_name**：数据同步的业务主键
- **base_name**：规范化基础名称，供其他系统使用
- **full_name_en**：完整英文名称，供其他系统使用
- **short_name_en**：简化英文名称，供其他系统使用
- **internal_name**：PDF原始数据保留
- **service_code**：系统内部标识和模式分组管理

### 需求5：8种核心正则表达式模式自动生成系统

**用户故事：** 作为系统管理员，我希望同步过程能够基于服务名称自动生成和维护regex_patterns表中的8种核心正则表达式模式，确保数据库中的模式数据完整、准确并可供其他系统直接使用。

#### 验收标准

1. WHEN 发现新服务或服务名称更新 THEN 系统 SHALL 基于服务名称自动生成8种核心正则表达式模式类型
2. WHEN 生成全称复合后缀模式 THEN 系统 SHALL 创建优先级120的模式，匹配完整服务名称加复杂后缀
3. WHEN 生成全称标准模式 THEN 系统 SHALL 创建优先级115的模式，精确匹配完整服务名称，带边界保护机制
4. WHEN 生成简称复合后缀模式 THEN 系统 SHALL 创建优先级110的模式，匹配简称加复杂后缀
5. WHEN 生成简称标准模式 THEN 系统 SHALL 创建优先级105的模式，精确匹配服务简称，带边界保护
6. WHEN 生成缩写复合后缀模式 THEN 系统 SHALL 创建优先级100的模式，匹配纯缩写加后缀
7. WHEN 生成缩写标准模式 THEN 系统 SHALL 创建优先级95的模式，精确匹配纯缩写，带边界保护
8. WHEN 生成特殊变体模式 THEN 系统 SHALL 创建优先级125的模式，处理特殊服务变体（如Aurora PostgreSQL）
9. WHEN 生成上下文保护模式 THEN 系统 SHALL 创建优先级90的模式，在特定上下文中避免误匹配
10. WHEN 设置优先级 THEN 系统 SHALL 遵循"数值越大优先级越高"原则，确保精确匹配优先于模糊匹配
11. WHEN 生成复合模式 THEN 系统 SHALL 在metadata JSONB字段中标注：isCompoundWithSuffix=true、suffixGroup（捕获组索引）、patternCategory，并在适用时设置hasBoundaryProtection=true
12. WHEN 正则模式生成完成 THEN 系统 SHALL 验证模式的语法有效性并确保related_service_id正确关联

### 需求6：边界保护机制系统

**用户故事：** 作为系统管理员，我希望生成的正则表达式模式具备完善的边界保护机制，避免在ARN、URL、代码标识符等上下文中的误匹配。

#### 验收标准

1. WHEN 实施边界保护 THEN 系统 SHALL 采用v2.0统一边界保护原则，禁止在服务名模式本体中使用可变宽度负向后行，确保Python正则表达式兼容性
2. WHEN 生成ARN保护模式 THEN 系统 SHALL 创建CONTEXT_PROTECTED类型的基线模式：`arn:(?:aws|aws-cn):[^\s]+`，优先级90
3. WHEN 生成URL保护模式 THEN 系统 SHALL 创建CONTEXT_PROTECTED类型的基线模式：`https?://[^\s]+`，优先级90
4. WHEN 生成代码标识符保护 THEN 系统 SHALL 在业务模式本体中仅保留固定宽度的轻量边界保护（如`(?<![:_-])`与`(?![:_-])`）
5. WHEN 处理边界保护 THEN 系统 SHALL 通过CONTEXT_PROTECTED模式类型统一处理上下文边界，在数据库中维护边界保护模式
6. WHEN 验证边界保护 THEN 系统 SHALL 验证所有CONTEXT_PROTECTED类型模式的有效性，将无效模式标记为非活跃状态

### 需求7：AWS Secrets Manager配置管理系统

**用户故事：** 作为系统管理员，我希望系统能够安全地管理敏感配置信息，通过AWS Secrets Manager存储和获取数据库凭证和S3路径配置。

#### 验收标准

1. WHEN 管理敏感配置 THEN 系统 SHALL 使用AWS Secrets Manager存储数据库凭证（host、port、username、password、database）和S3路径配置（snpdf）
2. WHEN 获取配置 THEN 系统 SHALL 实现ConfigManager类，提供配置缓存机制（1小时TTL），减少API调用频率
3. WHEN 解析S3路径 THEN 系统 SHALL 自动解析s3://bucket/key格式的路径并验证有效性
4. WHEN 数据库连接 THEN 系统 SHALL 使用连接池管理数据库连接，支持重试和健康检查
5. WHEN 配置缓存过期 THEN 系统 SHALL 自动刷新缓存并重新获取最新配置信息

### 需求8：结构化日志（最小化）

**用户故事：** 作为系统管理员，我希望系统输出结构化的日志信息（仅限最小化范围），便于手动部署场景下进行基本的故障排查。

#### 验收标准

1. WHEN 同步任务执行 THEN 系统 SHALL 记录结构化的JSON格式日志，包含时间戳、级别、组件、事件类型和上下文信息
2. WHEN 记录日志 THEN 系统 SHALL 使用统一的日志格式和级别管理（INFO、WARN、ERROR）
3. WHEN 发生异常 THEN 系统 SHALL 记录详细的错误信息和异常堆栈到结构化日志中
4. WHEN 记录执行状态 THEN 系统 SHALL 输出关键事件（sync_start、sync_progress、sync_complete、error_occurred）

### 需求9：分级错误处理和重试机制系统

**用户故事：** 作为系统管理员，我希望系统具备完善的错误处理、重试机制和恢复策略，确保系统的稳定性和可靠性。

#### 验收标准

1. WHEN 系统运行 THEN 系统 SHALL 提供分级的错误处理机制，包括网络错误、数据库错误、文件系统错误、解析错误、验证错误和超时错误
2. WHEN 发生可恢复错误 THEN 系统 SHALL 根据错误类型应用相应的重试策略（网络请求3次、数据库操作5次、文件操作3次）
3. WHEN 实施重试 THEN 系统 SHALL 使用指数退避算法，基础延迟1-2秒，最大延迟60秒
4. WHEN 错误分类 THEN 系统 SHALL 根据错误严重程度（LOW、MEDIUM、HIGH、CRITICAL）采取不同的处理策略
5. WHEN 发生严重错误 THEN 系统 SHALL 记录详细的错误上下文和堆栈信息，便于故障排查
6. WHEN 超时管理 THEN 系统 SHALL 提供超时控制（5分钟），防止长时间运行

### 需求10：数据质量保证和验证系统

**用户故事：** 作为系统管理员，我希望同步过程具有完整的数据质量检查和验证机制，确保数据的准确性和完整性。

#### 验收标准

1. WHEN 执行同步任务 THEN 系统 SHALL 验证所有必填字段的完整性（authoritative_full_name、base_name、full_name_en、short_name_en）
2. WHEN 发现数据异常 THEN 系统 SHALL 记录详细的错误日志并继续处理其他数据，不中断整个同步流程
3. WHEN 同步完成 THEN 系统 SHALL 生成同步报告，包含成功、失败和跳过的记录统计
4. IF 关键数据缺失或同步失败 THEN 系统 SHALL 详细记录错误信息到Lambda函数日志中，包含失败原因、上下文数据和调试信息
5. WHEN 数据验证失败 THEN 系统 SHALL 保持现有数据不变并记录验证失败原因
6. WHEN 检测数据一致性 THEN 系统 SHALL 验证外键关系和数据完整性约束

### 需求11：批量操作优化和性能管理系统

**用户故事：** 作为系统管理员，我希望同步系统能够高效处理大量数据，确保同步操作的性能和稳定性。

#### 验收标准

1. WHEN 处理大量服务数据 THEN 系统 SHALL 使用psycopg2.extras.execute_values进行批量插入操作，提高数据库写入性能
2. WHEN 执行批量操作 THEN 系统 SHALL 使用数据库事务确保操作的原子性，支持回滚机制
3. WHEN 批量操作失败 THEN 系统 SHALL 回退到单条记录处理模式并记录失败原因
4. WHEN 处理大数据集 THEN 系统 SHALL 使用分页处理（每批100条记录）避免内存溢出
5. WHEN 执行数据库操作 THEN 系统 SHALL 使用连接池管理数据库连接，提高连接复用效率

6. WHEN 读取活跃规则 THEN 系统 SHOULD 利用部分索引 idx_regex_patterns_active_valid 与稳定排序（ORDER BY priority DESC, id ASC）确保查询性能


### 需求12：正则表达式模式语法验证系统

**用户故事：** 作为系统管理员，我希望系统能够验证生成的正则表达式模式的语法正确性，确保模式能够正常使用。

#### 验收标准

1. WHEN 生成正则模式 THEN 系统 SHALL 验证所有模式的语法正确性，将无效模式标记为'invalid'状态，并详细记录验证过程到Lambda函数日志中，包含模式名称、正则表达式内容、验证结果、错误原因和相关服务信息
2. WHEN 模式语法验证通过 THEN 系统 SHALL 将该模式的validation_status更新为'valid'；WHEN 验证失败 THEN 系统 SHALL 将validation_status更新为'invalid'并记录失败原因（包含错误栈与上下文）
3. WHEN 写入或更新正则 THEN 系统 SHALL 保证regex_string长度小于10000字符，确保与数据库约束一致
4. WHEN 插入或更新模式 THEN 系统 SHALL 设置is_active为TRUE（除非生命周期管理将其标记为非活跃）

### 需求13：部署和运维文档

**用户故事：** 作为DevOps工程师，我希望有简洁的手动部署说明手册（前提条件、环境变量、Secrets示例与最小权限IAM清单），以便正确部署并进行基本验证。

#### 验收标准

1. WHEN 需要部署指导 THEN 系统 SHALL 提供简洁的手动部署说明手册，包含以下内容：
   - Lambda函数、最小权限IAM角色和策略的手动创建和配置步骤
   - EventBridge调度器的手动创建和配置步骤，包括灵活的调度表达式（cron格式）配置、重试策略配置、手动触发和定时触发模式设置
   - AWS Secrets Manager配置示例，包含数据库连接信息（host: postgres-shared-dev.cvsmudg6yyle.rds.cn-northwest-1.amazonaws.com.cn, port: 5432, username: mass_email_translator, password: [敏感信息], database: mass_email_translator）和PDF文档路径（snpdf: s3://mass-email-translator-feedback/awsofferingnames_pdf/AWSOfferingNames05032024.pdf）
   - EventBridge测试事件模板，用于在AWS控制台中手动触发Lambda函数
   - CloudWatch日志分析指导，验证Lambda函数执行结果
   - （可选）CloudWatch日志查看说明，用于手动部署场景下的基本日志排查
   - 详细的故障排查指导和常见问题解决方案


### 需求14：同步操作历史记录系统

**用户故事：** 作为系统管理员，我希望系统能够记录同步操作的历史，便于监控和故障排查。

#### 验收标准

1. WHEN 执行同步操作 THEN 系统 SHALL 记录所有同步操作的历史，包括时间戳、操作类型和结果到结构化日志

### 需求15：正则表达式模式关联管理系统

**用户故事：** 作为系统管理员，我希望系统能够正确管理正则表达式模式与服务的关联关系，确保数据一致性。

#### 验收标准

1. WHEN 创建新模式 THEN 系统 SHALL 自动生成完整的8种核心模式类型，确保覆盖所有使用场景
2. WHEN 更新服务名称 THEN 系统 SHALL 同步更新相关的regex_patterns记录，保持关联关系正确
3. IF 服务被删除 THEN 系统 SHALL 将相关的regex_patterns记录标记为非活跃状态


### 需求16：数据库中介模式系统

**用户故事：** 作为系统架构师，我希望AWS服务同步模块采用数据库中介模式，确保模块间的完全解耦和独立性。

#### 验收标准

1. WHEN 模块运行 THEN 系统 SHALL 作为完全独立的数据同步服务运行，不提供任何对外接口
2. WHEN 其他系统需要数据 THEN 系统 SHALL 确保其他系统直接从数据库获取所需数据，而不是通过模块接口
3. WHEN 数据存储完成 THEN 系统 SHALL 通过数据库为其他系统提供标准化的数据访问，实现模块间的完全解耦
4. WHEN 模块部署 THEN 系统 SHALL 支持完全独立的部署、运行和维护，不依赖其他模块的存在
5. WHEN 数据更新 THEN 系统 SHALL 通过数据库事务确保数据的一致性和完整性
6. WHEN 模块通信 THEN 系统 SHALL 仅通过数据库进行模块间的数据交换，避免直接的模块依赖

### 需求16A：空表初始化/全量重置（并发与一致性）

**用户故事：** 作为系统管理员，我希望当 service_names 与 regex_patterns 两表为空时，系统能够以并发安全、原子一致的方式完成初始化；当需要全量重置时，能在外部备份后安全执行。

#### 验收标准

1. WHEN 判断空表 THEN 系统 SHALL 在同一连接中对两表 COUNT 均为 0 才视为空表
2. WHEN 执行初始化/全量重置 THEN 系统 SHALL 在事务内获取 advisory lock（pg_try_advisory_xact_lock），防止并发全量操作
3. WHEN 获取锁后 THEN 系统 SHALL 再次在锁内二次判空；若非空则终止并提示走“外部备份+全量重置”流程
4. WHEN 执行写入 THEN 系统 SHALL 严格先写 service_names，再写 regex_patterns；不得使用 session_replication_role
5. WHEN 关联 SERVICE_NAME 模式 THEN 系统 SHALL 对 active 且 related_service_id IS NULL 的孤儿模式实施硬失败并回滚
6. WHEN 初始化完成 THEN 系统 SHALL 执行 ANALYZE service_names 与 regex_patterns

### 需求17：模块完全独立性系统

**用户故事：** 作为DevOps工程师，我希望AWS服务同步模块具有完全的独立性，能够独立部署、运行和维护。

#### 验收标准

1. WHEN 模块设计 THEN 系统 SHALL 遵循单一职责原则，专注于数据同步核心功能
2. WHEN 模块部署 THEN 系统 SHALL 支持完全独立的部署，不依赖其他模块的配置或状态
3. WHEN 模块运行 THEN 系统 SHALL 能够独立运行，不需要其他模块的协调或支持
4. WHEN 模块维护 THEN 系统 SHALL 支持独立的维护和更新，不影响其他模块的运行
5. WHEN 模块运行 THEN 系统 SHALL 提供最小化的结构化日志输出，便于手动部署场景下的基本故障排查

