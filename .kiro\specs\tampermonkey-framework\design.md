# 油猴脚本通用框架设计文档

## 概述

油猴脚本通用框架是一个为AWS中国区Mass Email系统设计的标准化脚本开发框架，提供统一的页面内容检测、脚本生命周期管理、错误处理和性能优化机制。该框架采用模块化设计，支持多种脚本类型，并确保脚本只在相关页面上激活，从而提高性能和用户体验。

## 架构

### 系统架构图

```mermaid
graph TB
    A[页面加载] --> B[框架初始化]
    B --> C[配置管理器]
    B --> D[内容检测引擎]
    
    C --> D
    D --> E{检测结果}
    
    E -->|成功| F[脚本激活器]
    E -->|失败| G[资源清理]
    
    F --> H[功能模块1]
    F --> I[功能模块2]
    F --> J[功能模块N]
    
    H --> K[事件总线]
    I --> K
    J --> K
    
    K --> L[性能监控器]
    K --> M[错误处理器]
    
    G --> N[执行完成]
    L --> N
    M --> N
```

### 核心组件

1. **配置管理器 (ConfigurationManager)**
   - 管理框架和脚本配置
   - 提供默认值和配置验证
   - 支持运行时配置更新

2. **内容检测引擎 (ContentDetectionEngine)**
   - 执行页面内容检测
   - 支持多种检测策略
   - 处理动态内容和异步加载

3. **脚本激活器 (ScriptActivator)**
   - 管理脚本生命周期
   - 处理激活和停用逻辑
   - 协调多个功能模块

4. **事件总线 (EventBus)**
   - 提供组件间通信
   - 支持事件订阅和发布
   - 实现松耦合架构

5. **性能监控器 (PerformanceMonitor)**
   - 跟踪资源使用情况
   - 提供性能指标和报告
   - 实施性能优化策略

6. **错误处理器 (ErrorHandler)**
   - 捕获和处理异常
   - 提供错误日志和报告
   - 实施恢复和降级策略

## 组件和接口

### 配置管理器

```javascript
class ConfigurationManager {
    constructor(userConfig = {}) {
        this.config = this.mergeWithDefaults(userConfig);
        this.validators = new Map();
        this.initializeValidators();
    }
    
    mergeWithDefaults(userConfig) {
        const defaultConfig = {
            // 内容检测配置
            contentDetection: {
                targets: ['CN Mass Email'],
                strategies: ['textContent', 'innerText', 'visibleText'],
                maxAttempts: 10,
                checkInterval: 1000,
                initialDelay: 2000,
                timeout: 30000,
                dynamicDetection: true
            },
            // 性能配置
            performance: {
                debounceDelay: 500,
                maxObserverNodes: 1000,
                useThrottling: true,
                throttleDelay: 300
            },
            // 调试配置
            debug: {
                enabled: false,
                logLevel: 'info', // 'debug', 'info', 'warn', 'error'
                consoleOutput: true,
                performanceMetrics: false
            },
            // 多脚本协调配置
            coordination: {
                namespace: 'awsMassEmailScript',
                shareDetectionResults: true,
                isolationLevel: 'medium' // 'low', 'medium', 'high'
            }
        };
        
        return this.deepMerge(defaultConfig, userConfig);
    }
    
    // 其他方法...
}
```

### 内容检测引擎

```javascript
class ContentDetectionEngine {
    constructor(config, eventBus) {
        this.config = config;
        this.eventBus = eventBus;
        this.detectors = new Map();
        this.attempts = 0;
        this.timerId = null;
        this.observer = null;
        this.detectionStartTime = 0;
        this.initializeDetectors();
    }
    
    initializeDetectors() {
        // 注册检测策略
        this.registerDetector('textContent', this.detectInTextContent.bind(this));
        this.registerDetector('innerText', this.detectInInnerText.bind(this));
        this.registerDetector('visibleText', this.detectInVisibleText.bind(this));
        this.registerDetector('selector', this.detectBySelector.bind(this));
        this.registerDetector('regex', this.detectByRegex.bind(this));
    }
    
    registerDetector(name, detectorFn) {
        this.detectors.set(name, detectorFn);
    }
    
    async startDetection() {
        this.detectionStartTime = performance.now();
        this.eventBus.publish('detection:started', { timestamp: this.detectionStartTime });
        
        // 初始延迟
        await this.delay(this.config.contentDetection.initialDelay);
        
        // 执行检测
        return this.runDetectionCycle();
    }
    
    async runDetectionCycle() {
        // 检查是否超时
        if (performance.now() - this.detectionStartTime > this.config.contentDetection.timeout) {
            this.eventBus.publish('detection:timeout', { 
                duration: performance.now() - this.detectionStartTime,
                attempts: this.attempts
            });
            return false;
        }
        
        // 执行所有启用的检测策略
        const result = await this.executeDetectionStrategies();
        
        if (result) {
            this.eventBus.publish('detection:success', { 
                target: result.target,
                strategy: result.strategy,
                duration: performance.now() - this.detectionStartTime,
                attempts: this.attempts
            });
            
            // 如果配置了动态检测，启动观察器
            if (this.config.contentDetection.dynamicDetection) {
                this.startDynamicDetection();
            }
            
            return true;
        }
        
        // 增加尝试次数
        this.attempts++;
        
        // 检查是否达到最大尝试次数
        if (this.attempts >= this.config.contentDetection.maxAttempts) {
            this.eventBus.publish('detection:failed', { 
                duration: performance.now() - this.detectionStartTime,
                attempts: this.attempts
            });
            return false;
        }
        
        // 安排下一次检测
        const interval = this.calculateNextInterval();
        await this.delay(interval);
        
        // 递归执行下一次检测
        return this.runDetectionCycle();
    }
    
    // 其他方法...
}
```

### 脚本激活器

```javascript
class ScriptActivator {
    constructor(config, eventBus) {
        this.config = config;
        this.eventBus = eventBus;
        this.modules = new Map();
        this.isActive = false;
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        this.eventBus.subscribe('detection:success', this.activateScript.bind(this));
        this.eventBus.subscribe('detection:failed', this.handleDetectionFailure.bind(this));
        this.eventBus.subscribe('detection:timeout', this.handleDetectionTimeout.bind(this));
    }
    
    registerModule(name, moduleInstance) {
        this.modules.set(name, moduleInstance);
        return this;
    }
    
    activateScript(detectionResult) {
        if (this.isActive) return;
        
        this.isActive = true;
        this.eventBus.publish('script:activating', { timestamp: performance.now() });
        
        // 按顺序激活所有模块
        const activationPromises = Array.from(this.modules.entries()).map(async ([name, module]) => {
            try {
                await module.activate(detectionResult);
                this.eventBus.publish('module:activated', { name, timestamp: performance.now() });
                return { name, success: true };
            } catch (error) {
                this.eventBus.publish('module:activation:error', { name, error });
                return { name, success: false, error };
            }
        });
        
        Promise.all(activationPromises).then(results => {
            const allSuccessful = results.every(r => r.success);
            
            if (allSuccessful) {
                this.eventBus.publish('script:activated', { 
                    timestamp: performance.now(),
                    modules: results.map(r => r.name)
                });
            } else {
                const failedModules = results.filter(r => !r.success).map(r => r.name);
                this.eventBus.publish('script:activation:partial', { 
                    timestamp: performance.now(),
                    failedModules
                });
            }
        });
    }
    
    // 其他方法...
}
```

### 事件总线

```javascript
class EventBus {
    constructor() {
        this.subscribers = new Map();
    }
    
    subscribe(event, callback) {
        if (!this.subscribers.has(event)) {
            this.subscribers.set(event, []);
        }
        
        this.subscribers.get(event).push(callback);
        return () => this.unsubscribe(event, callback);
    }
    
    unsubscribe(event, callback) {
        if (!this.subscribers.has(event)) return false;
        
        const callbacks = this.subscribers.get(event);
        const index = callbacks.indexOf(callback);
        
        if (index !== -1) {
            callbacks.splice(index, 1);
            return true;
        }
        
        return false;
    }
    
    publish(event, data = {}) {
        if (!this.subscribers.has(event)) return;
        
        const callbacks = this.subscribers.get(event);
        callbacks.forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error(`Error in event handler for ${event}:`, error);
            }
        });
    }
    
    // 其他方法...
}
```

## 数据模型

### 配置模型

```javascript
/**
 * 框架配置模型
 */
const FrameworkConfig = {
    // 内容检测配置
    contentDetection: {
        // 检测目标（可以是字符串或正则表达式）
        targets: ['CN Mass Email'],
        
        // 检测策略
        strategies: ['textContent', 'innerText', 'visibleText'],
        
        // 检测参数
        maxAttempts: 10,           // 最大检测次数
        checkInterval: 1000,       // 基础检测间隔（毫秒）
        initialDelay: 2000,        // 初始延迟（毫秒）
        timeout: 30000,            // 检测超时（毫秒）
        
        // 动态检测配置
        dynamicDetection: true,    // 是否启用动态检测
        observerConfig: {          // MutationObserver配置
            childList: true,
            subtree: true,
            characterData: true,
            attributes: false
        }
    },
    
    // 性能配置
    performance: {
        debounceDelay: 500,        // 防抖延迟（毫秒）
        throttleDelay: 300,        // 节流延迟（毫秒）
        maxObserverNodes: 1000,    // 最大观察节点数
        useThrottling: true        // 是否使用节流
    },
    
    // 调试配置
    debug: {
        enabled: false,            // 是否启用调试
        logLevel: 'info',          // 日志级别
        consoleOutput: true,       // 是否输出到控制台
        performanceMetrics: false  // 是否收集性能指标
    },
    
    // 多脚本协调配置
    coordination: {
        namespace: 'awsMassEmailScript',  // 命名空间
        shareDetectionResults: true,      // 是否共享检测结果
        isolationLevel: 'medium'          // 隔离级别
    }
};
```

### 检测结果模型

```javascript
/**
 * 内容检测结果模型
 */
class DetectionResult {
    constructor(success, data = {}) {
        this.success = success;
        this.timestamp = performance.now();
        this.target = data.target || null;
        this.strategy = data.strategy || null;
        this.element = data.element || null;
        this.attempts = data.attempts || 0;
        this.duration = data.duration || 0;
        this.error = data.error || null;
    }
    
    toJSON() {
        return {
            success: this.success,
            timestamp: this.timestamp,
            target: this.target,
            strategy: this.strategy,
            element: this.element ? {
                tagName: this.element.tagName,
                id: this.element.id,
                className: this.element.className
            } : null,
            attempts: this.attempts,
            duration: this.duration,
            error: this.error ? {
                name: this.error.name,
                message: this.error.message
            } : null
        };
    }
}
```

## 错误处理

### 错误分类和处理策略

1. **配置错误**
   - 无效配置: 使用默认值，记录警告
   - 配置冲突: 应用优先级规则，记录警告
   - 缺失必要配置: 抛出错误，阻止初始化

2. **检测错误**
   - DOM访问错误: 尝试备用策略，记录警告
   - 超时错误: 停止检测，记录错误
   - 策略执行错误: 跳过当前策略，尝试下一个

3. **激活错误**
   - 模块激活失败: 尝试继续激活其他模块，记录错误
   - 资源加载失败: 重试或使用备用资源，记录警告
   - 权限错误: 降级功能，记录错误

### 错误处理器

```javascript
class ErrorHandler {
    constructor(config, eventBus) {
        this.config = config;
        this.eventBus = eventBus;
        this.errors = [];
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // 监听各种错误事件
        this.eventBus.subscribe('error:configuration', this.handleConfigError.bind(this));
        this.eventBus.subscribe('error:detection', this.handleDetectionError.bind(this));
        this.eventBus.subscribe('error:activation', this.handleActivationError.bind(this));
        this.eventBus.subscribe('error:runtime', this.handleRuntimeError.bind(this));
        
        // 全局错误处理
        if (typeof window !== 'undefined') {
            window.addEventListener('error', this.handleGlobalError.bind(this));
            window.addEventListener('unhandledrejection', this.handleUnhandledRejection.bind(this));
        }
    }
    
    handleConfigError(error) {
        this.logError('Configuration', error);
        
        // 尝试应用默认配置
        if (error.key && error.defaultValue !== undefined) {
            this.eventBus.publish('config:apply-default', {
                key: error.key,
                value: error.defaultValue
            });
            return true;
        }
        
        return false;
    }
    
    handleDetectionError(error) {
        this.logError('Detection', error);
        
        // 检查是否有备用策略
        if (error.strategy && this.config.contentDetection.strategies.length > 1) {
            const currentIndex = this.config.contentDetection.strategies.indexOf(error.strategy);
            if (currentIndex !== -1 && currentIndex < this.config.contentDetection.strategies.length - 1) {
                // 尝试下一个策略
                this.eventBus.publish('detection:try-next-strategy', {
                    failedStrategy: error.strategy,
                    nextStrategy: this.config.contentDetection.strategies[currentIndex + 1]
                });
                return true;
            }
        }
        
        return false;
    }
    
    // 其他方法...
}
```

## 测试策略

### 单元测试

1. **配置管理器测试**
   - 配置合并和验证
   - 默认值处理
   - 运行时配置更新

2. **内容检测引擎测试**
   - 各种检测策略的准确性
   - 超时和重试机制
   - 动态内容处理

3. **脚本激活器测试**
   - 模块注册和激活
   - 错误处理和恢复
   - 生命周期事件

### 集成测试

1. **端到端流程测试**
   - 完整的检测和激活流程
   - 多模块协作
   - 错误恢复机制

2. **性能测试**
   - 资源使用监控
   - 响应时间测量
   - 内存泄漏检测

### 测试用例示例

```javascript
describe('ContentDetectionEngine', () => {
    let engine;
    let eventBus;
    let config;
    
    beforeEach(() => {
        // 设置测试环境
        document.body.innerHTML = '';
        eventBus = new EventBus();
        config = new ConfigurationManager({
            contentDetection: {
                targets: ['Test Content'],
                strategies: ['textContent'],
                maxAttempts: 3,
                checkInterval: 100,
                initialDelay: 0,
                timeout: 1000
            }
        }).getConfig();
        
        engine = new ContentDetectionEngine(config, eventBus);
    });
    
    test('should detect content when present', async () => {
        // 添加测试内容到页面
        document.body.innerHTML = '<div>Test Content</div>';
        
        // 监听检测成功事件
        const successPromise = new Promise(resolve => {
            eventBus.subscribe('detection:success', resolve);
        });
        
        // 启动检测
        engine.startDetection();
        
        // 等待检测成功
        const result = await successPromise;
        
        expect(result).toBeDefined();
        expect(result.target).toBe('Test Content');
        expect(result.strategy).toBe('textContent');
    });
    
    test('should fail detection after max attempts', async () => {
        // 页面不包含测试内容
        document.body.innerHTML = '<div>Other Content</div>';
        
        // 监听检测失败事件
        const failurePromise = new Promise(resolve => {
            eventBus.subscribe('detection:failed', resolve);
        });
        
        // 启动检测
        engine.startDetection();
        
        // 等待检测失败
        const result = await failurePromise;
        
        expect(result).toBeDefined();
        expect(result.attempts).toBe(3);
    });
    
    // 更多测试...
});
```

## 性能优化

### 检测算法优化

1. **文本搜索优化**
   - 使用 indexOf 而非正则表达式（除非必要）
   - 实现早期退出机制
   - 缓存中间结果

2. **DOM操作优化**
   - 最小化DOM查询
   - 使用文档片段
   - 批量处理DOM更新

3. **资源管理优化**
   - 及时清理不再需要的资源
   - 使用弱引用存储DOM元素
   - 限制并发操作数量

### 性能监控

```javascript
class PerformanceMonitor {
    constructor(config, eventBus) {
        this.config = config;
        this.eventBus = eventBus;
        this.metrics = {
            detectionTime: [],
            memoryUsage: [],
            domOperations: 0,
            eventHandlers: 0
        };
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // 监听性能相关事件
        this.eventBus.subscribe('detection:started', this.onDetectionStarted.bind(this));
        this.eventBus.subscribe('detection:success', this.onDetectionCompleted.bind(this));
        this.eventBus.subscribe('detection:failed', this.onDetectionCompleted.bind(this));
        this.eventBus.subscribe('script:activated', this.onScriptActivated.bind(this));
        
        // 定期收集内存使用情况
        if (this.config.debug.performanceMetrics && performance.memory) {
            this.memoryInterval = setInterval(() => {
                this.collectMemoryMetrics();
            }, 5000);
        }
    }
    
    onDetectionStarted(data) {
        this.detectionStartTime = data.timestamp;
    }
    
    onDetectionCompleted(data) {
        const duration = data.duration || (performance.now() - this.detectionStartTime);
        this.metrics.detectionTime.push({
            timestamp: performance.now(),
            duration: duration,
            attempts: data.attempts,
            success: 'target' in data
        });
        
        // 限制存储的指标数量
        if (this.metrics.detectionTime.length > 10) {
            this.metrics.detectionTime.shift();
        }
        
        // 发布性能指标
        this.eventBus.publish('performance:metrics:detection', {
            duration: duration,
            attempts: data.attempts,
            averageTime: this.calculateAverageDetectionTime()
        });
    }
    
    // 其他方法...
}
```

## 安全考虑

### 代码安全

1. **输入验证**
   - 验证所有配置参数
   - 清理和转义用户输入
   - 防止XSS攻击

2. **权限控制**
   - 最小权限原则
   - 避免访问敏感DOM元素
   - 限制网络请求范围

3. **数据安全**
   - 避免存储敏感信息
   - 使用安全的存储机制
   - 清理不再需要的数据

### 安全实践

```javascript
class SecurityManager {
    constructor(config, eventBus) {
        this.config = config;
        this.eventBus = eventBus;
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // 监听安全相关事件
        this.eventBus.subscribe('security:validate-config', this.validateConfig.bind(this));
        this.eventBus.subscribe('security:validate-input', this.validateInput.bind(this));
        this.eventBus.subscribe('security:validate-url', this.validateUrl.bind(this));
    }
    
    validateConfig(config) {
        // 验证配置安全性
        const issues = [];
        
        // 检查命名空间
        if (config.coordination && config.coordination.namespace) {
            if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(config.coordination.namespace)) {
                issues.push({
                    key: 'coordination.namespace',
                    message: 'Namespace must be alphanumeric and start with a letter',
                    severity: 'warning'
                });
            }
        }
        
        // 检查其他安全相关配置
        // ...
        
        return {
            valid: issues.length === 0,
            issues
        };
    }
    
    validateInput(input, context) {
        // 根据上下文验证输入
        switch (context) {
            case 'selector':
                return this.validateSelector(input);
            case 'url':
                return this.validateUrl(input);
            case 'html':
                return this.sanitizeHtml(input);
            default:
                return this.sanitizeGeneral(input);
        }
    }
    
    // 其他方法...
}
```

## 多脚本协调

### 命名空间管理

```javascript
class NamespaceManager {
    constructor(namespace) {
        this.namespace = namespace;
        this.ensureGlobalNamespace();
    }
    
    ensureGlobalNamespace() {
        if (typeof window === 'undefined') return;
        
        window.__TM_NAMESPACES = window.__TM_NAMESPACES || {};
        window.__TM_NAMESPACES[this.namespace] = window.__TM_NAMESPACES[this.namespace] || {
            instances: [],
            sharedData: {},
            detectionResults: {}
        };
    }
    
    registerInstance(instance) {
        if (typeof window === 'undefined') return;
        
        window.__TM_NAMESPACES[this.namespace].instances.push({
            id: instance.id,
            version: instance.version,
            registeredAt: Date.now()
        });
    }
    
    shareDetectionResult(target, result) {
        if (typeof window === 'undefined') return;
        
        window.__TM_NAMESPACES[this.namespace].detectionResults[target] = {
            result,
            timestamp: Date.now()
        };
    }
    
    getSharedDetectionResult(target) {
        if (typeof window === 'undefined') return null;
        
        const shared = window.__TM_NAMESPACES[this.namespace].detectionResults[target];
        
        if (!shared) return null;
        
        // 检查结果是否过期（5秒内有效）
        if (Date.now() - shared.timestamp > 5000) {
            delete window.__TM_NAMESPACES[this.namespace].detectionResults[target];
            return null;
        }
        
        return shared.result;
    }
    
    // 其他方法...
}
```

### 消息传递

```javascript
class MessageBus {
    constructor(namespace, eventBus) {
        this.namespace = namespace;
        this.eventBus = eventBus;
        this.setupMessageChannel();
    }
    
    setupMessageChannel() {
        if (typeof window === 'undefined') return;
        
        // 创建或获取命名空间的消息通道
        window.__TM_MESSAGE_CHANNELS = window.__TM_MESSAGE_CHANNELS || {};
        
        if (!window.__TM_MESSAGE_CHANNELS[this.namespace]) {
            window.__TM_MESSAGE_CHANNELS[this.namespace] = new BroadcastChannel(`tm-${this.namespace}`);
        }
        
        this.channel = window.__TM_MESSAGE_CHANNELS[this.namespace];
        
        // 监听消息
        this.channel.onmessage = (event) => {
            const { type, data, sender } = event.data;
            
            // 忽略自己发送的消息
            if (sender === this.id) return;
            
            // 转发到事件总线
            this.eventBus.publish(`message:${type}`, {
                data,
                sender,
                timestamp: Date.now()
            });
        };
        
        // 生成唯一ID
        this.id = `${this.namespace}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    
    sendMessage(type, data) {
        if (!this.channel) return false;
        
        this.channel.postMessage({
            type,
            data,
            sender: this.id,
            timestamp: Date.now()
        });
        
        return true;
    }
    
    // 其他方法...
}
```

## 应用场景示例

### 反馈收集脚本

```javascript
// 初始化框架
const tampermonkey = new TampermonkeyFramework({
    contentDetection: {
        targets: ['CN Mass Email'],
        strategies: ['textContent', 'innerText', 'visibleText']
    }
});

// 注册反馈模块
tampermonkey.registerModule('feedback', {
    activate: function(detectionResult) {
        // 创建反馈按钮
        const button = document.createElement('button');
        button.id = 'mass-email-feedback-btn';
        button.textContent = 'Feedback';
        button.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #ff9900;
            color: #232f3e;
            border: none;
            border-radius: 5px;
            padding: 10px 15px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            z-index: 9999;
        `;
        
        // 添加点击事件
        button.onclick = this.showFeedbackModal;
        
        // 添加到页面
        document.body.appendChild(button);
        
        return Promise.resolve();
    },
    
    showFeedbackModal: function() {
        // 实现反馈模态框
        // ...
    },
    
    submitFeedback: function(data) {
        // 实现反馈提交
        // ...
    }
});

// 启动框架
tampermonkey.start();
```

### 翻译辅助脚本

```javascript
// 初始化框架
const tampermonkey = new TampermonkeyFramework({
    contentDetection: {
        targets: ['CN Mass Email'],
        strategies: ['textContent', 'innerText', 'visibleText']
    }
});

// 注册翻译辅助模块
tampermonkey.registerModule('translationHelper', {
    activate: function(detectionResult) {
        // 查找需要翻译的元素
        const elements = this.findTranslatableElements();
        
        // 添加翻译辅助功能
        elements.forEach(element => {
            this.enhanceElement(element);
        });
        
        // 添加全局翻译工具栏
        this.addTranslationToolbar();
        
        return Promise.resolve();
    },
    
    findTranslatableElements: function() {
        // 实现可翻译元素查找
        // ...
    },
    
    enhanceElement: function(element) {
        // 实现元素增强
        // ...
    },
    
    addTranslationToolbar: function() {
        // 实现翻译工具栏
        // ...
    }
});

// 启动框架
tampermonkey.start();
```

## 部署和集成

### 框架打包

```javascript
// 框架入口
class TampermonkeyFramework {
    constructor(userConfig = {}) {
        this.version = '1.0.0';
        this.id = `tm-framework-${Date.now()}`;
        
        // 初始化组件
        this.eventBus = new EventBus();
        this.config = new ConfigurationManager(userConfig).getConfig();
        this.namespaceManager = new NamespaceManager(this.config.coordination.namespace);
        this.errorHandler = new ErrorHandler(this.config, this.eventBus);
        this.contentDetector = new ContentDetectionEngine(this.config, this.eventBus);
        this.scriptActivator = new ScriptActivator(this.config, this.eventBus);
        this.performanceMonitor = new PerformanceMonitor(this.config, this.eventBus);
        
        // 注册实例
        this.namespaceManager.registerInstance(this);
    }
    
    registerModule(name, moduleInstance) {
        this.scriptActivator.registerModule(name, moduleInstance);
        return this;
    }
    
    async start() {
        try {
            // 检查是否已有共享的检测结果
            if (this.config.coordination.shareDetectionResults) {
                const sharedResult = this.namespaceManager.getSharedDetectionResult(
                    this.config.contentDetection.targets[0]
                );
                
                if (sharedResult) {
                    this.eventBus.publish('detection:shared-result', sharedResult);
                    this.scriptActivator.activateScript(sharedResult);
                    return true;
                }
            }
            
            // 启动内容检测
            const result = await this.contentDetector.startDetection();
            
            // 共享检测结果
            if (result && this.config.coordination.shareDetectionResults) {
                this.namespaceManager.shareDetectionResult(
                    this.config.contentDetection.targets[0],
                    result
                );
            }
            
            return result;
        } catch (error) {
            this.eventBus.publish('error:startup', error);
            return false;
        }
    }
    
    // 其他方法...
}

// 导出框架
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TampermonkeyFramework;
} else if (typeof window !== 'undefined') {
    window.TampermonkeyFramework = TampermonkeyFramework;
}
```

### 使用示例

```javascript
// ==UserScript==
// @name         AWS Mass Email Helper
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  Helper script for AWS Mass Email system
// <AUTHOR> Team
// @match        https://issues.cn-northwest-1.amazonaws.cn/issues/*
// @grant        GM_xmlhttpRequest
// @require      https://cdn.example.com/tampermonkey-framework.min.js
// ==/UserScript==

(function() {
    'use strict';
    
    // 初始化框架
    const tampermonkey = new TampermonkeyFramework({
        contentDetection: {
            targets: ['CN Mass Email'],
            strategies: ['textContent', 'innerText', 'visibleText']
        },
        debug: {
            enabled: true,
            logLevel: 'info'
        }
    });
    
    // 注册功能模块
    tampermonkey.registerModule('feedback', FeedbackModule);
    tampermonkey.registerModule('translation', TranslationModule);
    tampermonkey.registerModule('dataSync', DataSyncModule);
    
    // 启动框架
    tampermonkey.start().then(result => {
        if (result) {
            console.log('AWS Mass Email Helper activated successfully');
        } else {
            console.log('AWS Mass Email Helper not activated (not on a relevant page)');
        }
    });
})();
```