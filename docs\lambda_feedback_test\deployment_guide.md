# 部署指南：用户反馈收集系统 (Lambda + API Gateway)

本文档详细说明了如何部署用于收集用户反馈的后端服务。该系统由一个 S3 存储桶、一个 Lambda 函数和一个 API Gateway HTTP API 组成，前端通过一个 Tampermonkey 油猴脚本与后端交互。

## 1. 架构概览

1.  **前端 (油猴脚本)**: 在目标页面 (`issues.cn-northwest-1.amazonaws.cn`) 注入一个 "Feedback" 按钮。
2.  **API Gateway**: 提供一个公共的 HTTP `GET` 端点，用于触发 Lambda 函数。
3.  **Lambda 函数**:
    *   接收到请求后，生成一个有时效性的 S3 预签名 URL (Presigned URL)。
    *   将该 URL 返回给前端。
4.  **前端 (油猴脚本)**:
    *   使用获取到的预签名 URL，通过 `PUT` 请求直接将包含反馈内容的 JSON 文件上传到 S3。
5.  **S3 存储桶**: 存储所有用户提交的反馈 JSON 文件。

---

## 2. 后端部署步骤

### 第 1 步：创建 S3 存储桶

这是所有反馈数据的最终归宿。

1.  登录 AWS 管理控制台，导航到 **S3** 服务。
2.  创建一个新的存储桶（例如 `lianhe-mass-email-feedback-test`）。请确保存储桶名称全局唯一。
3.  **配置 CORS**：这是允许前端脚本直接上传文件到 S3 的关键一步。
    *   进入你创建的存储桶，点击 **"权限" (Permissions)** 标签页。
    *   向下滚动到 **"跨源资源共享 (CORS)"** 部分，点击"编辑"。
    *   将以下 JSON 粘贴进去，并将 `AllowedOrigins` 中的 URL 替换为你的前端页面地址：

    ```json
    [
        {
            "AllowedHeaders": [
                "*"
            ],
            "AllowedMethods": [
                "PUT",
                "POST"
            ],
            "AllowedOrigins": [
                "https://issues.cn-northwest-1.amazonaws.cn"
            ],
            "ExposeHeaders": []
        }
    ]
    ```
4.  保存更改。

### 第 2 步：创建 IAM 角色

Lambda 函数需要权限才能向 S3 桶中写入对象。

1.  导航到 **IAM** 服务，选择 **"角色" (Roles)**，然后点击 **"创建角色" (Create role)**。
2.  **受信任的实体类型**: 选择 **"AWS 服务"**。
3.  **使用案例**: 选择 **"Lambda"**，然后点击"下一步"。
4.  **添加权限**:
    *   搜索并添加 `AWSLambdaBasicExecutionRole` 策略（用于写入日志到 CloudWatch）。
    *   点击 **"创建策略" (Create policy)**，打开新标签页。
        *   选择 **JSON** 标签页。
        *   粘贴以下策略，并将 `YOUR_BUCKET_NAME` 替换为你在第1步中创建的S3桶名：
        ```json
        {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Effect": "Allow",
                    "Action": "s3:PutObject",
                    "Resource": "arn:aws:s3:::YOUR_BUCKET_NAME/feedback/*"
                }
            ]
        }
        ```
        *   完成策略的创建（给它一个名字，如 `AllowFeedbackUploadToS3`）。
    *   回到创建角色的页面，刷新权限列表，然后搜索并添加你刚刚创建的 `AllowFeedbackUploadToS3` 策略。
5.  完成角色的创建（给它一个名字，如 `LambdaFeedbackUploadRole`）。

### 第 3 步：部署 Lambda 函数

这是处理业务逻辑的核心。

1.  导航到 **Lambda** 服务，点击 **"创建函数" (Create function)**。
2.  选择 **"从头开始创作" (Author from scratch)**。
3.  **函数名称**: 例如 `mass-email-feedback-presigner`。
4.  **运行时**: 选择 **Python 3.9** (或更高版本)。
5.  **架构**: 选择 `x86_64`。
6.  **权限**: 展开 "更改默认执行角色"，选择 **"使用现有角色"**，然后从下拉列表中选择你在第2步中创建的 `LambdaFeedbackUploadRole`。
7.  点击 **"创建函数"**。
8.  **配置代码**:
    *   在 **"代码源" (Code source)** 区域，将编辑器中的所有默认代码删除。
    *   复制 `mass_email_dev/lambda/feedback_test/lianhe_mass_email_feedback_test.py` 文件中的**全部内容**并粘贴进去。
9.  **配置环境变量**:
    *   点击 **"配置" (Configuration)** 标签页，然后选择 **"环境变量" (Environment variables)**。
    *   添加以下两个变量：
        *   **Key**: `S3_BUCKET_NAME`, **Value**: `你的S3桶名` (例如 `lianhe-mass-email-feedback-test`)
        *   **Key**: `ALLOWED_ORIGIN`, **Value**: `https://issues.cn-northwest-1.amazonaws.cn`
10. **部署代码 (至关重要！)**
    *   回到 **"代码" (Code)** 标签页。
    *   **必须点击代码编辑器上方的 "Deploy" 按钮！** 仅仅保存是不够的，必须部署才能使更改生效。我们在调试中发现，很多问题都源于代码未部署。

### 第 4 步：创建 API Gateway

这是前端访问 Lambda 的入口。

1.  导航到 **API Gateway** 服务，点击 **"创建 API" (Create API)**。
2.  在 **HTTP API** 卡片上，点击 **"构建" (Build)**。
3.  **API 名称**: 例如 `FeedbackSystemAPI`。
4.  **配置集成**:
    *   点击 **"下一步" (Next)**。
    *   在 **"配置路由" (Configure routes)** 页面：
        *   **方法**: 选择 `GET`。
        *   **资源路径**: 输入 `/generate-upload-url`。
        *   **集成目标**: 选择 **Lambda**。
        *   **集成详细信息**: 在下拉列表中选择你在第3步中创建的 `mass-email-feedback-presigner` 函数。
5.  点击 **"下一步"**，然后 **"创建"**。
6.  **获取调用 URL**:
    *   API 创建后，在详情页面找到 **"调用 URL" (Invoke URL)**。复制它，下一步会用到。
7.  **配置 CORS**:
    *   在左侧导航栏选择 **"CORS"**。
    *   确保 **"访问控制允许源" (Access-Control-Allow-Origin)** 中包含了你的前端地址 `https://issues.cn-northwest-1.amazonaws.cn`。通常 API Gateway 会根据你的 Lambda 集成自动推荐一个合理的 CORS 配置，确认即可。

---

## 3. 前端部署步骤

### 第 1 步：配置并安装油猴脚本

1.  打开 `mass_email_dev/tampermonkey/feedback_test/feedback_script.js` 文件。
2.  **更新 API 端点**:
    *   找到 `API_ENDPOINT_GENERATE_URL` 常量。
    *   将其值替换为你在第4步中获取的 API Gateway **调用 URL**。
3.  **确认 @connect 指令**:
    *   检查脚本头部的元数据，确保以下两个 `@connect` 指令都存在。这在调试中是关键一步，用于授予脚本跨域请求的权限。
    ```javascript
    // @connect      qek0wlrzwb.execute-api.cn-north-1.amazonaws.com.cn // (你的 API Gateway 域名)
    // @connect      lianhe-mass-email-feedback-test.s3.cn-north-1.amazonaws.com.cn // (你的 S3 桶域名)
    ```
4.  在浏览器的 Tampermonkey 插件中，更新或安装此脚本。

### 第 2 步：测试

1.  导航到 `https://issues.cn-northwest-1.amazonaws.cn` 下的任意一个 issue 页面。
2.  确认页面右下角出现了 "Feedback" 按钮。
3.  点击按钮，填写反馈内容并提交。
4.  观察弹窗中的状态和调试日志，确认没有报错。
5.  前往你的 S3 存储桶，在 `feedback/` 目录下应该能看到新上传的 JSON 文件。

---

## 4. 常见问题排查 (Troubleshooting)

*   **错误: "Hello from Lambda!"**
    *   **原因**: API Gateway 调用了 Lambda，但 Lambda 运行的是默认模板代码。
    *   **解决方案**: 严格按照 **第 3 步** 的指示，确保你已将正确的 Python 代码粘贴到 Lambda 控制台，并**点击了 "Deploy" 按钮**。

*   **错误: "This domain is not a part of the @connect list"**
    *   **原因**: 油猴脚本没有被授予访问 API Gateway 或 S3 域名的权限。
    *   **解决方案**: 严格按照 **第 1 步 (前端)** 的指示，检查并添加正确的 `// @connect` 指令。

*   **浏览器控制台出现 CORS 错误**
    *   **原因**: CORS 配置不正确。
    *   **解决方案**:
        1.  检查 **第 1 步 (S3)** 的 CORS 配置是否正确。
        2.  检查 **第 4 步 (API Gateway)** 的 CORS 配置是否正确。

*   **Lambda 执行报错 (在 CloudWatch Logs 中查看)**
    *   **原因**: 可能是 IAM 权限不足或环境变量未配置。
    *   **解决方案**:
        1.  检查 **第 2 步 (IAM)** 的策略是否正确授予了对目标 S3 桶的 `s3:PutObject` 权限。
        2.  检查 **第 3 步 (Lambda)** 的环境变量 `S3_BUCKET_NAME` 和 `ALLOWED_ORIGIN` 是否已正确设置。 