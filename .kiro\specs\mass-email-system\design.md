# Design Document

## Overview

Mass Email 批量邮件翻译和用户反馈收集系统采用混合云原生架构，结合多阶段文本处理流水线和现代化反馈收集机制。系统设计遵循 AWS 中国区最佳实践，确保安全性、可扩展性和高可用性。

## Architecture

### System Architecture Diagram

```mermaid
graph TB
    subgraph "Translation Pipeline"
        A[Raw Email Content] --> B[Stage 0: Line Analysis]
        B --> C[Stage 1: JS Preprocessing]
        C --> D[Stage 2: LLM Translation]
        D --> E[Stage 3: Post Processing]
        E --> F[Localized Content]
    end
    
    subgraph "Feedback Collection"
        G[AWS Issues Page] --> H[Tampermonkey Script]
        H --> I[API Gateway]
        I --> J[Lambda Function]
        J --> K[S3 Storage]
    end
    
    subgraph "Service Name Synchronization"
        L[EventBridge Schedule] --> M[Web Scraper Lambda]
        M --> N[AWS China Services Page]
        M --> O[S3 PDF Parser]
        O --> P[S3 Bucket]
        M --> Q[PostgreSQL Database]
        O --> Q
    end
    
    subgraph "AWS China Infrastructure"
        R[CloudWatch Logs]
        S[IAM Roles]
        T[Environment Variables]
    end
    
    J --> R
    M --> R
    O --> R
    J --> S
    M --> S
    O --> S
    J --> T
    M --> T
    O --> T
```

### Multi-Stage Processing Pipeline

#### Stage 0: Text Line Type Analysis
- **Purpose**: 智能识别和分类输入文本的不同行类型
- **Components**:
  - Metadata Line Detector: 识别 `Service:`, `Region:`, `Failure mode X:` 等
  - Wording Line Detector: 识别 `Wording:`, `First Post Wording:` 等
  - General Content Classifier: 处理其他可翻译内容

#### Stage 1: JavaScript Term Standardization
- **Purpose**: 英文术语标准化和占位符生成
- **Key Functions**:
  - Service Name Normalization: 基于 `servicePatterns` 数组的权威数据源
  - CLI Command Protection: `aws ...` 命令转换为 `__CLICMD_X__` 占位符
  - Time Zone Conversion: 精确计算各时区到 UTC+8 的转换
  - URL Localization: AWS 域名本地化处理

#### Stage 2: LLM Translation Engine
- **Purpose**: 翻译所有非占位符的英文自然语言内容
- **Core Requirements**:
  - 严格保留所有 `__XXX_YYY__` 占位符
  - 翻译占位符周围的自然语言文本
  - 维护原始段落和行结构

#### Stage 3: Post-Processing & Finalization
- **Purpose**: 占位符恢复和最终中文格式化
- **Operations**:
  - Placeholder Restoration: 恢复为标准化值或格式化中文值
  - Brand Term Replacement: 最终中文品牌术语替换
  - Format Optimization: 空格调整和格式一致性确保

## Components and Interfaces

### Translation Module Components

#### PlaceholderManager
```typescript
interface PlaceholderManager {
  generatePlaceholder(content: string, type: PlaceholderType): string;
  restorePlaceholder(placeholder: string): string;
  validateIntegrity(text: string): boolean;
}

enum PlaceholderType {
  CLI_COMMAND = 'CLICMD',
  ARN_STRING = 'ARNSTR',
  JSON_BLOCK = 'JSONBLOCK',
  SERVICE_NAME = 'SRVCNM',
  TIMESTAMP = 'TIMESTAMP',
  URL = 'URL'
}
```

#### ServiceNameTracker
```typescript
interface ServiceNameTracker {
  trackMention(serviceName: string, scope: TrackingScope): ServiceMentionState;
  getStandardName(serviceName: string, isFirstMention: boolean): string;
  resetScope(scope: TrackingScope): void;
}

enum TrackingScope {
  GLOBAL = 'global',
  LINE_SCOPE = 'line'
}
```

#### TimeZoneConverter
```typescript
interface TimeZoneConverter {
  convertToBeijingTime(timeString: string, sourceTimezone: string): string;
  formatChineseTime(datetime: Date, includeTime: boolean): string;
  validateTimeFormat(timeString: string): boolean;
}
```

### Feedback Collection Components

#### Lambda Function Interface
```python
class FeedbackHandler:
    def __init__(self, s3_bucket: str, allowed_origin: str):
        self.s3_bucket = s3_bucket
        self.allowed_origin = allowed_origin
        self.s3_client = boto3.client('s3')
    
    def generate_presigned_url(self) -> Dict[str, str]:
        """Generate S3 presigned URL for feedback upload"""
        pass
    
    def create_object_key(self) -> str:
        """Create timestamp-based object key"""
        pass
    
    def handle_cors(self, response: Dict) -> Dict:
        """Add CORS headers to response"""
        pass
```

### Service Name Synchronization Components

#### Web Scraper Lambda Interface
```python
class ServiceNameScraper:
    def __init__(self, db_config: Dict, s3_config: Dict):
        self.db_client = psycopg2.connect(**db_config)
        self.s3_client = boto3.client('s3')
        self.target_url = "https://www.amazonaws.cn/en/about-aws/regional-product-services/"
    
    def scrape_web_services(self) -> List[ServiceInfo]:
        """Scrape AWS China services page for service names"""
        pass
    
    def parse_pdf_services(self, s3_bucket: str, pdf_key: str) -> List[ServiceInfo]:
        """Extract service names from S3-stored PDF documents"""
        pass
    
    def normalize_service_names(self, raw_services: List[str]) -> List[ServiceMapping]:
        """Standardize and normalize service name formats"""
        pass
    
    def update_database(self, service_mappings: List[ServiceMapping]) -> bool:
        """Update PostgreSQL database with latest service mappings"""
        pass
```

#### PostgreSQL Database Schema
```sql
-- AWS China Service Names Database Schema
CREATE TABLE aws_china_services (
    id SERIAL PRIMARY KEY,
    service_key VARCHAR(100) UNIQUE NOT NULL,
    full_name_en VARCHAR(255) NOT NULL,
    full_name_cn VARCHAR(255),
    short_name_en VARCHAR(100) NOT NULL,
    short_name_cn VARCHAR(100),
    aliases TEXT[], -- Array of alternative names
    category VARCHAR(100),
    region_availability TEXT[], -- Array of available regions
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    source_type VARCHAR(50) NOT NULL, -- 'web_scrape' or 'pdf_extract'
    is_active BOOLEAN DEFAULT TRUE
);

CREATE INDEX idx_service_key ON aws_china_services(service_key);
CREATE INDEX idx_full_name_en ON aws_china_services(full_name_en);
CREATE INDEX idx_short_name_en ON aws_china_services(short_name_en);
```

#### Tampermonkey Script Interface
```javascript
class FeedbackCollector {
    constructor(config) {
        this.apiEndpoint = config.API_ENDPOINT_GENERATE_URL;
        this.debugLogger = new DebugLogger();
    }
    
    injectFeedbackButton() {
        // Inject feedback button after 2-second delay
    }
    
    showFeedbackModal() {
        // Display feedback form modal
    }
    
    handleFeedbackSubmit() {
        // Process feedback submission workflow
    }
    
    uploadToS3(uploadURL, feedbackData) {
        // Upload feedback JSON to S3
    }
}
```

## Data Models

### Translation Data Models

#### TextLine
```typescript
interface TextLine {
  content: string;
  type: LineType;
  metadata?: {
    key?: string;
    value?: string;
  };
  placeholders: Placeholder[];
}

enum LineType {
  METADATA = 'metadata',
  WORDING = 'wording',
  GENERAL_CONTENT = 'general'
}
```

#### Placeholder
```typescript
interface Placeholder {
  id: string;
  type: PlaceholderType;
  originalContent: string;
  standardizedContent: string;
  position: {
    start: number;
    end: number;
  };
}
```

#### ServicePattern
```typescript
interface ServicePattern {
  baseNames: string[];
  standardFullName: string;
  standardShortName: string;
  aliases: string[];
  firstMentionFormat: string;
  subsequentMentionFormat: string;
}
```

### Feedback Data Models

#### FeedbackPayload
```typescript
interface FeedbackPayload {
  mass_email_page_url: string;
  satisfaction: 'satisfied' | 'unsatisfied';
  comments: string;
  submitted_at_iso: string;
  user_agent: string;
}
```

#### S3ObjectMetadata
```typescript
interface S3ObjectMetadata {
  bucket: string;
  key: string; // Format: feedback/YYYY/MM/DD/HH-MM-SS-{uuid}.json
  contentType: string;
  uploadURL: string;
  expiresIn: number; // 300 seconds
}
```

### Service Name Synchronization Data Models

#### ServiceInfo
```typescript
interface ServiceInfo {
  serviceName: string;
  fullNameEn: string;
  fullNameCn?: string;
  shortNameEn: string;
  shortNameCn?: string;
  aliases: string[];
  category?: string;
  regionAvailability: string[];
  sourceType: 'web_scrape' | 'pdf_extract';
}
```

#### ServiceMapping
```typescript
interface ServiceMapping {
  serviceKey: string;
  standardizedNames: {
    fullNameEn: string;
    fullNameCn?: string;
    shortNameEn: string;
    shortNameCn?: string;
  };
  aliases: string[];
  metadata: {
    category?: string;
    regions: string[];
    lastUpdated: Date;
    sourceType: string;
    confidence: number;
  };
}
```

#### DatabaseSyncResult
```typescript
interface DatabaseSyncResult {
  totalProcessed: number;
  newServices: number;
  updatedServices: number;
  errors: string[];
  syncTimestamp: Date;
  sourcesSynced: {
    webScrape: boolean;
    pdfExtract: boolean;
  };
}
```

## Error Handling

### Translation Pipeline Error Handling

#### Stage-Level Error Recovery
```typescript
interface StageErrorHandler {
  handleStageError(stage: ProcessingStage, error: Error, input: any): StageResult;
  rollbackToStage(targetStage: ProcessingStage): void;
  validateStageOutput(stage: ProcessingStage, output: any): boolean;
}
```

#### Placeholder Integrity Validation
- **Pre-LLM Validation**: 确保所有技术内容正确转换为占位符
- **Post-LLM Validation**: 验证 LLM 输出中占位符的完整性
- **Restoration Validation**: 确保占位符正确恢复为原始内容

### Feedback System Error Handling

#### Lambda Function Error Handling
```python
class ErrorHandler:
    @staticmethod
    def handle_s3_error(error: Exception) -> Dict:
        """Handle S3-related errors"""
        return {
            'statusCode': 500,
            'headers': {'Access-Control-Allow-Origin': ALLOWED_ORIGIN},
            'body': json.dumps({'error': 'S3 operation failed'})
        }
    
    @staticmethod
    def handle_cors_error(origin: str) -> Dict:
        """Handle CORS validation errors"""
        return {
            'statusCode': 403,
            'headers': {'Access-Control-Allow-Origin': ALLOWED_ORIGIN},
            'body': json.dumps({'error': 'CORS policy violation'})
        }
```

#### Frontend Error Handling
```javascript
class ErrorHandler {
    static handleNetworkError(error) {
        this.logDebugToModal(`网络错误: ${JSON.stringify(error)}`);
        this.showUserMessage('❌ 网络错误，请检查连接。', 'error');
    }
    
    static handleAPIError(response) {
        this.logDebugToModal(`API错误: 状态码 ${response.status}`);
        this.showUserMessage('❌ 错误：无法获取上传授权。', 'error');
    }
    
    static handleUploadError(error) {
        this.logDebugToModal(`上传错误: ${error.message}`);
        this.showUserMessage('❌ 上传失败，请稍后重试。', 'error');
    }
}
```

### Service Name Synchronization Error Handling

#### Web Scraping Error Handling
```python
class ServiceSyncErrorHandler:
    @staticmethod
    def handle_web_scraping_error(error: Exception, url: str) -> Dict:
        """Handle web scraping errors"""
        return {
            'error_type': 'web_scraping_failed',
            'url': url,
            'message': str(error),
            'timestamp': datetime.utcnow().isoformat()
        }
    
    @staticmethod
    def handle_pdf_parsing_error(error: Exception, s3_key: str) -> Dict:
        """Handle PDF parsing errors"""
        return {
            'error_type': 'pdf_parsing_failed',
            's3_key': s3_key,
            'message': str(error),
            'timestamp': datetime.utcnow().isoformat()
        }
    
    @staticmethod
    def handle_database_error(error: Exception, operation: str) -> Dict:
        """Handle database operation errors"""
        return {
            'error_type': 'database_operation_failed',
            'operation': operation,
            'message': str(error),
            'timestamp': datetime.utcnow().isoformat()
        }
```

## Testing Strategy

### Translation Pipeline Testing

#### Unit Testing Strategy
```typescript
describe('Translation Pipeline', () => {
  describe('Stage 0: Line Analysis', () => {
    it('should correctly identify metadata lines');
    it('should classify wording lines properly');
    it('should handle mixed content types');
  });
  
  describe('Stage 1: Preprocessing', () => {
    it('should generate correct placeholders for CLI commands');
    it('should track service names across scopes');
    it('should convert time zones accurately');
  });
  
  describe('Stage 2: LLM Translation', () => {
    it('should preserve all placeholders');
    it('should translate natural language content');
    it('should maintain text structure');
  });
  
  describe('Stage 3: Post-processing', () => {
    it('should restore placeholders correctly');
    it('should apply final formatting');
    it('should validate output quality');
  });
});
```

#### Integration Testing
- **End-to-End Pipeline Testing**: 完整的 4 阶段处理流程测试
- **Service Name Tracking Testing**: 跨作用域的服务名称追踪验证
- **Time Zone Conversion Testing**: 各种时区格式的转换准确性测试

### Feedback System Testing

#### Lambda Function Testing
```python
def test_lambda_handler():
    # Test presigned URL generation
    # Test CORS header handling
    # Test error scenarios
    # Test environment variable handling
```

#### Frontend Testing
```javascript
describe('Feedback Collection', () => {
  it('should inject feedback button after delay');
  it('should handle API responses correctly');
  it('should upload feedback data to S3');
  it('should provide debug information');
  it('should handle error scenarios gracefully');
});
```

### Service Name Synchronization Testing

#### Lambda Function Testing
```python
def test_service_name_scraper():
    # Test web scraping functionality
    # Test PDF parsing from S3
    # Test database operations
    # Test error handling scenarios
    # Test data normalization
```

#### Integration Testing
- **Web Scraping Testing**: 验证从AWS中国区服务页面提取服务名称的准确性
- **PDF Processing Testing**: 测试从S3存储的PDF文件中提取服务信息
- **Database Sync Testing**: 验证PostgreSQL数据库同步的完整性
- **Scheduled Execution Testing**: 测试EventBridge触发的定期同步功能

## Development Workflow Integration

### Core Workflow Implementation

#### 1. 思考、检索、整合工作流
```typescript
interface DevelopmentWorkflow {
  analyzeAndDecompose(request: UserRequest): TaskPlan;
  gatherInformation(plan: TaskPlan): InformationContext;
  executeDeepResearch(context: InformationContext): ResearchResult;
  provideInteractiveFeedback(result: any): UserFeedback;
}
```

#### 2. MCP 工具集成策略
- **逻辑与规划**: 使用 `server-sequential-thinking` 进行任务拆解
- **文件系统操作**: 使用 `filesystem` 管理本地资源
- **网络信息检索**: 使用 `tavily-mcp` 获取最新技术信息
- **交互反馈**: 使用 `interactive-feedback-mcp` 确保用户满意度

#### 3. 质量保证集成
```typescript
interface QualityAssurance {
  validateTranslationAccuracy(input: string, output: string): ValidationResult;
  checkPlaceholderIntegrity(text: string): IntegrityResult;
  verifyAWSChinaCompliance(content: string): ComplianceResult;
  performSecurityAudit(system: SystemComponent): SecurityResult;
}
```

## Security Considerations

### Translation Pipeline Security
- **Input Sanitization**: 防止恶意输入影响翻译流程
- **Placeholder Injection Prevention**: 防止恶意占位符注入
- **Content Validation**: 确保输出内容的安全性

### Feedback System Security
- **CORS Policy Enforcement**: 严格限制跨域访问
- **IAM Role Minimal Permissions**: 最小权限原则
- **Input Validation**: 用户输入的严格验证
- **Presigned URL Security**: 短期有效期和访问控制

## File Structure Reference

### Project Directory Structure

```
├── docs/
│   ├── dev/                                  # Development documentation
│   ├── backups/                              # Documentation backups
│   └── lambda_feedback_test/                 # Lambda testing docs
├── lambda/
│   ├── dev/                                  # Development Lambda functions
│   └── feedback_test/
│       ├── lianhe_mass_email_feedback_test.py # Lambda function
│       └── lambda_test_event/                # Test events
├── tampermonkey/
│   ├── dev/                                  # Development scripts
│   ├── backups/                              # Script backups
│   └── feedback_test/
│       └── feedback_script.js                # Frontend script
├── mass_email_outline/
│   ├── mass_email_requirements.md            # Business requirements
│   └── Mass-Email-*.pdf                     # Translation guidelines
├── poc/
│   ├── mass_email_rules.md                   # Translation rules
│   ├── 提示词v11.16代码版本.md                # LLM prompt specifications
│   └── various POC files                    # Development iterations
└── .kiro/specs/mass-email-system/           # Current specification
```

### Development Environment Structure

#### Development Directories (`dev/`)
新增的开发目录用于存放正在开发中的代码和文档：

- **`docs/dev/`**: 开发阶段的文档，包括设计草稿、技术笔记等
- **`lambda/dev/`**: 开发中的 Lambda 函数代码，用于测试新功能
- **`tampermonkey/dev/`**: 开发中的用户脚本，用于功能迭代和测试

#### Production/Testing Directories
- **`lambda/feedback_test/`**: 经过测试的 Lambda 函数代码
- **`tampermonkey/feedback_test/`**: 经过测试的用户脚本
- **`docs/lambda_feedback_test/`**: Lambda 测试相关文档

## Performance Optimization

### Translation Pipeline Optimization
- **Caching Strategy**: 缓存常用服务名称和翻译结果
- **Parallel Processing**: 并行处理不同类型的文本行
- **Memory Management**: 优化大文本处理的内存使用

### Feedback System Optimization
- **Lambda Cold Start Optimization**: 减少冷启动时间
- **S3 Upload Performance**: 优化文件上传性能
- **Frontend Performance**: 最小化 UI 注入对页面性能的影响