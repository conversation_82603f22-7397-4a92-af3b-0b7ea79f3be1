version: 1.0.0
last_updated: '2025-04-03'
metadata:
  description: AWS服务名称标准化配置
  maintainers:
  - AWS Translation Team
  version_history:
  - version: 1.0.0
    date: '2025-02-24'
    changes: Initial version - Migration from legacy files
  - version: 1.0.1
    date: '2025-04-03'
    changes: 优化数据结构，移除重复定义，统一别名风格，修正命名标准

# 服务配置数据结构说明
# ---------------------
# 各配置项含义：
# 
# 1. name: 服务的标识符，用于在系统内部唯一标识该服务，通常使用官方完整名称
#
# 2. short_name: 服务的简称，通常用于后续出现时的替换
#
# 3. aliases: 定义服务的所有可能别名，用于匹配原文中的各种表达形式
#    - 可以使用YAML锚点（&name）定义，在其他地方通过*name引用
#    - 包含该服务在文档中可能出现的所有变体形式
#
# 4. category: 服务所属的分类，用于组织和管理服务（如compute、storage等）
#
# 5. priority: 优先级，数字越小优先级越高，用于解决匹配冲突
#    - 1: 高优先级，用于需要特殊处理的核心服务（如EC2、S3等）
#    - 2: 标准优先级（默认值）
#    - 3: 低优先级，用于在其他规则无法匹配时进行处理
#
# 6. metadata: 服务的元数据和处理规则
#    - special_handling: 表示需要特殊处理，通常用于复杂的替换规则
#    - first_occurrence: 首次出现时的替换规则
#      - replacement: 首次出现时的替换文本，通常使用完整的服务名称
#    - subsequent_occurrence: 后续出现时的替换规则
#      - replacement: 后续出现时的替换文本，通常使用简称
#    - preserve_case: 是否保留原文的大小写格式
#    - first_use_full_name: 首次使用时是否使用全名

services:
  # 计算服务
  compute:
  - name: Amazon Elastic Compute Cloud (Amazon EC2)
    short_name: Amazon EC2
    aliases: &ec2_aliases
      - Amazon EC2
      - AWS EC2
      - EC2
      - ec2
      - Elastic Compute Cloud
    category: compute
    priority: 1
    metadata:
      special_handling: true
      first_occurrence:
        replacement: "Amazon Elastic Compute Cloud (Amazon EC2)"
      subsequent_occurrence:
        replacement: "Amazon EC2"
      preserve_case: true
      first_use_full_name: true
  
  - name: AWS Lambda
    short_name: Lambda
    aliases: &lambda_aliases
      - AWS Lambda
      - Amazon Lambda
      - Lambda
      - lambda
      - AWS LAMBDA
      - aws lambda
    category: compute
    priority: 1
    metadata:
      special_handling: true
      first_occurrence:
        replacement: "AWS Lambda"
      subsequent_occurrence:
        replacement: "AWS Lambda"
      preserve_case: true
      first_use_full_name: true
  
  - name: EC2 Instance Connect
    short_name: EC2 Instance Connect
    aliases: &ec2_ic_aliases
      - EC2 Instance Connect
      - Instance Connect
    category: compute
    priority: 2
    metadata:
      special_handling: true
      first_occurrence:
        replacement: "EC2 Instance Connect"
      subsequent_occurrence:
        replacement: "EC2 Instance Connect"
      preserve_case: true
      first_use_full_name: true
  
  - name: Amazon EC2 Auto Scaling
    short_name: EC2 Auto Scaling
    aliases:
      - Amazon EC2 Auto Scaling
      - AWS EC2 Auto Scaling
      - EC2 Auto Scaling
      - Auto Scaling
    category: compute
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Fargate
    short_name: Fargate
    aliases:
      - Amazon Fargate
      - AWS Fargate
      - Fargate
    category: compute
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false

  # 存储服务
  storage:
  - name: Amazon Simple Storage Service (Amazon S3)
    short_name: Amazon S3
    aliases: &s3_aliases
      - Amazon S3
      - AWS S3
      - S3
      - s3
      - Amazon Simple Storage Service
      - AWS Simple Storage Service
      - Simple Storage Service
    category: storage
    priority: 1
    metadata:
      special_handling: true
      first_occurrence:
        replacement: "Amazon Simple Storage Service (Amazon S3)"
      subsequent_occurrence:
        replacement: "Amazon S3"
      preserve_case: true
      first_use_full_name: true
  
  - name: Amazon Elastic Block Store (EBS)
    short_name: Amazon EBS
    aliases:
      - Amazon Elastic Block Store
      - Amazon EBS
      - AWS EBS
      - EBS
      - Elastic Block Store
    category: storage
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Elastic File System (EFS)
    short_name: Amazon EFS
    aliases:
      - Amazon Elastic File System
      - Amazon EFS
      - AWS EFS
      - EFS
      - Elastic File System
    category: storage
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon S3 Glacier
    short_name: Amazon S3 Glacier
    aliases:
      - Amazon S3 Glacier
      - Amazon Glacier
      - AWS Glacier
      - S3 Glacier
      - Glacier
    category: storage
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Storage Gateway
    short_name: Storage Gateway
    aliases:
      - Amazon Storage Gateway
      - AWS Storage Gateway
      - Storage Gateway
    category: storage
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false

  # 数据库服务
  database:
  - name: Amazon DynamoDB
    short_name: DynamoDB
    aliases:
      - Amazon DynamoDB
      - AWS DynamoDB
      - DynamoDB
    category: database
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Relational Database Service (RDS)
    short_name: Amazon RDS
    aliases:
      - Amazon Relational Database Service
      - Amazon RDS
      - AWS RDS
      - RDS
    category: database
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Aurora
    short_name: Aurora
    aliases:
      - Amazon Aurora
      - AWS Aurora
      - Aurora
      - Amazon Aurora MySQL
      - Amazon Aurora PostgreSQL
    category: database
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon DocumentDB
    short_name: Amazon DocumentDB
    aliases:
      - Amazon DocumentDB
      - AWS DocumentDB
      - DocumentDB
      - Amazon DocumentDB (with MongoDB compatibility)
    category: database
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon ElastiCache
    short_name: ElastiCache
    aliases:
      - Amazon ElastiCache
      - AWS ElastiCache
      - ElastiCache
    category: database
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Neptune
    short_name: Neptune
    aliases:
      - Amazon Neptune
      - AWS Neptune
      - Neptune
    category: database
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Redshift
    short_name: Redshift
    aliases:
      - Amazon Redshift
      - AWS Redshift
      - Redshift
    category: database
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Timestream
    short_name: Timestream
    aliases:
      - Amazon Timestream
      - AWS Timestream
      - Timestream
    category: database
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon MemoryDB
    short_name: MemoryDB
    aliases:
      - Amazon MemoryDB
      - AWS MemoryDB
      - MemoryDB
    category: database
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Database Migration Service (Amazon DMS)
    short_name: Database Migration Service
    aliases:
      - Amazon Database Migration Service
      - AWS Database Migration Service
      - Amazon DMS
      - AWS DMS
      - DMS
    category: database
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false

  # 网络和内容分发
  networking:
  - name: Amazon Virtual Private Cloud (VPC)
    short_name: Amazon VPC
    aliases:
      - Amazon Virtual Private Cloud
      - Amazon VPC
      - AWS VPC
      - VPC
    category: networking
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon CloudFront
    short_name: CloudFront
    aliases:
      - Amazon CloudFront
      - AWS CloudFront
      - CloudFront
    category: networking
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Route 53
    short_name: Route 53
    aliases:
      - Amazon Route 53
      - AWS Route 53
      - Route 53
    category: networking
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon API Gateway
    short_name: API Gateway
    aliases:
      - Amazon API Gateway
      - AWS API Gateway
      - API Gateway
    category: networking
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Direct Connect
    short_name: Direct Connect
    aliases:
    - Amazon Direct Connect
      - AWS Direct Connect
      - Direct Connect
    category: networking
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon PrivateLink
    short_name: PrivateLink
    aliases:
      - Amazon PrivateLink
      - AWS PrivateLink
      - PrivateLink
    category: networking
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Elastic Load Balancing (ELB)
    short_name: Elastic Load Balancing
    aliases:
      - Elastic Load Balancing
      - ELB
      - AWS ELB
      - Amazon ELB
    category: networking
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false

  # 管理和监控
  management:
  - name: Amazon Systems Manager
    short_name: Systems Manager
    aliases: &ssm_aliases
      - Amazon Systems Manager
      - AWS Systems Manager
      - Systems Manager
      - SSM
      - AWS SSM
      - Amazon SSM
      - AWS Systems Manager (SSM)
    category: management
    priority: 1
    metadata:
      special_handling: true
      first_occurrence:
        replacement: "Amazon Systems Manager"
      subsequent_occurrence:
        replacement: "Systems Manager"
      preserve_case: true
      first_use_full_name: true
  
  - name: Amazon CloudWatch
    short_name: CloudWatch
    aliases:
      - Amazon CloudWatch
      - AWS CloudWatch
      - CloudWatch
    category: management
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon CloudWatch Events
    short_name: CloudWatch Events
    aliases:
      - Amazon CloudWatch Events
      - AWS CloudWatch Events
      - CloudWatch Events
    category: management
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon CloudWatch Logs
    short_name: CloudWatch Logs
    aliases:
      - Amazon CloudWatch Logs
      - AWS CloudWatch Logs
      - CloudWatch Logs
    category: management
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon CloudTrail
    short_name: CloudTrail
    aliases:
      - Amazon CloudTrail
      - AWS CloudTrail
      - CloudTrail
    category: management
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: AWS Config
    short_name: Config
    aliases:
      - AWS Config
      - Amazon Config
      - Config
    category: management
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: AWS Organizations
    short_name: Organizations
    aliases:
      - AWS Organizations
      - Amazon Organizations
      - Organizations
    category: management
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: AWS Health Dashboard
    short_name: Health Dashboard
    aliases:
      - AWS Health Dashboard
      - Amazon Health Dashboard
      - Health Dashboard
    category: management
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false

  # 安全、身份与合规
  security:
  - name: Amazon Identity and Access Management (IAM)
    short_name: IAM
    aliases:
      - Amazon Identity and Access Management
      - AWS Identity and Access Management
      - Amazon IAM
      - AWS IAM
      - IAM
    category: security
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Cognito
    short_name: Cognito
    aliases:
      - Amazon Cognito
      - AWS Cognito
      - Cognito
    category: security
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon GuardDuty
    short_name: GuardDuty
    aliases:
      - Amazon GuardDuty
      - AWS GuardDuty
      - GuardDuty
    category: security
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Inspector
    short_name: Inspector
    aliases:
      - Amazon Inspector
      - AWS Inspector
      - Inspector
    category: security
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon IAM Identity Center
    short_name: IAM Identity Center
    aliases:
      - Amazon IAM Identity Center
      - AWS IAM Identity Center
      - IAM Identity Center
    category: security
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Key Management Service (KMS)
    short_name: Amazon KMS
    aliases:
      - Amazon Key Management Service
      - AWS Key Management Service
      - Amazon KMS
      - AWS KMS
      - KMS
    category: security
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Security Hub
    short_name: Security Hub
    aliases:
      - Amazon Security Hub
      - AWS Security Hub
      - Security Hub
    category: security
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon WAF
    short_name: WAF
    aliases:
      - Amazon WAF
      - AWS WAF
      - WAF
    category: security
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Network Firewall
    short_name: Network Firewall
    aliases:
      - Amazon Network Firewall
      - AWS Network Firewall
      - Network Firewall
    category: security
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false

  # 开发者工具
  devtools:
  - name: Amazon CodeBuild
    short_name: CodeBuild
    aliases:
      - Amazon CodeBuild
      - AWS CodeBuild
      - CodeBuild
    category: devtools
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon CodeCommit
    short_name: CodeCommit
    aliases:
      - Amazon CodeCommit
      - AWS CodeCommit
      - CodeCommit
    category: devtools
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon CodeDeploy
    short_name: CodeDeploy
    aliases:
      - Amazon CodeDeploy
      - AWS CodeDeploy
      - CodeDeploy
    category: devtools
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon CodePipeline
    short_name: CodePipeline
    aliases:
      - Amazon CodePipeline
      - AWS CodePipeline
      - CodePipeline
    category: devtools
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Cloud9
    short_name: Cloud9
    aliases:
      - Amazon Cloud9
      - AWS Cloud9
      - Cloud9
    category: devtools
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  # 容器服务
  containers:
  - name: Amazon Elastic Container Service (ECS)
    short_name: Amazon ECS
    aliases:
      - Amazon Elastic Container Service
      - AWS Elastic Container Service
      - Amazon ECS
      - AWS ECS
      - ECS
    category: containers
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Elastic Container Registry (ECR)
    short_name: Amazon ECR
    aliases:
      - Amazon Elastic Container Registry
      - AWS Elastic Container Registry
      - Amazon ECR
      - AWS ECR
      - ECR
    category: containers
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Elastic Kubernetes Service (EKS)
    short_name: Amazon EKS
    aliases:
      - Amazon Elastic Kubernetes Service
      - AWS Elastic Kubernetes Service
      - Amazon EKS
      - AWS EKS
      - EKS
    category: containers
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon App Mesh
    short_name: App Mesh
    aliases:
      - Amazon App Mesh
      - AWS App Mesh
      - App Mesh
    category: containers
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  # 分析和机器学习
  analytics_ml:
  - name: Amazon SageMaker
    short_name: SageMaker
    aliases:
      - Amazon SageMaker
      - AWS SageMaker
      - SageMaker
    category: analytics_ml
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Athena
    short_name: Athena
    aliases:
      - Amazon Athena
      - AWS Athena
      - Athena
    category: analytics_ml
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Kinesis
    short_name: Kinesis
    aliases:
      - Amazon Kinesis
      - AWS Kinesis
      - Kinesis
    category: analytics_ml
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Kinesis Data Streams
    short_name: Kinesis Data Streams
    aliases:
      - Amazon Kinesis Data Streams
      - AWS Kinesis Data Streams
      - Kinesis Data Streams
    - Amazon Kinesis Streams
    category: analytics_ml
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Kinesis Data Firehose
    short_name: Kinesis Data Firehose
    aliases:
      - Amazon Kinesis Data Firehose
      - AWS Kinesis Data Firehose
      - Kinesis Data Firehose
      - Amazon Data Firehose
    category: analytics_ml
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Kinesis Video Streams
    short_name: Kinesis Video Streams
    aliases:
      - Amazon Kinesis Video Streams
      - AWS Kinesis Video Streams
      - Kinesis Video Streams
    category: analytics_ml
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon EMR
    short_name: EMR
    aliases:
      - Amazon EMR
      - AWS EMR
      - EMR
      - Amazon Elastic MapReduce
      - AWS Elastic MapReduce
    category: analytics_ml
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon OpenSearch Service
    short_name: OpenSearch Service
    aliases:
    - Amazon OpenSearch Service
      - AWS OpenSearch Service
      - OpenSearch Service
      - OpenSearch
    category: analytics_ml
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon QuickSight
    short_name: QuickSight
    aliases:
    - Amazon QuickSight
      - AWS QuickSight
      - QuickSight
    category: analytics_ml
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Lake Formation
    short_name: Lake Formation
    aliases:
      - Amazon Lake Formation
      - AWS Lake Formation
      - Lake Formation
    category: analytics_ml
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: AWS Glue
    short_name: Glue
    aliases:
      - AWS Glue
      - Amazon Glue
      - Glue
    category: analytics_ml
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false

  # 应用集成
  integration:
  - name: Amazon Simple Queue Service (SQS)
    short_name: Amazon SQS
    aliases:
      - Amazon Simple Queue Service
      - AWS Simple Queue Service
      - Amazon SQS
      - AWS SQS
      - SQS
    category: integration
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Simple Notification Service (SNS)
    short_name: Amazon SNS
    aliases:
      - Amazon Simple Notification Service
      - AWS Simple Notification Service
      - Amazon SNS
      - AWS SNS
      - SNS
    category: integration
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon EventBridge
    short_name: EventBridge
    aliases:
      - Amazon EventBridge
      - AWS EventBridge
      - EventBridge
    category: integration
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon MQ
    short_name: Amazon MQ
    aliases:
      - Amazon MQ
      - AWS MQ
      - MQ
    category: integration
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon Step Functions
    short_name: Step Functions
    aliases:
    - Amazon Step Functions
      - AWS Step Functions
      - Step Functions
    category: integration
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Amazon AppSync
    short_name: AppSync
    aliases:
      - Amazon AppSync
      - AWS AppSync
      - AppSync
    category: integration
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false

  # 其他服务
  other:
  - name: AWS Support
    short_name: Support
    aliases:
    - AWS Support
      - Amazon Support
      - Amazon Web Services Support
      - Support
    category: other
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Patch Manager
    short_name: Patch Manager
    aliases:
    - Patch Manager
      - AWS Patch Manager
      - Amazon Patch Manager
    category: other
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Session Manager
    short_name: Session Manager
    aliases:
    - Session Manager
      - AWS Session Manager
      - Amazon Session Manager
    category: other
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
  
  - name: Parameter Store
    short_name: Parameter Store
    aliases:
      - Parameter Store
      - AWS Parameter Store
      - Amazon Parameter Store
    category: other
    priority: 2
    metadata:
      preserve_case: true
      special_handling: false
      
rules:
  case_sensitive: false
  preserve_original: false
  default_priority: 2
  special_characters:
  - '-'
  - .
  - /
  - (
  - )
