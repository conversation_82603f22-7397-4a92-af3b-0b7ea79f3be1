---
inclusion: always
---

# 批量邮件系统开发上下文

## 项目概述

这是AWS中国区批量邮件翻译和用户反馈收集系统。该系统使用多阶段处理流水线，结合Python预处理、LLM翻译和JavaScript后处理，自动化邮件翻译以符合AWS中国区标准。

## 核心架构组件

### 1. 翻译自动化模块
- **多阶段流水线**: 4阶段处理（阶段0-3）
- **占位符系统**: 在翻译过程中保护技术内容
- **服务名称追踪**: 全局和行级别的首次/后续提及追踪
- **时区转换**: 各种时区到UTC+8的精确计算
- **数据库集成**: 使用v2版本service_names表，以authoritative_full_name作为业务主键

### 2. 反馈收集模块
- **Lambda函数**: 生成S3预签名URL用于反馈上传
- **Tampermonkey框架**: 使用统一框架进行页面内容检测和UI注入
- **页面内容检测**: 基于框架的智能内容检测，支持多种策略
- **S3存储**: 基于时间戳层次结构的有序反馈数据
- **模块化架构**: 框架支持多个反馈模块和扩展

### 3. 服务名称同步模块 ✅ **96%完成**

#### 核心原则：功能精简
**🎯 重要**: AWS服务同步系统遵循**功能精简原则**，只实现核心业务功能：
- ✅ 只实现核心业务功能和基本错误处理
- ✅ 使用简单的日志记录（基本的成功/失败状态）
- ✅ 保持代码简洁和可维护性
- ❌ 不添加复杂的监控和性能统计功能
- ❌ 不添加详细的报告生成和分析功能

#### 模块架构
```
aws_service_sync/
├── handler.py              # Lambda入口函数
├── config.py              # 配置管理
├── scrapers/
│   ├── web_scraper.py     # 网页抓取器
│   └── pdf_parser.py      # PDF解析器
├── processors/
│   ├── data_processor.py  # 数据处理器
│   ├── regex_pattern_generator.py    # 正则模式生成器
│   └── service_pattern_sync.py       # 服务模式同步处理器
├── storage/
│   ├── rds_client.py      # RDS数据库客户端
│   └── s3_client.py       # S3操作客户端
└── utils/
    ├── logger.py          # 基础日志工具
    └── retry.py           # 重试机制
```

#### 功能模块
- **EventBridge调度器**: 按可配置计划触发Lambda函数执行 ✅
- **网页抓取模块**: 从AWS中国区域服务页面提取服务名称 ✅
- **PDF解析模块**: 处理S3存储的PDF文件，进行三列数据提取 ✅
- **数据处理模块**: 网页和PDF数据源之间的智能匹配 ✅
- **PostgreSQL数据库**: 支持多数据源同步的v2架构集中存储 ✅
- **正则表达式模式生成**: 基于8种核心模式类型的自动生成系统 ✅
- **模式管理系统**: 完整的生命周期管理，包括优化、维护、验证和清理 ✅
- **配置管理**: AWS Secrets Manager集成，安全配置存储 ✅
- **错误处理和日志**: 全面的错误处理，结构化日志用于CloudWatch监控 ✅
- **性能优化**: 智能模式分析和优化建议 ✅
- **集成测试套件**: 完整的测试套件，包含12个端到端功能测试用例 ✅
- **部署基础设施**: 完整的部署指南和监控配置 ✅
- **⏳ 剩余工作**: 仅剩自动化部署脚本（任务10.2）待完成

### 4. Tampermonkey框架模块
- **内容检测引擎**: 统一的页面内容检测，支持多种策略（textContent、innerText、visibleText、selector、regex）
- **脚本激活器**: 管理脚本生命周期和模块协调
- **事件总线**: 提供模块间通信和松耦合
- **性能监控器**: 跟踪资源使用和优化指标
- **错误处理器**: 全面的错误处理和优雅降级
- **多脚本协调**: 命名空间管理和共享资源协调

## 数据库架构

### 数据库设计理念
系统实施"逻辑外化"方法，将硬编码的业务规则从应用程序代码移动到数据库中。这种设计显著提高了可维护性和灵活性。

### 核心数据库表（v2架构）

#### service_names（v2更新版）
- **authoritative_full_name**: 来自官方网站的业务主键（例如："Amazon Elastic Compute Cloud (EC2)"）
- **base_name**: 翻译逻辑的规范化名称（例如："Amazon Elastic Compute Cloud"）
- **internal_name**: PDF"AWS offering (internal name)"字段
- **full_name_en/short_name_en**: 首次/后续提及的替换值
- **service_code**: AWS官方服务代码（例如："ec2"）
- **source**: 数据源跟踪（web_scrape、pdf_parse、manual）
- **last_synced_at**: 自动更新的同步时间戳

#### brand_term_mappings
- 存储强制品牌术语替换（例如："Amazon Support" → "亚马逊云科技中国支持团队"）
- 支持规则的动态启用/禁用
- 基于优先级的应用顺序

#### url_mappings
- 存储支持正则表达式的URL本地化规则
- 重叠规则的优先级控制
- 中国区可访问性验证

#### regex_patterns
存储用于实体识别的正则表达式模式，基于全新的8种核心模式类型系统

**v2版本关键字段**:
- **pattern_name**: 模式的易读名称，如 "EC2_FULL_COMPLEX_SUFFIX"
- **pattern_type**: 模式类型（SERVICE_NAME、TIMEZONE、CLI_COMMAND、URL、GENERAL）
- **regex_string**: 正则表达式本身，支持边界保护和复合后缀
- **related_service_id**: 关联的服务ID（外键到service_names表）
- **service_code**: 服务代码，如 "ec2", "s3"，便于按服务分组管理
- **priority**: 匹配优先级，范围90-130，数字越大优先级越高
- **notes**: 模式元数据，如 "isCompoundWithSuffix: true, suffixGroup: 1"
- **validation_status**: 验证状态（pending, valid, invalid）

**8种核心模式类型**:
1. **全称复合后缀模式** (优先级: 120) - 匹配完整服务名称 + 复杂后缀
2. **全称标准模式** (优先级: 115) - 精确匹配完整服务名称，带边界保护
3. **简称复合后缀模式** (优先级: 110) - 匹配简称 + 复杂后缀
4. **简称标准模式** (优先级: 105) - 精确匹配服务简称，带边界保护
5. **缩写复合后缀模式** (优先级: 100) - 匹配纯缩写 + 后缀
6. **缩写标准模式** (优先级: 95) - 精确匹配纯缩写，带边界保护
7. **特殊变体模式** (优先级: 125) - 处理特殊服务变体（如Aurora PostgreSQL）
8. **上下文保护模式** (优先级: 90) - 在特定上下文中避免误匹配

**边界保护机制（更新）**:
- 服务名模式不内嵌“可变宽度负向后行”。上下文边界由 BoundaryGuard 统一处理：
  - `CONTEXT_ARN_GENERIC`：`arn:(?:aws|aws-cn):\S+`
  - `CONTEXT_URL_GENERIC`：`https?://\S+`
- 代码/标识符相邻字符的固定宽度约束（如 `(?<![:_-])`、`(?![:_-])`）可按需保留。

**开发要点**:
- 完全替代硬编码的正则表达式数组
- 支持复合词智能处理和后缀捕获
- 实现分层优先级系统（90-130）
- 提供特殊服务（Aurora、Health、RDS等）的专门处理
- 集成边界保护机制避免误匹配

#### translation_jobs
- 记录完整的翻译任务生命周期，包含4阶段跟踪
- **placeholder_map**: 存储占位符到原文映射的JSONB字段
- **service_mention_state**: 跟踪首次/后续服务提及的JSONB字段
- 分布式系统支持的UUID主键

#### feedback_submissions
- 存储与S3集成的用户反馈
- 链接到translation_jobs进行关联
- 满意度枚举和结构化评论

### 数据库集成点
- **翻译流水线**: 使用数据库规则进行服务名称标准化和品牌术语替换
- **服务同步**: 从网页抓取和PDF解析更新服务名称
- **反馈收集**: 存储与翻译任务关联的反馈数据
- **规则管理**: 支持无需代码部署的规则动态启用/禁用

## 关键技术规范

### 翻译处理阶段

#### 阶段0：文本行分析（JavaScript）
- 元数据行：`Service:`、`Region:`、`Failure mode X:`等
- 特殊内容行：`Wording :`、`First Post Wording :`
- 一般内容行：所有其他可翻译内容

#### 阶段1：文本预处理标准化（Python实现，原JavaScript逻辑）
- CLI命令 → `__CLICMD_X__` 占位符
- ARN字符串 → `__ARNSTR_X__` 占位符
- JSON块 → `__JSONBLOCK_X__` 占位符
- 服务名称 → `__SRVCNM_X__` 占位符
- 时间戳 → `__TIMESTAMP_X__` 占位符
- URL → `__URL_X__` 占位符

#### 阶段2：LLM翻译
- 翻译所有非占位符的英文内容
- 完全保持所有占位符不变
- 维持段落和行结构

#### 阶段3：占位符恢复和最终格式化（JavaScript）
- 将占位符恢复为标准化值
- 应用最终的中文品牌术语替换
- 将时间戳格式化为中文格式

### AWS中国区本地化规则

#### 品牌术语
- `Amazon` → `亚马逊云科技`
- `Amazon Support` → `亚马逊云科技中国支持团队`
- `Amazon console` → `亚马逊云科技控制台`
- `Amazon account` → `亚马逊云科技账户`

#### 时区转换
- PDT (UTC-7) → 原始时间 + 15小时
- PST (UTC-8) → 原始时间 + 16小时
- UTC/GMT → 原始时间 + 8小时
- 最终格式：`YYYY年M月D日 上午/下午 h:mm 北京时间`

#### 链接本地化
- `aws.amazon.com` → `amazonaws.cn`
- `console.aws.amazon.com` → `console.amazonaws.com.cn`
- 支持链接 → `https://console.amazonaws.cn/support/`

#### CLI区域参数
- 宁夏区域内容 → `--region cn-northwest-1`
- 北京区域内容 → `--region cn-north-1`

## 文件结构参考

```
├── .kiro/
│   ├── specs/                               # 功能规范
│   │   ├── aws-service-sync/                # AWS服务同步系统
│   │   ├── database-design/                 # 数据库架构和设计
│   │   ├── feedback-script-optimization/    # 反馈脚本优化
│   │   ├── mass-email-system/              # 核心系统需求
│   │   └── tampermonkey-framework/         # 通用Tampermonkey框架
│   └── steering/                           # 开发指导
│       ├── mass-email-system-context.md    # 系统概述和上下文
│       ├── database-development-guide.md   # 数据库开发指导
│       ├── development-workflow-guide.md   # 开发工作流程
│       ├── feedback-system-guide.md        # 反馈系统开发
│       ├── tampermonkey-development-guide.md # Tampermonkey开发
│       └── translation-rules.md            # 翻译规则和标准
├── database/
│   ├── mass_email_database_schema_v1.sql   # 完整的数据库架构和数据
│   └── ddl3.txt                            # 旧版DDL参考
├── deployment/                             # ✅ **新增**: 自动化部署资源
│   └── lambda/
│       └── dev/
│           └── aws_service_sync/           # AWS服务同步系统部署资源
│               ├── README.md               # 详细部署指导文档
│               ├── DEPLOYMENT_GUIDE.md     # 快速部署指导
│               ├── deploy.sh               # 主部署脚本
│               ├── monitor.sh              # 监控配置脚本
│               ├── test.sh                 # 测试脚本
│               └── examples/               # 配置示例文件
│                   ├── environment-variables.json
│                   ├── eventbridge-schedules.json
│                   ├── cloudwatch-alarms.json
│                   └── iam-policies.json
├── integration_testing/                    # ✅ **新增**: 集成测试套件
│   ├── integration_tests.py               # 主要集成测试代码
│   ├── local_test_runner.py               # 本地测试运行器
│   ├── eventbridge_test_events.py         # EventBridge测试事件生成器
│   ├── test_report_analyzer.py            # 测试报告分析器
│   ├── run_tests.bat                      # Windows测试运行脚本
│   ├── run_tests.sh                       # Linux/Mac测试运行脚本
│   ├── README.md                          # 集成测试指导文档
│   ├── TESTING_README.md                  # 详细测试说明
│   └── examples/                          # 测试配置示例
│       └── test_config.json               # 测试配置文件示例
├── docs/
│   ├── dev/                                # 开发文档
│   │   └── service-name-sync-module-update-summary.md # 服务同步更新
│   ├── backups/                            # 文档备份
│   └── lambda_feedback_test/               # Lambda测试文档
├── lambda/
│   ├── dev/                                # 开发Lambda函数
│   │   └── aws_service_sync/               # AWS服务同步系统源代码
│   └── feedback_test/
│       ├── lianhe_mass_email_feedback_test.py # Lambda函数
│       └── lambda_test_event/              # 测试事件
├── tampermonkey/
│   ├── dev/                                # 开发脚本
│   ├── backups/                            # 脚本备份
│   └── feedback_test/
│       └── feedback_script.js              # 前端脚本
├── mass_email_outline/
│   ├── mass_email_requirements.md          # 业务需求
│   └── Mass-Email-*.pdf                   # 翻译指南
└── poc/
    ├── mass_email_rules.md                 # 翻译规则
    ├── 提示词v11.16代码版本.md              # LLM提示词规范
    └── various POC files                   # 开发迭代
```

## 开发指导原则

### 翻译功能开发指导
1. 始终考虑4阶段处理流水线
2. 实现技术内容的占位符保护
3. 使用v2数据库架构确保服务名称首次/后续追踪的准确性
4. 验证时区转换计算
5. 测试各种输入格式（元数据、措辞、一般内容）
6. 使用authoritative_full_name作为服务名称查找的业务主键
7. 利用base_name进行翻译逻辑状态追踪

### 反馈功能开发指导
1. **框架集成**: 使用统一的Tampermonkey框架进行内容检测和模块管理
2. 维护CORS安全性（仅允许issues.cn-northwest-1.amazonaws.cn）
3. 使用带时间戳和UUID的结构化S3文件路径
4. 在UI中提供详细的调试信息
5. 优雅地处理成功和错误情况
6. 彻底测试跨域请求
7. **页面内容检测**: 使用框架的多策略内容检测引擎
8. **动态内容处理**: 利用框架的MutationObserver支持
9. **性能优化**: 受益于框架内置的性能监控
10. **用户体验**: 使用框架的事件总线实现无缝模块协调

### AWS服务同步功能开发指导 ✅ **系统已完成**

#### 核心功能开发
1. ✅ 始终验证来自网页抓取和PDF解析的数据源
2. ✅ 实现网络故障和解析错误的健壮错误处理
3. ✅ 使用authoritative_full_name作为业务主键进行原子性数据库事务更新
4. ✅ 通过适当的规范化和去重维护数据完整性
5. ✅ 记录所有同步活动用于监控和调试
6. ✅ 处理速率限制并遵守robots.txt网页抓取规则
7. ✅ 在处理前验证PDF文件完整性
8. ✅ 使用BeautifulSoup和requests进行网页抓取
9. ✅ 使用pdfplumber进行PDF解析
10. ✅ 将处理后的数据存储到RDS PostgreSQL数据库的v2架构中
11. ✅ 实现适当的数据库连接池和错误处理
12. ✅ 使用AWS Secrets Manager管理数据库凭证
13. ✅ 实现全面的日志记录用于故障排查
14. ✅ 为每次同步运行创建详细的执行报告

#### 8种核心模式类型生成
15. ✅ **全称复合后缀模式** (优先级: 120) - 匹配完整服务名称 + 复杂后缀
16. ✅ **全称标准模式** (优先级: 115) - 精确匹配完整服务名称，带边界保护
17. ✅ **简称复合后缀模式** (优先级: 110) - 匹配简称 + 复杂后缀
18. ✅ **简称标准模式** (优先级: 105) - 精确匹配服务简称，带边界保护
19. ✅ **缩写复合后缀模式** (优先级: 100) - 匹配纯缩写 + 后缀
20. ✅ **缩写标准模式** (优先级: 95) - 精确匹配纯缩写，带边界保护
21. ✅ **特殊变体模式** (优先级: 125) - 处理特殊服务变体（如Aurora PostgreSQL）
22. ✅ **上下文保护模式** (优先级: 90) - 在特定上下文中避免误匹配

#### 边界保护机制
23. ✅ **ARN保护**: 使用负向前瞻避免ARN中的服务名匹配
24. ✅ **URL保护**: 避免URL路径中的服务名误匹配
25. ✅ **代码保护**: 防止代码块中的服务名被错误识别
26. ✅ **复合词智能处理**: 支持复杂后缀的精确捕获和保留

#### 特殊服务处理
27. ✅ **Aurora服务族**: 支持PostgreSQL、MySQL变体的专门处理
28. ✅ **Health服务族**: 区分Health Dashboard和通用Health服务
29. ✅ **RDS服务族**: 支持多种数据库引擎的特定模式生成
30. ✅ **上下文感知**: CLI命令和配置文件上下文的智能识别

#### 高级功能
31. ✅ **智能正则表达式模式优化和性能分析**
32. ✅ **自动化模式清理、归档和维护管理**
33. ✅ **全面的模式验证和冲突检测系统**
34. ✅ **完整的模式生命周期管理和备份恢复**
35. ✅ **完整的自动化部署系统和监控配置**

### AWS服务同步部署开发指导 ✅ **部署系统已完成**
1. ✅ **一键部署**: 使用 `deployment/lambda/dev/aws_service_sync/deploy.sh` 进行自动化部署
2. ✅ **多环境支持**: 支持dev/test/prod环境的独立部署和配置
3. ✅ **权限管理**: 自动创建最小权限的IAM角色和策略
4. ✅ **监控配置**: 使用 `monitor.sh` 自动配置8种CloudWatch告警和仪表板
5. ✅ **测试验证**: 使用 `test.sh` 进行函数调用、日志分析和指标检查
6. ✅ **配置示例**: 提供完整的环境变量、调度规则、告警配置示例
7. ✅ **故障排查**: 详细的部署问题诊断和解决方案
8. ✅ **安全配置**: 遵循AWS安全最佳实践的权限和网络配置
9. ✅ **成本优化**: 提供资源配置优化和成本监控建议
10. ✅ **维护更新**: 支持代码更新、配置变更和版本管理

### Tampermonkey框架开发指导
1. **内容检测**: 使用框架的统一内容检测引擎，支持多种策略
2. **模块开发**: 遵循框架的模块注册和生命周期模式
3. **事件通信**: 使用事件总线进行模块间通信
4. **性能优化**: 利用框架内置的性能监控和优化功能
5. **错误处理**: 使用框架的全面错误处理和恢复机制
6. **多脚本协调**: 使用命名空间管理进行脚本协调
7. **配置管理**: 遵循框架的配置管理模式
8. **测试调试**: 使用框架的调试和测试工具

### 集成测试开发指导 ✅ **测试系统已完成**
1. ✅ **测试套件**: 使用 `integration_testing/integration_tests.py` 进行完整的端到端测试
2. ✅ **本地测试**: 使用 `integration_testing/local_test_runner.py` 进行本地开发测试
3. ✅ **事件生成**: 使用 `integration_testing/eventbridge_test_events.py` 生成各种测试事件
4. ✅ **测试报告**: 使用 `integration_testing/test_report_analyzer.py` 生成详细测试报告
5. ✅ **跨平台支持**: 提供 `run_tests.bat` (Windows) 和 `run_tests.sh` (Linux/Mac) 脚本
6. ✅ **Mock服务**: 完整的Mock服务支持，包括Secrets Manager、S3、RDS等
7. ✅ **性能监控**: 内置性能监控和资源使用分析
8. ✅ **测试配置**: 使用 `examples/test_config.json` 进行灵活的测试配置
9. ✅ **错误场景**: 全面的错误场景测试和异常处理验证
10. ✅ **自动化报告**: 自动生成测试报告和性能分析结果

### 部署基础设施开发指导 ✅ **部署基础设施已完成**
1. ✅ **自动化部署**: 使用 `deployment/lambda/dev/aws_service_sync/deploy.sh` 一键部署
2. ✅ **环境管理**: 支持多环境部署配置和环境变量管理
3. ✅ **监控设置**: 使用 `monitor.sh` 自动配置CloudWatch监控和告警
4. ✅ **测试验证**: 使用 `test.sh` 进行部署后的功能验证
5. ✅ **配置示例**: 提供完整的配置文件示例和最佳实践
6. ✅ **权限管理**: 自动创建和配置必要的IAM角色和策略
7. ✅ **安全配置**: 遵循AWS安全最佳实践的配置管理
8. ✅ **故障排查**: 详细的部署问题诊断和解决方案
9. ✅ **版本管理**: 支持代码版本管理和回滚机制
10. ✅ **成本优化**: 提供资源配置优化建议

### 服务名称同步功能开发指导
1. 始终验证来自网页抓取和PDF解析的数据源
2. 实现网络故障和解析错误的健壮错误处理
3. 使用数据库事务进行原子性更新
4. 通过适当的规范化和去重维护数据完整性
5. 记录所有同步活动用于监控和调试
6. 处理速率限制并遵守robots.txt网页抓取规则
7. 在处理前验证PDF文件完整性
8. 使用BeautifulSoup和requests进行网页抓取
9. 使用pdfplumber进行PDF解析
10. 将处理后的数据存储到RDS PostgreSQL数据库
11. 实现适当的数据库连接池和错误处理
12. 使用AWS Secrets Manager管理数据库凭证
13. 实现全面的日志记录用于故障排查
14. 为每次同步运行创建详细的执行报告

### 开发环境使用指导
1. **开发阶段**: 使用 `dev/` 目录进行活跃开发
   - `docs/dev/`: 开发文档、设计草稿、技术笔记
   - `lambda/dev/`: 用于测试新功能的开发Lambda函数
   - `tampermonkey/dev/`: 用于功能迭代的开发用户脚本
2. **测试阶段**: 将稳定代码移至相应的测试目录
   - `lambda/feedback_test/`: 已测试的Lambda函数代码
   - `tampermonkey/feedback_test/`: 已测试的用户脚本
   - `docs/lambda_feedback_test/`: Lambda测试文档
3. **开发工作流**: 使用目录结构遵循 开发 → 测试 → 部署 模式

### 质量保证优先级
1. **占位符完整性**: 绝不修改 `__XXX_YYY__` 占位符
2. **时间准确性**: 验证时区转换计算
3. **链接可访问性**: 确保所有链接在中国区可用
4. **品牌一致性**: 正确应用AWS中国品牌术语
5. **安全性**: 验证CORS、IAM权限和输入清理

## 环境配置

### Lambda环境变量
- `S3_BUCKET_NAME`: 反馈存储桶
- `ALLOWED_ORIGIN`: CORS允许的来源 (https://issues.cn-northwest-1.amazonaws.cn)

### Tampermonkey配置
- 目标: `https://issues.cn-northwest-1.amazonaws.cn/issues/*`
- API端点: 在脚本CONFIG对象中配置
- 注入延迟: 页面加载后2秒

## 测试方法

### 翻译测试
- 独立测试每个阶段
- 验证占位符保护
- 检查跨作用域的服务名称追踪
- 验证各种格式的时区计算

### 反馈系统测试
- 测试 API Gateway → Lambda → S3 流程
- 验证CORS处理
- 测试错误场景和用户反馈
- 验证文件上传和存储结构