<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Mass Email 翻译流程</title>
    <style>
        body { font-family: Arial, sans-serif; }
        h1, h2, h3 { color: #333; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; }
        th { background-color: #f2f2f2; }
        code { font-family: 'Courier New', Courier, monospace; }
    </style>
</head>
<body>

<h1>Mass Email 翻译流程</h1>

<h2>1. 背景说明：</h2>
<p>现有Mass Message翻译流程中，PS翻译之后，Legal团队会进行Review，Review之后，CS会发出正式的Mass Email。由于Legal团队后期不再参与Mass Message翻译流程，为了保证PS翻译质量，PS内部需要定义新的翻译流程。该流程会在12月1日正式开始执行。</p>

<h2>2. 翻译流程：</h2>
<table>
    <thead>
    <tr>
        <th>CS handling</th>
        <th>PS translation</th>
        <th>PS Internal Review</th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td>
            Set next Step to “Pending on PS translation”<br>
            E2M update wording
        </td>
        <td>
            PP received, and find engineer to translation<br>
            Y Need N confirm with E2M<br>
            PS engineer update SIM and translate and Set<br>
            Set Next step to “Comment by requester”
        </td>
        <td>
            PS MoD Review, and Set Next Step to “Pending on CS internal review”
        </td>
    </tr>
    </tbody>
</table>

<p>1）CS 在需要PS翻译时，会将Next Step设置为“Pending on PS translation”，PP会收到SIM-Monitor提醒CN Mass Email翻译时，尽快找Group对应工程师进行翻译</p>

<p>例如：</p>
<p>
    Hi, primary, Folder: CN Mass Email<br>
    Issue Title: From E2M: Mass Communication Request for EC2 - GlobalChina<br>
    Edited Fields: Issue Modified<br>
    Issue link: <a href="https://issues.cn-northwest-1.amazonaws.cn/issues/03513ec0-59e8-4fb8-86ac-8e3112e1c602">https://issues.cn-northwest-1.amazonaws.cn/issues/03513ec0-59e8-4fb8-86ac-8e3112e1c602</a><br>
    请按照 Mass Email 翻译Group 找工程师进行翻译
</p>

<p>2）PP在SIM中记录关键字：“BJS pp check in, login@ will help on it .”</p>

<p>3）PS 工程师翻译：</p>
<ul>
    <li>
        **-Key** 需要和E2M确定英文wording内容: 在SIM中更新需要确认的内容，并将Next Step修改为“Comment by requester[”
    </li>
    <li>
        -  若不需要找E2M确认：在SIM中更新翻译的内容，并设置Next Step 为“Pending on PS internal review”，MoD会自动收到提醒。
    </li>
</ul>

<p>4）MoD进行Review：</p>
<ul>
    <li>
        -  MoD Review完成之后，在SIM中提供review之后的final 翻译版本，并修改Next Step 为“Pending on CS internal review”
    </li>
</ul>

<h3>注：</h3>
<p>若E2M修改了wording内容，CS需要PS继续翻译时，也会将Next Step设置为”Pending on PS translation”，PP也会收到提醒，PPI找之前翻译的工程师继续翻译即可。</p>

<h2>3. 翻译注意事项：</h2>
<p>保证翻译的准确性，例如是否存在歧义，翻译是否通顺，可理解。Legal提供的翻译审核要点，参考下面的qup：</p>

<h3>工作表1：Mass Email PS 审核要点</h3>
<p>下面是review PS 翻译总结的问题点：</p>

<table>
    <thead>
    <tr>
        <th>问题点</th>
        <th>示例</th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td>AWS没有修改</td>
        <td>
            1） AWS CLI → 正文中首次使用Amazon Command Line Interface (Amazon CLI)，后续使用Amazon CLI<br>
            2） AWS Support → 亚马逊云科技中国支持团队
        </td>
    </tr>
    <tr>
        <td>产品名称问题</td>
        <td>
            1） RDS → 正文中首次出现使用全称：Amazon Relational Database Service (RDS)，后续使用简称：Amazon RDS<br>
            2) EMR → 正文中首次出现使用全称：Amazon Elastic MapReduce (EMR)，后续使用简称：Amazon EMR<br>
            注：涉及全品全称时，如果在线版/regional product services table*和 *service names and other offerings*离线版不一致，应该以在线版regional product services table为准
        </td>
    </tr>
    <tr>
        <td>服务在中国区是否可用问题</td>
        <td>1） M6i R6i → 中国区没有</td>
    </tr>
    <tr>
        <td>文档链接问题</td>
        <td>
            1） 检查中国区是否有对应的文档链接<br>
            2） 确保链接直接跟内容相关且不出现youtube这类中国无法访问的连接
        </td>
    </tr>
    <tr>
        <td>固定翻译</td>
        <td>
            1） 若AWS字样出现在并产品名称的地方，需要翻译为“亚马逊云科技”，例如<br>
            AWS：“亚马逊云科技”<br>
            AWS account：“亚马逊云科技账户”<br>
            AWS console: 亚马逊云科技控制台<br>
            AWS Support: “亚马逊云科技和支持团队”<br>
            AWS Document: 亚马逊云科技文档<br>
            2）”Affected resources” 翻译为“受影响的资源”<br>
            3） 如果您有任何问题或疑虑，请联系亚马逊云科技中国支持团队<br>
            4）Support console链接： <a href="https://console.amazonaws.cn/support/">https://console.amazonaws.cn/support/</a>
        </td>
    </tr>
    <tr>
        <td>UTC时间没有转换为北京时间</td>
        <td>通知文件中的时间应表述为北京时间或UTC+8。</td>
    </tr>
    </tbody>
</table>

<h3>Mass Email 正文中给的CLI示例，如果涉及region参数的情 -  通知的Region是单个region的话，–region参数直接写通知的region</h3>

<ol>
    <li>
        "If you set or modify a role trust policy for a role associated with the GitHub IdP in your AWS account"<br>
        PS翻译为"如果您的亚马逊云科技账户中设置或修改角色的信任关系为 GitHub IdP ", -- "如果您设置或修改您亚马逊云科技账号中与GitHub IdP 关联的角色信任追踪"
    </li>
    <li>
        if the task is part of an ECS service [2], it will be stopped respecting the service's minimumHealthyPercent [3] value.<br>
        PS翻译机器人翻译：如果该任务是 ECS 服务的一部分 [2]，则该任务将停止遵守该服务的 minimumHealthyPercent [3] (值。<br>
        review之后的翻译：如果该任务是 Amazon ECS 服务的一部分 [2]，则任务将按照服务设置的 minimumHealthyPercent [3] 值进行停止。
    </li>
    <li>
        "A list is included at the end of this message with up to 100 of your most recently created EMR clusters identified as running EMR version 6.9.0 between August 29, 2022 and August 29, 2023.",<br>
        PS翻译为"本邮件末尾附有一份清单，列出了在 2022 年 8 月 29 日至 2023 年 8 月 29 日期间运行 EMR 6.9.0 版本的多达 100 个最新创建的 EMR 群集。",<br>
        修正后为此消息末尾提供了您在 (IBCGIQN)) 区域中最近创建的最多 100个Amazon EMR集群的列表，这些集群被识别为在 2022 年 8 月 29 日至 2023 年 8 月 29 日期间运行Amazon EMR 6.9.0 版本。*
    </li>
    <li>
        "我们建议将这些实例升级到较新的次要版本，以便受益于针对已知安全漏洞的补丁以及MariaDB社区添加的错误修复、性能改进和新功能。"<br>
        修正为 "我们建议将这些实例升级到较新的次要版本，从而受益于针对已知安全漏洞的补丁，以及错误修复、性能改进和 MariaDB 社区添加的新功能。"
    </li>
    <li>
        "将运行次要版本 10.3.35、10.4.25、10.4.26、10.5.16、10.5.17 和 10.6.8 的 MariaDB 数据库，以及从这些版本的快照恢复到 10.3.38、10.4.28、10.5.19 和 10.6.12 或是高版本的任何实例升级。"<br>
        修正为 "将运行次要版本 10.3.35、10.4.25、10.4.26、10.5.16、10.5.17 和 10.6.8 的 MariaDB 数据库，以及从这些版本的快照恢复的任何实例升级到 10.3.38、10.4.28、10.5.19 和 10.6.12 或是高版本"
    </li>
    <li>
        "创建新的资源分配并选择 CloudFormation 堆栈，不排除任何情况。" -- "创建新的资源分配并选择无排除的 CloudFormation 堆栈"
    </li>
    <li>
        "将运行次要版本 8.0.30 的 MySQL 数据库以及从这些版本的快照恢复到 8.0.32 或是高版本的所有实例进行升级。" -- " 将运行次要版本 8.0.30 的 MySQL 数据库以及从这些版本的快照恢复的任何实例升级到 8.0.32 或是高版本。"
    </li>
    <li>
        "这是由于授权实施不正确，" -> "这是由于授权实施不正确导致的"
    </li>
    <li>
        "You have up to 10 months to upgrade your Elastic Beanstalk environments configured on platform branches running on the Windows Server 2012 R2 operating system."<br>
        翻译为"您最多有 10 个月的时间来升级在 Windows Server 2012 R2 操作系统上运行的平台分支上配置的 Elastic Beanstalk 环境。",<br>
        调整之后为"您最多有 10 个月的时间来升级配置在运行 Windows Server 2012 R2 操作系统的平台分支的Amazon Elastic Beanstalk 环境 "
    </li>
    <li>
        "这些部署最迟将于 2023 年 12 月 31 日完成。此更新将取消在所有region的所有API中使用 TLS 1.0 和 1.1 版本的功能。" -- "这些部署最迟将于 2023 年 12 月 31 日之前完成。此更新取消了在所有亚马逊云科技区域将TLS 1.0 和 1.1 版本和API一起使用的功能。"
    </li>
</ol>

<h3>1) Legal review 7 Amazon SageMaker automatically updates all endpoints that are currently in-service to the latest and secure software. 从客户理解层面认为需要修改为 -- Amazon SageMaker automatically updates the software on all endpoints that are currently in-service to the latest and secure software.</h3>

<h3>2) Legal修改了产品名称（Amazon Aurora – MySQL 修改为 Amazon Aurora – MySQL-compatible ）</h3>

<h3>3) 针对翻译中的"从今天起" 的字眼需要确认是否有明确的日期</h3>

<h3>长语句翻译存在歧义</h3>

<h3>长语句翻译不通顺以及语法问题</h3>

<h3>需要确认翻译的wording是否对客户是否易理解的</h3>

<h2>4. CN Mass Email Sample Repository</h2>
<p><a href="https://westernclouddata.awsapps.com/workdocs/index.html#/folder/c728b65a4aa3a151553c155b3b25678942bb43f6e204956c76ac94627dc977a0">https://westernclouddata.awsapps.com/workdocs/index.html#/folder/c728b65a4aa3a151553c155b3b25678942bb43f6e204956c76ac94627dc977a0</a></p>

<h2>Max Email Sample Regulatory</h2>

<table>
    <thead>
    <tr>
        <th style="width: 50px;"></th>
        <th>名称</th>
        <th>上次确定时间</th>
        <th>大小</th>
        <th>类型</th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td>☐ ☆</td>
        <td>Database Profile</td>
        <td>今天下午2:36</td>
        <td>525.5 KB</td>
        <td>文件夹</td>
    </tr>
    <tr>
        <td>☐ ☆</td>
        <td>Linux Profile</td>
        <td>今天下午3:31</td>
        <td>71 KB</td>
        <td>文件夹</td>
    </tr>
    <tr>
        <td>☐ ☆</td>
        <td>XCD Profile</td>
        <td>今天下午1:55</td>
        <td>0 B</td>
        <td>文件夹</td>
    </tr>
    <tr>
        <td>☐ ☆</td>
        <td>ASIA Profile</td>
        <td>今天下午1:64</td>
        <td>0 B</td>
        <td>文件夹</td>
    </tr>
    <tr>
        <td>☐ ☆</td>
        <td>Windows Profile</td>
        <td>今天下午1:54</td>
        <td>0 B</td>
        <td>文件夹</td>
    </tr>
    <tr>
        <td>☐ ☆</td>
        <td>DSP Profile</td>
        <td>今天下午1:54</td>
        <td>0 B</td>
        <td>文件夹</td>
    </tr>
    <tr>
        <td>☐ ☆</td>
        <td>Networking&User Profile</td>
        <td>今天下午1:44</td>
        <td>0 B</td>
        <td>文件夹</td>
    </tr>
    <tr>
        <td>☐ ☆</td>
        <td>DNS Profile</td>
        <td>今天下午1:53</td>
        <td>0 B</td>
        <td>文件夹</td>
    </tr>
    <tr>
        <td>☐ ☆</td>
        <td>BigData Profile</td>
        <td>今天下午1:53</td>
        <td>0 B</td>
        <td>文件夹</td>
    </tr>
    </tbody>
</table>

</body>
</html>